import api from './api'
export default {
  list: postData => api.fetchBaseData('/pro/hxc/promoneyawards/list.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/promoneyawards/batch.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/promoneyawards/add.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/promoneyawards/delete.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/promoneyawards/update.htm', postData),
}
