<template>
  <hi-wall-set-right class="wall-piclottery-edit-box" v-loading="loadPage">
    <div slot="content" v-if="!loadPage">
      <el-form :model="nowActConfig" :rules="rules" ref="form" label-width="120px" :disabled="disabled">
        <hi-collapse title="基础设置">
          <el-form-item label="奖项名称：" prop="title">
            <el-input class="w348" v-model.trim="nowActConfig.title" placeholder="请输入奖项名称" maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="规则：" prop="rule">
            <el-input
              class="w348"
              v-model="nowActConfig.rule"
              type="textarea"
              :rows="3"
              placeholder="请输入规则"
              show-word-limit
              maxlength="500"
            ></el-input>
          </el-form-item>
          <el-form-item label="轮次开始后：" prop="ingJoinSwitch">
            <el-radio-group v-model="nowActConfig.ingJoinSwitch">
              <el-radio label="Y">允许参与</el-radio>
              <el-radio label="N">禁止参与</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="提交：" prop="submitType">
            <el-radio-group v-model="nowActConfig.submitType">
              <el-radio label="MANUAL_SUBMIT">手动提交</el-radio>
              <el-radio label="AUTO_SUBMIT"
                >自动提交
                <el-tooltip
                  effect="dark"
                  content="移动端选择答案后自动提交，多选题时选择错误答案直接判错，仅支持单选/多选类型题目"
                  placement="top-start"
                >
                  <i class="el-icon-info mrg-l-5"></i>
                </el-tooltip>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="看题时间：" prop="watchTime">
            <div class="flex flex-a-c">
              <span>每题开始</span>
              <el-input-number
                class="w-70 mrg-l-10 mrg-r-10"
                v-model.number="nowActConfig.watchTime"
                :min="0"
                :max="10000"
                :precision="0"
                :controls="false"
              ></el-input-number>
              <span>秒后可答题</span>
            </div>
          </el-form-item>
          <el-form-item label="答题时间：" prop="answerTime">
            <!-- 单选按钮组 -->
            <el-radio-group v-model="nowActConfig.answerTimeType" class="mrg-b-15">
              <el-radio label="UNIFIED_TIME">所有题目统一答题时间</el-radio>
              <el-radio label="INDIVIDUAL_TIME">每道题单独设置答题时间</el-radio>
            </el-radio-group>
            <!-- 条件显示的时间设置 -->
            <div v-if="nowActConfig.answerTimeType === 'UNIFIED_TIME'" class="flex flex-a-c">
              <el-input-number
                class="w-70 mrg-r-10"
                v-model.number="nowActConfig.answerTime"
                :min="1"
                :max="10000"
                :precision="0"
                :controls="false"
              ></el-input-number>
              <span>秒</span>
            </div>
          </el-form-item>
          <el-form-item label="答案展示：" prop="showAnswerType">
            <!-- 单选按钮组 -->
            <el-radio-group v-model="nowActConfig.showAnswerType" class="mrg-b-15" @change="handleShowAnswerTypeChange">
              <el-radio label="MANUAL_ANSWER"
                >手动控制展示时间
                <el-tooltip effect="dark" content="每题答完固定显示本题答案，主办方手动切换到下一题" placement="top-start">
                  <i class="el-icon-info mrg-l-5"></i>
                </el-tooltip>
              </el-radio>
              <el-radio label="TIMED_ANSWER"
                >设置展示时间
                <el-tooltip
                  effect="dark"
                  content="每题答完根据设置的时长显示答案，时长结束后自动切换下一题，设置为0时不显示答案"
                  placement="top-start"
                >
                  <i class="el-icon-info mrg-l-5"></i>
                </el-tooltip>
              </el-radio>
            </el-radio-group>

            <!-- 条件显示的时间设置 -->
            <div v-if="nowActConfig.showAnswerType === 'TIMED_ANSWER'" class="flex flex-a-c">
              <el-input-number
                class="w-70 mrg-r-10"
                v-model.number="nowActConfig.showAnswerTime"
                :min="0"
                :max="10000"
                :precision="0"
                :controls="false"
              ></el-input-number>
              <span>秒</span>
            </div>
          </el-form-item>
          <!-- 每题得分 -->
          <el-form-item label="每题得分：" prop="showResultType">
            <el-radio-group v-model="nowActConfig.showResultType">
              <el-radio v-if="nowActConfig.showAnswerType === 'TIMED_ANSWER'" label="HIDE_ANSWER">不展示</el-radio>
              <el-radio v-if="nowActConfig.showAnswerType === 'TIMED_ANSWER'" label="END_SHOW_ANSWER"
                >每题结束展示
                <el-tooltip effect="dark" content="每题结束后单独页面展示本题答题情况，可设置时长自动关闭" placement="top-start">
                  <i class="el-icon-info mrg-l-5"></i>
                </el-tooltip>
              </el-radio>
              <el-radio v-if="nowActConfig.showAnswerType === 'MANUAL_ANSWER'" label="MANUAL_SHOW">手动控制展示</el-radio>
            </el-radio-group>
            <div v-if="nowActConfig.showResultType === 'END_SHOW_ANSWER'" class="flex flex-a-c">
              <span>展示时间</span>
              <el-input-number
                class="w-70 mrg-l-10"
                v-model.number="nowActConfig.showResultTime"
                :min="1"
                :max="10000"
                :precision="0"
                :controls="false"
              ></el-input-number>
              <span>秒</span>
            </div>
          </el-form-item>
          <!-- 注释展示 -->
          <el-form-item label="注释展示：" prop="showCommentSwitch">
            <el-radio-group v-model="nowActConfig.showCommentSwitch">
              <el-radio label="N">不展示</el-radio>
              <el-radio label="Y">每题结束展示</el-radio>
            </el-radio-group>
          </el-form-item>
        </hi-collapse>
        <hi-collapse title="题目设置">
          <el-form-item label="选择题目：" prop="chooseSubjectType">
            <el-radio-group v-model="nowActConfig.chooseSubjectType">
              <el-radio label="FIXED">固定题目</el-radio>
              <el-radio label="RANDOM">题库随机</el-radio>
            </el-radio-group>
          </el-form-item>
          <div class="question-box">
            <hi-question-table
              ref="questionTableRef"
              :actConfig="nowActConfig"
              @updateSubjectContent="handleUpdateSubjectContent"
              :disabled="disabled"
            ></hi-question-table>
          </div>
          <el-form-item label="分值：" prop="scoreType">
            <el-radio-group v-model="nowActConfig.scoreType">
              <el-radio label="TOTAL"
                >所有题目统一分值
                <el-tooltip effect="dark" content="参与者每答错一道题，扣除设置的固定分数" placement="top-start">
                  <i class="el-icon-info mrg-l-5"></i>
                </el-tooltip>
              </el-radio>
              <el-radio label="SINGLE">
                每道题单独设置分数
                <el-tooltip effect="dark" content="根据题目的难度，扣除对应分数" placement="top-start">
                  <i class="el-icon-info mrg-l-5"></i>
                </el-tooltip>
              </el-radio>
            </el-radio-group>
            <div v-if="nowActConfig.scoreType === 'TOTAL'" class="flex flex-a-c">
              <span>每题</span>
              <el-input-number
                class="w-70 mrg-l-10 mrg-r-10"
                v-model.number="nowActConfig.score"
                :min="1"
                :max="10000"
                :precision="0"
                :controls="false"
              ></el-input-number>
              <span>分</span>
            </div>
          </el-form-item>

          <!-- 连对加分设置 -->
          <el-form-item label="连对加分：">
            <div class="flex flex-a-c mrg-b-10">
              <span>连续答对</span>
              <el-input-number
                class="w-70 mrg-l-10 mrg-r-10"
                v-model.number="nowActConfig.rightPlusCnt"
                :min="0"
                :max="100"
                :precision="0"
                :controls="false"
              ></el-input-number>
              <span>题</span>

              <span class="mrg-l-20">额外增加</span>
              <el-input-number
                class="w-70 mrg-l-10 mrg-r-10"
                v-model.number="nowActConfig.rightPlusScore"
                :min="0"
                :max="10000"
                :precision="0"
                :controls="false"
              ></el-input-number>
              <span>分</span>
            </div>
          </el-form-item>

          <!-- 获胜加分设置 -->
          <el-form-item label="获胜加分：">
            <div class="flex flex-a-c mrg-b-10">
              <span>前</span>
              <el-input-number
                class="w-70 mrg-l-10 mrg-r-10"
                v-model.number="nowActConfig.winPlusRank"
                :min="0"
                :max="1000"
                :precision="0"
                :controls="false"
              ></el-input-number>
              <span>名</span>

              <span class="mrg-l-20">额外增加</span>
              <el-input-number
                class="w-70 mrg-l-10 mrg-r-10"
                v-model.number="nowActConfig.winPlusScore"
                :min="0"
                :max="10000"
                :precision="0"
                :controls="false"
              ></el-input-number>
              <span>分</span>
            </div>
          </el-form-item>

          <!-- 答错扣分设置 -->
          <el-form-item label="答错扣分：">
            <el-radio-group v-model="nowActConfig.wrongScoreType" class="mrg-b-15" @change="handleWrongScoreTypeChange">
              <el-radio label="TOTAL">所有题目统一扣分</el-radio>
              <el-radio label="SINGLE">每道题单独设置扣分</el-radio>
            </el-radio-group>
            <div v-if="nowActConfig.wrongScoreType === 'TOTAL'" class="flex flex-a-c">
              <span>每题答错扣</span>
              <el-input-number
                class="w-70 mrg-l-10 mrg-r-10"
                v-model.number="nowActConfig.wrongScore"
                :min="0"
                :max="10000"
                :precision="0"
                :controls="false"
              ></el-input-number>
              <span>分</span>
            </div>
          </el-form-item>
          <!-- 淘汰机制设置 -->
          <el-form-item label="答错淘汰：">
            <div class="flex flex-a-c">
              <hi-switch
                active-value="Y"
                inactive-value="N"
                active-text="开启"
                inactive-text="关闭"
                v-model="nowActConfig.outSwitch"
                @change="handleOutSwitchChange"
              ></hi-switch>
              <span class="mrg-l-10 sub">全部参与人员淘汰时轮次自动结束</span>
            </div>

            <div v-if="nowActConfig.outSwitch === 'Y'">
              <div class="flex flex-a-c mrg-b-10">
                <span>答错</span>
                <el-input-number
                  class="w-70 mrg-l-10 mrg-r-10"
                  v-model.number="nowActConfig.outWrongCnt"
                  :min="1"
                  :max="100"
                  :precision="0"
                  :controls="false"
                ></el-input-number>
                <span>题淘汰</span>
              </div>
              <div class="flex flex-a-c mrg-b-10">
                <span>淘汰范围</span>
                <div class="mrg-l-10">
                  <el-radio-group v-model="nowActConfig.outRange">
                    <el-radio label="CURRENT_ROUND"
                      >本轮次淘汰
                      <el-tooltip effect="dark" content="被淘汰人员无法继续参与本轮次" placement="top-start">
                        <i class="el-icon-info mrg-l-5"></i>
                      </el-tooltip>
                    </el-radio>
                    <el-radio label="ALL_ROUND"
                      >所有轮次淘汰
                      <el-tooltip effect="dark" content="被淘汰人员无法继续参与本场活动所有轮次答题" placement="top-start">
                        <i class="el-icon-info mrg-l-5"></i>
                      </el-tooltip>
                    </el-radio>
                  </el-radio-group>
                </div>
              </div>
              <div class="flex flex-a-c">
                <span>淘汰人员排名</span>
                <div class="mrg-l-10">
                  <el-radio-group v-model="nowActConfig.outRankSwitch">
                    <el-radio label="N"
                      >不计入排名
                      <el-tooltip effect="dark" content="被淘汰人员无法获得本场轮次的排名，即无法通过排名获得奖品" placement="top-start">
                        <i class="el-icon-info mrg-l-5"></i>
                      </el-tooltip>
                    </el-radio>
                    <el-radio label="Y"
                      >计入排名
                      <el-tooltip effect="dark" content="被淘汰人员根据积分及答题时间计入排名，可通过排名获得奖品" placement="top-start">
                        <i class="el-icon-info mrg-l-5"></i>
                      </el-tooltip>
                    </el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>
          </el-form-item>
        </hi-collapse>
        <div class="relative">
          <hi-collapse title="高级设置">
            <div class="relative pd-10">
              <el-form-item label="参与方式：" prop="joinType">
                <el-radio-group :value="nowActConfig.joinType" @input="handleJoinTypeChange">
                  <el-radio label="PERSONAL">个人参与</el-radio>
                  <el-radio label="TEAM">团队参与</el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 团队列表 -->
              <hi-team-list
                v-if="nowActConfig.joinType === 'TEAM'"
                v-model="teamList"
                :actConfig="nowActConfig"
                :disabled="disabled"
                :wall="wall"
              ></hi-team-list>

              <!-- 奖品列表 -->
              <hi-prize
                class="mrg-t-20"
                ref="prizeRef"
                :isTeam="nowActConfig.joinType === 'TEAM'"
                :actConfig="nowActConfig"
                :disabled="isEnd || disabled"
                @create="(cb) => cb(save)"
                @awardListChange="onAwardListChange"
              ></hi-prize>
            </div>
          </hi-collapse>

          <hi-access-rules :config="config" :actConfig="nowActConfig" :disabled="disabled" @create="(cb) => cb(save)"></hi-access-rules>

          <hi-unlock placement="middle" vatKey="answerraceAdvancedLimit" :actInfo="config" @readConfig="$emit('updateConfig')"></hi-unlock>
        </div>

        <!-- 参与条件限制 -->
      </el-form>
    </div>
    <div slot="control" class="control">
      <el-button plain @click="backList" :disabled="false">返回</el-button>
      <el-button type="primary" :disabled="saveDisabled" @click="save('NORMAL')">保存</el-button>
    </div>
  </hi-wall-set-right>
</template>
<script>
import api from '@/api'
import { Hi } from '@/libs/common'
import HiAccessRules from '@/views/wall/interact/common/accessRules/index.vue'
import HiPrize from '@/views/wall/interact/common/prize/index.vue'
import { mapState } from 'vuex'
import HiQuestionTable from './questionTable.vue'
import HiTeamList from './teamList.vue'

//  随机题库
// const randomSubject = []

const defaultAct = {
  title: '答题轮次', //奖项名称
  rule: '', //规则
  joinType: 'PERSONAL', //参与方式
  ingJoinSwitch: 'Y', //轮次开始后参与开关
  submitType: 'MANUAL_SUBMIT', //提交
  watchTime: 0, //看题时间
  answerTimeType: 'UNIFIED_TIME', //答题时间设置方式
  answerTime: 10, //统一答题时间
  showAnswerType: 'MANUAL_ANSWER', //答案展示方式
  showAnswerTime: 10, //答案展示时间
  showResultType: 'MANUAL_SHOW', //每题得分展示方式
  showResultTime: 10, //每题得分展示时间
  showCommentSwitch: 'N', //注释展示开关
  chooseSubjectType: 'FIXED', //选择题目方式
  subjectContent: [], //随机题目规则
  scoreType: 'TOTAL', //分值计算方式
  score: 10, //统一分值
  rightPlusCnt: 3, //连对题目个数
  rightPlusScore: 0, //连对加分分值
  winPlusRank: 1, //获胜加分排名
  winPlusScore: 0, //获胜加分分值
  wrongScoreType: 'TOTAL', //答错扣分方式
  wrongScore: 0, //答错统一扣分
  outSwitch: 'N', //淘汰开关
  outWrongCnt: 1, //淘汰答错个数
  outRange: 'CURRENT_ROUND', //淘汰范围
  outRankSwitch: 'N', //淘汰人员是否参与排名
}
export default {
  name: 'wall-answerracev3-edit',
  inject: ['wall'],
  props: {
    editId: {
      type: Number,
      default: 0,
    },
    themeId: {
      type: [String, Number],
      default: '',
    },
    config: {
      default: () => ({}),
    },
  },
  components: {
    HiPrize,
    HiQuestionTable,
    HiTeamList,
    HiAccessRules,
  },
  data() {
    return {
      loadPage: true,
      actConfig: {}, //活动配置
      nowActConfig: {}, //当前活动配置
      awardList: [], //奖品列表
      teamList: [], //团队列表
      rules: {
        title: [{ required: true, message: '奖项名称不能为空', trigger: 'blur' }],
      },
    }
  },
  computed: {
    ...mapState('questionBank', ['subjectDifficultyList']),
    disabled() {
      return this.nowActConfig.id && this.nowActConfig.state !== 'NOT_STARTED'
    },
    hasChanges() {
      const d1 = Hi.Object.copy(this.actConfig)
      const d2 = Hi.Object.copy(this.nowActConfig)
      if (d1.subjectContent) {
        d1.subjectContent = JSON.stringify(d1.subjectContent)
      }
      if (d2.subjectContent) {
        d2.subjectContent = JSON.stringify(d2.subjectContent)
      }
      return this.nowActConfig && Object.keys(this.nowActConfig).length > 0 && !!Hi.Object.difference(d1, d2, { deep: true })
    },
    defaultChange() {
      return Hi.Object.difference(defaultAct, this.nowActConfig, {
        ignoreKey: ['updateDate'],
      })
    },
    saveDisabled() {
      return this.nowActConfig.id && !this.hasChanges
    },
    isEnd() {
      return this.nowActConfig.id && this.nowActConfig.count > 0
    },
    _countMax() {
      return this.awardList.length ? this.awardList.reduce((total, award) => total + award.count, 0) : Infinity
    },
    // 是否有红包奖品
    hasRedPacket() {
      return this.awardList.some((item) => item.type === 'REDPACK')
    },
  },
  watch: {
    actConfig: {
      handler(config) {
        if (config.id) {
          Object.keys(config).forEach((key) => {
            this.$set(this.nowActConfig, key, Hi.Object.copy(config[key]))
          })
        } else {
          this.nowActConfig = Hi.Object.copy(defaultAct)
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleUpdateSubjectContent(dataList) {
      this.nowActConfig.subjectContent = dataList
    },

    // 获取答题轮次
    async fetchAnswerrace(id) {
      const data = await api.answerracev3.read({
        where: {
          wallId: this.wall.id,
          id: id || this.editId || this.nowActConfig.id,
        },
      })
      if (data.subjectContent) {
        data.subjectContent = JSON.parse(data.subjectContent)
      }
      this.actConfig = data
    },

    // 答案展示方式
    handleShowAnswerTypeChange(val) {
      if (val === 'MANUAL_ANSWER') {
        this.nowActConfig.showResultType = 'MANUAL_SHOW'
      } else {
        this.nowActConfig.showResultType = 'HIDE_ANSWER'
      }
    },

    // 处理答错扣分方式变化
    handleWrongScoreTypeChange(val) {
      if (val === 'TOTAL') {
        this.nowActConfig.wrongScore = 0
      }
    },

    // 处理淘汰开关变化
    handleOutSwitchChange(val) {
      if (val === 'N') {
        this.nowActConfig.outWrongCnt = 0
        this.nowActConfig.outRange = 'CURRENT_ROUND'
        this.nowActConfig.outRankSwitch = 'N'
      } else {
        this.nowActConfig.outWrongCnt = 3 // 默认答错3题淘汰
      }
    },

    // 处理参与方式变化
    async handleJoinTypeChange(val) {
      if (this.awardList && this.awardList.length > 0) {
        // 有红包奖品时不允许切换
        if (this.hasRedPacket) {
          this.$notify.error({ title: '错误', message: '已设置红包奖品，无法切换参与方式' })
          return
        }

        try {
          await this.$confirm('切换参与方式会清空当前已创建的奖品，确认切换？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })

          await this.$refs.prizeRef?.clearAwardList?.()
          this.nowActConfig.joinType = val
        } catch (error) {
          if (error !== 'cancel') {
            this.$notify.error({ title: '错误', message: error?.msg || '切换参与方式失败' })
          }
        }
        return
      }

      this.nowActConfig.joinType = val
    },

    // 奖品列表变化
    onAwardListChange({ awardList }) {
      this.awardList = Hi.Object.copy(awardList)
    },

    // 创建答题轮次
    async _insertAnswerrace() {
      const submitData = {
        wallId: this.wall.id,
        themeId: this.themeId,
        ...this.nowActConfig,
      }
      if (submitData.subjectContent) {
        submitData.subjectContent = JSON.stringify(submitData.subjectContent)
      }
      const data = await api.answerracev3.add(submitData)
      this.$notify.success({ title: '成功', message: '活动创建成功' })
      return data
    },

    // 更新答题轮次
    async _updateAnswerrace() {
      const toUpdate = (value) => {
        const data = Hi.Object.copy(value)
        if (data.subjectContent) {
          data.subjectContent = JSON.stringify(data.subjectContent)
        }
        return data
      }

      const update = Hi.Object.difference(toUpdate(this.actConfig), toUpdate(this.nowActConfig), { deep: true })
      if (update) {
        await api.answerracev3.update({
          where: { id: this.nowActConfig.id, wallId: this.config.wallId },
          update: update,
        })
        this.$notify.success({ title: '成功', message: '保存成功' })
      }
    },

    // 保存
    async save() {
      try {
        await this.$refs.form.validate()
        if (this.nowActConfig.chooseSubjectType === 'FIXED' && this.$refs.questionTableRef?.quesAndOptArr.length === 0) {
          this.$notify.error({ title: '错误', message: '请先添加题目' })
          return
        }

        if (this.nowActConfig.chooseSubjectType === 'RANDOM' && this.nowActConfig.subjectContent.length === 0) {
          this.$notify.error({ title: '错误', message: '请先添加题目' })
          return
        }

        if (this.nowActConfig.id) {
          await this._updateAnswerrace()
          await this.fetchAnswerrace()
        } else {
          const id = await this._insertAnswerrace()
          await this.fetchAnswerrace(id)
        }
      } catch (error) {
        console.error('保存失败:', error)
        if (error === false) return false
        if (error !== 'cancel') {
          this.$notify.error({ title: '错误', message: error?.msg || '提交数据出错' })
        }
      }
    },

    //返回列表
    async backList() {
      const hasChanges = this.actConfig.id ? this.hasChanges : this.defaultChange
      if (!hasChanges) {
        this.$emit('list')
        return
      }

      try {
        await this.$confirm('是否保存您所做的更改？', '提示', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        await this.save('NORMAL')
        this.$emit('list')
      } catch (error) {
        if (error === 'cancel') {
          this.$emit('list')
        }
      }
    },
  },
  async mounted() {
    this.$emit('loaded')
    if (this.editId) {
      await this.fetchAnswerrace(this.editId)
    }
    this.loadPage = false
  },
}
</script>
<style scoped lang="scss">
.w348 {
  width: 348px;
}

.sub {
  font-size: 12px;
  color: #999;
}

.question-box {
  margin: 0 0 20px 140px;
}
</style>
