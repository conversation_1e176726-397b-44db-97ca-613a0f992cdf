import api from './api'
export default {
  list: postData => api.fetchBaseData('/pro/hxc/proresultctrl/list.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/proresultctrl/add.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/proresultctrl/delete.htm', postData),
  batchupdate: postData => api.fetchBaseData('/pro/hxc/proresultctrl/batchupdate.htm', postData),
  modulelist: postData => api.fetchBaseData('/pro/hxc/proresultctrl/modulelist.htm', postData),
  ndagain: postData => api.fetchBaseData('/pro/hxc/proresultctrl/ndagain.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proresultctrl/update.htm', postData),
}