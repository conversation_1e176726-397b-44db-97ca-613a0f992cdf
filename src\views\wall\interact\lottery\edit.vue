<template>
  <hi-wall-set-right class="wall-reward-edit-box" v-if="loadPage">
    <div>
      <el-form :model="nowLotteryAward" :rules="rules" ref="form" label-width="150px">
        <hi-collapse title="基础设置">
          <el-form-item label="奖项名称：" prop="title">
            <el-input class="w348" v-model.trim="nowLotteryAward.title" placeholder="请输入奖项名称" maxlength="20"></el-input>
            <div class="mrg-l-5" style="display: inline-block">
              <el-checkbox v-model="nowLotteryAward.hideTitleSwitch" :true-label="'Y'" :false-label="'N'">大屏幕不展示标题</el-checkbox>
            </div>
          </el-form-item>
        </hi-collapse>
        <hi-collapse title="奖品设置">
          <el-form-item label="抽奖模式：">
            <el-radio-group :value="nowLotteryAward.singlePrizeSwitch" :disabled="limitDisable" @input="radioChange">
              <el-radio label="Y">单轮单奖品</el-radio>
              <el-radio label="N">单轮多奖品</el-radio>
            </el-radio-group>
            <div class="sub" style="margin-top: -10px">
              <div>一轮仅可抽取一种奖品</div>
              <div>一轮可一次性抽取多种奖品</div>
            </div>
          </el-form-item>
          <el-form-item v-if="nowLotteryAward.singlePrizeSwitch === 'N'" label="单次抽出数量：">
            <div class="sub">点击"抽奖按钮"一次，抽出的中奖人数量</div>
            <el-radio-group v-model="nowLotteryAward.extractType" :disabled="limitDisable">
              <div class="flex flex-d-c">
                <el-radio label="TOTAL" class="mrg-t-10"
                  >统一设置总数量，随机分配奖品
                  <el-tooltip effect="dark" content="实时显示签名数量" placement="top-start">
                    <template slot="content">
                      <p>奖品随机分配给中奖人，每种奖品中奖人数量不同</p>
                      <p>仅保证奖品数之和等于设置值</p>
                      <p>适用于： 相同价值奖项一次性抽出，随机分配给中奖人。</p>
                    </template>
                    <img class="doubt mrg-r-10" src="~@/assets/wall/interact/ask.png" />
                  </el-tooltip>
                </el-radio>
                <el-radio label="SINGLE" class="mrg-t-20"
                  >按奖品分别设置数量，固定分配奖品
                  <el-tooltip effect="dark" content="实时显示签名数量" placement="top-start">
                    <template slot="content">
                      <p>每种奖品抽出的中奖人数量固定</p>
                      <p>适用于：不同级别奖项一次性抽出</p>
                    </template>
                    <img class="doubt mrg-r-10" src="~@/assets/wall/interact/ask.png" />
                  </el-tooltip>
                </el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <hi-prize
            :actConfig="nowLotteryAward"
            :disabled="limitDisable"
            @create="(cb) => cb(save, 'NORMAL')"
            @awardListChange="awardListChange"
          ></hi-prize>
          <el-form-item
            v-if="nowLotteryAward.singlePrizeSwitch === 'Y' || (nowLotteryAward.extractType === 'TOTAL' && _countMax)"
            class="mrg-t-20"
            label="单次抽出："
          >
            <el-input-number
              controls-position="right"
              class="w100 mr-10"
              v-model="nowLotteryAward.onceNum"
              :min="1"
              :precision="0"
              :max="Number(_countMax)"
              placeholder=""
            ></el-input-number>
            <span>人</span>
          </el-form-item>
        </hi-collapse>
      </el-form>
      <hi-collapse title="高级设置">
        <div class="relative py-20px">
          <el-form label-width="170px">
            <el-form-item label="允许重复中奖">
              <div class="flex flex-a-c" style="height: 32px">
                <hi-switch active-value="Y" inactive-value="N" active-text="开启" inactive-text="关闭" v-model="nowLotteryAward.repeatwinSwitch">
                </hi-switch>
                <el-tooltip effect="dark" content="开启后，其他轮次中过奖的人有机会再中，但本轮次中仅可被抽中一次" placement="top-start">
                  <img class="doubt" src="~@/assets/wall/interact/doubt.png" />
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="参与条件限制">
              <p class="f999 mrg-b-5 font-12">选择分组，分组内人员按照设置参与互动</p>
              <hi-limit :wall="wall" :config="config" :actConfig="nowLotteryAward" @configChange="configChange"> </hi-limit>
            </el-form-item>
            <el-form-item label="大屏幕抽奖参与人数展示">
              <hi-switch active-value="Y" inactive-value="N" active-text="开启" inactive-text="关闭" v-model="nowLotteryAward.showLotteryNum">
              </hi-switch>
            </el-form-item>
          </el-form>
          <hi-unlock vatKey="lotteryAdvancedLimit" :actInfo="config" placement="middle" @readConfig="$emit('update')"></hi-unlock>
        </div>
      </hi-collapse>
    </div>
    <div slot="control" class="control">
      <el-button plain @click="backList" :disabled="false">返回</el-button>
      <el-button type="primary" :disabled="btnDisabled || isEdit" @click="save('NORMAL')">保存</el-button>
    </div>
  </hi-wall-set-right>
</template>
<script>
import api from '@/api'
import { Hi } from '@/libs/common'
import HiPrize from '@/views/wall/interact/common/prize/index.vue'
import HiClaim from '../common/claim.vue'
import HiLimit from '../common/limit.vue'

export default {
  name: 'wall-lottery-edit',
  props: {
    editId: Number,
    prizeType: String,
    themeType: String,
    config: {
      default: () => ({}),
    },
    wall: {
      default: () => ({}),
    },
  },
  components: {
    HiLimit,
    HiClaim,
    HiPrize,
  },
  data() {
    return {
      then: null,
      showClaim: false,
      prizeNameFocusFlag: false, // 奖品名称选中标记
      actComp: 'HiTheme',
      rules: {
        title: [{ required: true, message: '奖项名称不能为空', trigger: 'blur' }],
        onceNum: [
          { required: true, message: '单次抽奖人数不能为空', trigger: 'blur' },
          { required: true, message: '单次抽奖人数不能为空', trigger: 'change' },
        ],
      },
      awardChangeObj: null,
      //奖品配置
      lotteryAward: {
        title: '滚动抽奖',
        onceNum: 1,
        repeatwinSwitch: 'N',
        extractType: 'TOTAL',
        themeType: this.themeType,
        showLotteryNum: 'Y',
        limitType: 'nolimit',
        hiddenLotteryNum: 'N',
        singlePrizeSwitch: 'Y',
      },
      nowLotteryAward: {},
      editData: {},
      ableGroup: [],
      nowAbleGroup: [],
      awardsId: '', //奖品id
      updateClaim: null, //兑奖方式更新内容
      loadPage: false,
      awardList: [],
    }
  },
  computed: {
    themeBan() {
      //中奖数量如果大于0，说明已经开始或结束
      return this.nowLotteryAward.id && this.nowLotteryAward.count > 0
    },
    limitDisable() {
      return this.ban || this.themeBan
    },
    ban() {
      return this.nowLotteryAward.displayState === 'DOWN_WALL'
    },
    prizeNamePlaceholder() {
      return this.prizeNameFocusFlag ? '' : '如：\n苹果手机\n价值5000元'
    },
    isEdit() {
      if (this.nowLotteryAward.displayState === 'DOWN_WALL') return true
      return false
    },
    btnDisabled() {
      return (
        this.nowLotteryAward.displayState !== 'DRAFT' && this.nowLotteryAward.id && !Hi.Object.difference(this.lotteryAward, this.nowLotteryAward)
      )
    },
    wallVersion() {
      return this.wall.wallVersion
    },
    _countMax() {
      return this.awardList.length ? this.awardList.reduce((total, award) => total + award.count, 0) : Infinity
    },
  },
  methods: {
    radioChange(e) {
      if (e === 'Y' && this.nowLotteryAward.id && this.awardList.length > 1) {
        this.$notify.error({ title: '错误', message: '已设置多奖品' })
        return
      }
      if (e === 'Y') {
        this.nowLotteryAward.extractType = 'TOTAL'
      }
      this.nowLotteryAward.singlePrizeSwitch = e
    },
    // 新窗口打开名单管理
    async oepnRoster() {
      const routeUrl = this.$router.resolve({
        name: 'wall-interact-lottery',
        query: { wallFlag: this.$route.query.wallFlag, open: 'roster' },
      })
      window.open(routeUrl.href, '_blank')
    },
    async configChange(awardconf) {
      Object.assign(this.nowLotteryAward, awardconf)
    },
    // [{Object}] obj   奖品新增或删除后的列表   obj.add  新增
    awardChange(obj) {
      this.awardChangeObj = obj
    },
    async save(displayState) {
      this.nowLotteryAward.displayState = displayState
      try {
        await this.$refs.form.validate()
        if (this.nowLotteryAward.id) {
          let d1 = Hi.Object.difference(this.lotteryAward, this.nowLotteryAward)
          if (this.nowLotteryAward.displayState !== displayState) {
            if (!d1) return
            d1.displayState = displayState
          }
          if (d1) {
            delete d1.limitType
            await api.walllottery.update({
              where: { id: this.nowLotteryAward.id, wallId: this.config.wallId },
              update: Object.assign(d1, { displayState }),
            })
            this.$notify.success({ title: '成功', message: '活动保存成功' })
          }
        } else {
          let data = await api.walllottery.insert({
            wallId: this.wall.id,
            ...this.nowLotteryAward,
            prizeType: this.prizeType,
          })
          this.awardsId = data //奖品id
          this.$notify.success({ title: '成功', message: '活动创建成功' })
        }
        await this.initData()
      } catch (err) {
        console.log(err)
        if (err === false) return false
        if (err !== 'cancel') {
          this.$notify.error({ title: '错误', message: err.msg || '提交数据出错' })
        }
      }
    },
    async getAwardgroupList() {
      try {
        let list = await api.lotteryawardgroup.list({
          where: {
            wallId: this.wall.id,
            lotteryId: this.editId || this.nowLotteryAward.id,
          },
        })
        let arr = []
        list.forEach((item) => {
          arr.push(item.groupId)
        })
        this.ableGroup = arr
        this.nowAbleGroup = Hi.Object.copy(this.ableGroup)
      } catch (err) {
        console.log(err)
        this.$notify.error({ title: '错误', message: err.msg })
      }
    },
    async backList() {
      let d1 = Hi.Object.difference(this.lotteryAward, this.nowLotteryAward)
      let d3 = JSON.stringify(this.ableGroup) !== JSON.stringify(this.nowAbleGroup)
      if (d1 || d3) {
        this.$confirm('是否保存您所做的更改？', '提示', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            await this.save('NORMAL')
          })
          .catch((err) => {
            if (err === 'cancel') {
              this.$emit('list')
            }
          })
      } else {
        this.$emit('list')
      }
    },
    //初始化数据
    async initData() {
      try {
        let editId = this.editId || this.nowLotteryAward.id || this.awardsId
        if (editId) {
          this.lotteryAward = await api.walllottery.read({
            where: { id: editId, wallId: this.wall.id },
          })
          this.awardsId = this.lotteryAward.id
        }
        this.nowLotteryAward = Hi.Object.copy(this.lotteryAward)
      } catch (err) {
        console.error(err)
      }
    },

    async awardListChange({ awardList }) {
      this.awardList = Hi.Object.copy(awardList)
    },
  },
  async mounted() {
    await this.initData()
    this.$emit('loaded')
    this.loadPage = true
  },
}
</script>
<style scoped lang="stylus">
.mr-10 {
  margin-right: 10px;
}

.ml-10 {
  margin-left: 10px;
}

.w348 {
  width: 348px;
}

.w100 {
  width: 100px;
}

.margin1020 {
  margin: 0 15px 0 10px;
}

.btn-to-edit {
  padding-top: 6px;
  padding-bottom: 6px;
}

.control {
  display: flex;
  align-items: center;
  justify-content: center;
}

.award-pic {
  width: 86px;
  height: 86px;
  border: 1px solid #bfcbd9;

  .img {
    display: inline-block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}

.inline {
  display: inline-block;
}

>>> .el-upload--picture-card {
  width: 86px;
  height: 86px;
  line-height: 86px;
  border: 0;
  position: relative;
}

>>> .el-upload--picture-card p {
  width: 86px;
  height: 86px;
  line-height: 86px;
  text-align: center;
  position: absolute;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  color: #fff;
  visibility: hidden;
}

>>> .el-upload--picture-card:hover p {
  visibility: visible;
}

.sub{
  display: flex;
  font-size: 12px;
  color: #999;
  div:first-child{
    width : 140px;
  }
}
.doubt{
  width: 14px;
  height: 14px;
  margin-left: 10px;
}
</style>
