<template>
  <el-dialog
    title="导入名单"
    :visible.sync="localVisible"
    width="600px"
    destroy-on-close
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
  >
    <!-- 导入模式选择 -->
    <div class="import-mode-section">
      <el-radio v-model="importMode" label="multiple" @change="handleModeChange">
        多个字段
        <el-tooltip content="导入表格内多个字段，按照字段进行分段，如座位、区域、排号">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </el-radio>
      <el-radio v-model="importMode" label="single" @change="handleModeChange">
        单个字段(自动分段)
        <el-tooltip content="导列表内单个字段，根据设置的规则自动分成多段。">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </el-radio>
    </div>

    <!-- 多个字段模式 -->
    <div class="multiple-mode">
      <div class="section-title flex flex-j-sb mrg-b-10 flex-a-c">
        <span>1. 设置分段名称</span>
        <el-button type="primary" size="small" plain @click="addSegment" :disabled="nowStageList.length >= 3"> 添加分段 </el-button>
      </div>
      <div class="segment-settings">
        <el-table :data="nowStageList" border style="width: 100%">
          <el-table-column prop="name" label="分段名称">
            <template v-slot="{ row, $index }">
              <el-input v-model="row.name" :placeholder="`分段${$index + 1}`" maxlength="10"></el-input>
            </template>
          </el-table-column>
          <el-table-column v-if="importMode === 'single'" label="位数" width="200">
            <template v-slot="{ row }">
              <div class="position-range">
                <el-input v-model.number="row.startIndex" :min="0" :max="row.endIndex" size="small" maxlength="2" type="number"></el-input>
                <span>~</span>
                <el-input v-model.number="row.endIndex" :min="row.startIndex" :max="20" size="small" maxlength="2" type="number"></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="importMode === 'single'" label="例" width="100">
            <template v-slot="{ row }">
              <span>{{ getExample(row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template v-slot="{ $index }">
              <el-button v-if="nowStageList.length > 1" type="text" style="color: #f56c6c" size="small" @click="removeSegment($index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="segment-tip">
          最多可添加三个分段
          <el-button
            type="primary"
            size="mini"
            :loading="savingStages"
            :disabled="isStagesSaved()"
            @click="handleSaveStages"
            style="margin-left: 10px"
          >
            {{ savingStages ? '保存中...' : '保存分段设置' }}
          </el-button>
        </div>
      </div>

      <!-- 下载模板和上传 -->
      <div class="section-title">2. 上传数据文件</div>
      <div class="upload-section" :class="{ 'disabled-section': !isStagesSaved() }">
        <div v-if="!isStagesSaved()" class="stage-warning">
          <i class="el-icon-warning"></i>
          <span>请先保存分段设置后再进行数据导入</span>
        </div>
        <div class="upload-actions">
          <el-button @click="exportTable" :disabled="!isStagesSaved()">下载模板</el-button>
          <hi-upload-excel @parsing="(v) => (fileParsingLoad = v)" @change="uploadExcel" :disabled="!isStagesSaved()">
            <el-button type="primary" :disabled="!isStagesSaved()">上传名单</el-button>
          </hi-upload-excel>
        </div>

        <div v-if="fileName" class="file-info">
          <i class="el-icon-document"></i>
          <span>{{ fileName || '名单导入模板 .xls' }}</span>
          <el-button type="text" @click="clearFile">移除</el-button>
        </div>
        <div class="upload-tip">
          {{ isStagesSaved() ? '下载模板，根据模板格式填写信息后上传。' : '请先保存分段设置，然后下载对应的模板文件。' }}
        </div>
      </div>
    </div>

    <div v-if="showLimit" class="fred font-12 mrg-t-5">
      数据总数超过{{ importCountMaxLimit }}条，无法添加，请<span v-if="isOem"> {{ wall.wallVersion === 'product' ? '联系客服' : '升级活动' }}</span>
      <span class="uphint" v-else-if="activityType !== 'product-advance'" @click="$emit('upgrade')"> 升级高级活动 </span>
      <span v-else>联系客服</span>
    </div>
    <!-- 底部操作区 -->

    <div slot="footer" class="dialog-footer">
      <div class="footer-info">
        <div class="limit-info">
          <p class="limit-current">当前名单数量限制：{{ importCountMaxLimit }}</p>
          <p class="limit-hint">
            {{ limitHintText }}
            <el-button v-if="activityType === 'product-advance' && !isOem" type="text" size="mini" @click="showServiceQRcode"> 联系客服 </el-button>
          </p>
        </div>
      </div>

      <div class="footer-actions">
        <el-button @click="localVisible = false" :disabled="importing || savingStages"> 取消 </el-button>
        <el-button
          type="primary"
          @click="handleImport"
          :loading="importing"
          :disabled="!fileName || showLimit || importing || savingStages || !isStagesSaved()"
        >
          {{ importing ? '导入中...' : '开始导入' }}
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import api from '@/api'
import HiUploadExcel from '@/components/upload-excel'
import { Hi, timer, showServiceQRcode } from '@/libs/common'
import { exportMixin } from '@/libs/mixins'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'ImportDialog',
  inject: ['wall'],
  mixins: [exportMixin],
  components: { HiUploadExcel },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    config: {
      type: Object,
      default: () => ({}),
    },
    stageList: {
      type: Array,
      default: () => [],
    },
    groupList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      localVisible: false,
      importMode: 'multiple',
      importing: false,
      savingStages: false, // 保存分段设置状态
      // 导入数据
      showLimit: false,
      fileName: '',
      fileParsingLoad: false,
      importData: [],
      nowStageList: [],
    }
  },
  computed: {
    ...mapState('wall', ['wall']),
    ...mapGetters({
      isOem: 'user/isOem',
    }),
    activityType() {
      return this.$store.getters['wall/getActivityType']
    },
    importCountMaxLimit() {
      return this.$store.getters['user/getCountMaxLimit'](this.config, this.wall)
    },
    limitHintText() {
      if (this.isOem) {
        return ''
      }
      return this.activityType === 'product-advance' ? '扩容名单数量请联系客服' : '升级活动可扩容名单数量'
    },
  },
  watch: {
    visible(val) {
      this.localVisible = val
    },
    localVisible(val) {
      this.$emit('update:visible', val)
    },
    stageList: {
      handler(val) {
        this.nowStageList = Hi.Object.copy(val)
      },
      deep: true,
      immediate: true,
    },
    activityType: {
      handler(val) {
        this.localVisible = false
      },
    },
  },
  methods: {
    // 模式切换
    handleModeChange() {
      this.clearFile()
    },

    // 添加分段
    addSegment() {
      if (this.nowStageList.length < 3) {
        let key
        const keys = this.nowStageList.map((item) => item.key)
        const allKeys = ['d1', 'd2', 'd3']
        const missingKey = allKeys.find((key) => !keys.includes(key))
        if (missingKey) {
          key = missingKey
        }
        this.nowStageList.push({
          wallId: this.wall.id,
          name: `示例`,
          key,
        })
      }
    },

    // 移除分段
    removeSegment(index) {
      if (this.nowStageList.length > 1) {
        this.nowStageList.splice(index, 1)
      }
    },

    // 保存分段设置
    async handleSaveStages() {
      // 验证分段名称
      if (this.nowStageList.some((stage) => !stage.name || !stage.name.trim())) {
        this.$notify.warning({
          title: '错误',
          message: '请填写所有分段名称',
        })
        return
      }

      // 检查是否有重复的分段名称
      const names = this.nowStageList.map((stage) => stage.name.trim())
      const uniqueNames = [...new Set(names)]
      if (names.length !== uniqueNames.length) {
        this.$notify.error({
          title: '错误',
          message: '分段名称不能重复',
        })
        return
      }

      this.savingStages = true

      try {
        // 检查是否有现有数据
        const count = await api.seglotteryimport.count({
          where: { wallId: this.wall.id },
        })

        if (count > 0) {
          // 有数据时需要确认
          try {
            await this.$confirm('当前已有名单数据，保存分段设置将清空现有数据，是否继续？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })

            // 清空现有数据
            await api.seglotteryimport.cleardata({ where: { wallId: this.wall.id } })
          } catch (error) {
            this.savingStages = false
            return // 用户取消操作
          }
        }

        // 保存分段设置
        await this.updateStages()

        this.$notify.success({ title: '成功', message: '分段设置保存成功' })
        this.$emit('refresh')
      } catch (error) {
        console.error('保存分段设置失败:', error)
        this.$notify.error({
          title: '错误',
          message: error.message || error.msg || '保存分段设置失败',
        })
      } finally {
        this.savingStages = false
      }
    },

    // 获取示例
    getExample(rule) {
      const example = '15888888888'
      return example.substring(rule.startIndex - 1, rule.endIndex)
    },

    // 文件选择
    async uploadExcel(data = [[]], sheet, name) {
      try {
        this.showLimit = false
        this.fileParsingLoad = true
        await timer(500)
        let importData = data[0]
        //去除空行
        importData = importData.filter((i) => {
          return i.length > 0
        })
        if (importData.length < 2) {
          this.$notify.error({ title: '错误', message: '请检查导入文件,必填项不能为空' })
          this.fileParsingLoad = false
          return
        }
        importData.shift() //去掉表头数据

        this.importData = importData
        this.fileName = name
        this.showLimit = this.importData.length > this.importCountMaxLimit
      } catch (error) {
        console.log(error)
      }
      this.fileParsingLoad = false
    },

    // 清除文件
    clearFile() {
      this.fileName = ''
    },

    // 下载多字段模板
    async exportData() {
      try {
        // 根据formMap排序获取key列表
        const sortedKeys = this.nowStageList.slice().map((item) => item.key)

        // 构建数据对象
        let data = []
        let exampleRow = {
          wei: '13888888888',
          group: '默认分组，分组1，分组2',
        }
        sortedKeys.forEach((key) => {
          exampleRow[key] = '示例1'
        })
        data.push(exampleRow)

        // 构建label对象
        let labelObj = {}
        sortedKeys.forEach((key) => {
          labelObj[`${key}Name`] = this.nowStageList.find((item) => item.key === key).name
        })

        // 构建columns
        let columns = []

        if (this.importMode === 'single') {
          columns.push({
            label: '名单数据（自动分段）',
            property: 'wei',
            autoWidth: true,
          })
        } else {
          columns = sortedKeys.map((key) => {
            let label = labelObj[`${key}Name`]
            return {
              label,
              property: key,
              autoWidth: true,
            }
          })
        }

        columns.push({
          label: '分组（多个分组分号隔开，非必填，字母不区分大小写）',
          property: 'group',
          autoWidth: true,
        })

        return { name: '导入名单模板', sheets: [{ name: '导入名单模板', columns, data }] }
      } catch (error) {
        console.log(error)
        this.$notify.error({ title: '错误', message: error.msg || error.message || '导出失败!' })
      }
    },

    // 验证导入数据
    validateImportData() {
      if (!this.fileName) {
        this.$message.warning('请先选择要导入的文件')
        return false
      }

      if (this.importData.length > this.importCountMaxLimit) {
        this.showLimit = true
        return false
      }

      return true
    },

    // 检查分段设置是否已保存
    isStagesSaved() {
      // 检查当前分段设置是否与已保存的一致
      if (this.stageList.length === 0) {
        return false
      }

      // 比较分段数量
      if (this.nowStageList.length !== this.stageList.length) {
        return false
      }

      const batch = Hi.Object.batchSave(this.stageList, this.nowStageList)
      return !batch
    },

    // 处理导入数据
    processImportData(stageList) {
      const updateList = []
      const groupArray = []
      const keys = stageList.map((item) => item.key)

      this.importData.forEach((item) => {
        const data = {}

        // 处理分段数据
        keys.forEach((key, index) => {
          data[key] = item[index]
        })

        // 处理单个字段模式的位数信息
        if (this.importMode === 'single') {
          const wei = String(item[0] || '')
          stageList.forEach((rule) => {
            const value = wei.substring(rule.startIndex - 1, rule.endIndex)
            data[rule.key] = value
          })
        }

        // 处理分组数据
        const groupStr = item[item.length - 1]
        if (typeof groupStr === 'string') {
          data.group = groupStr
            .replace(/；|,|，/g, ';')
            .split(';')
            .map((group) => group.trim())
            .filter((group) => group)
        } else {
          data.group = []
        }

        // 按顺序收集所有分组名称，保持先后顺序
        data.group.forEach((groupName) => {
          if (groupName.trim()) {
            groupArray.push(groupName.trim())
          }
        })
        updateList.push(data)
      })

      // 转换为 Set 以便后续处理，但保持了收集时的顺序
      const groupSet = new Set(groupArray)
      return { updateList, groupSet }
    },

    // 清除现有数据
    async clearExistingData() {
      try {
        await api.seglotteryimport.cleardata({ where: { wallId: this.wall.id } })
      } catch (error) {
        throw new Error(`清除现有数据失败: ${error.msg || error.message}`)
      }
    },

    // 更新阶段配置
    async updateStages() {
      const batch = Hi.Object.batchSave(this.stageList, this.nowStageList)
      if (!batch) return false

      // 为批量操作添加wallId
      batch.forEach((item) => {
        switch (item.type) {
          case 'add':
            item.data.forEach((dataItem) => {
              // @ts-ignore
              dataItem.wallId = this.wall.id
            })
            break
          case 'del':
            item.data = {
              // @ts-ignore
              wallId: this.wall.id,
              idList: item.data,
            }
            break
          case 'update':
            // @ts-ignore
            item.data.where.wallId = this.wall.id
            break
        }
      })

      try {
        await api.seglotterystage.batch(batch)
      } catch (error) {
        throw new Error(`更新阶段配置失败: ${error.msg || error.message}`)
      }
      return true
    },

    // 创建新分组
    async createNewGroups(groupSet) {
      // 1. 已存在: ["默认分组"] → seenGroupsLowerCase: ["默认分组"]
      // 2. 处理"分组A": 不存在 → 添加到 processedGroups: ["分组A"]
      // 3. 处理"分组a": 已存在(小写相同) → 跳过
      // 4. 处理"分组B": 不存在 → 添加到 processedGroups: ["分组A", "分组B"]
      // 5. 处理"分组A": 已存在 → 跳过
      // 最终创建: ["分组A", "分组B"]

      // 获取已存在的分组列表
      const existingGroupNames = this.groupList.map((item) => item.name)
      // 获取新分组列表
      const groupArray = Array.from(groupSet).filter((groupName) => groupName && groupName.trim())

      // 按照先后顺序处理分组，大小写不敏感去重
      const processedGroups = []
      const seenGroupsLowerCase = new Set()

      // 先添加已存在的分组（小写形式）到已见集合
      existingGroupNames.forEach((name) => {
        seenGroupsLowerCase.add(name.toLowerCase())
      })

      // 按顺序处理新分组
      groupArray.forEach((groupName) => {
        const lowerCaseName = groupName.toLowerCase() // 小写形式
        if (!seenGroupsLowerCase.has(lowerCaseName)) {
          processedGroups.push(groupName)
          seenGroupsLowerCase.add(lowerCaseName)
        }
      })

      if (processedGroups.length === 0) return

      try {
        await Promise.all(
          processedGroups.map((name) =>
            api.seglotterygroup.add({
              wallId: this.wall.id,
              name,
              type: 'CUSTOM',
            })
          )
        )
      } catch (error) {
        throw new Error(`创建分组失败: ${error.msg || error.message}`)
      }
    },

    // 创建导入记录
    async createImportRecords(updateList, groupList) {
      let importBatch = []
      let groupAssociations = []
      updateList.forEach((item) => {
        const importData = {
          wallId: this.wall.id,
          ...item,
          startIndex: Number(item.startIndex) || 0,
          endIndex: Number(item.endIndex) || 0,
        }
        delete importData.group
        importBatch.push(importData)

        // 生成对应分段的唯一key
        let uid = ['d1', 'd2', 'd3']
          .map((key) => (importData[key] = item[key]))
          .filter(Boolean)
          .join('')

        if (item.group && item.group.length > 0) {
          const associations = item.group
            .map((name) => {
              // 大小写不敏感查找分组，优先使用已存在的分组名称
              const trimmedName = name.trim()
              const group = groupList.find((g) => g.name.toLowerCase() === trimmedName.toLowerCase())
              return group
                ? {
                    wallId: this.wall.id,
                    groupId: group.id,
                    uid,
                  }
                : null
            })
            .filter(Boolean)
          groupAssociations.push(...associations)
        }
      })

      const data = await api.seglotteryimport.batchAdd({ dataList: importBatch })

      // 如果有分组关联记录，批量保存
      if (groupAssociations.length > 0) {
        groupAssociations.forEach((item) => {
          item.seglotteryImportId = data[item.uid]
        })
        const saveData = Hi.Object.batchSave([], groupAssociations)
        await api.seglotteryimportgroup.batch(saveData)
      }
    },

    // 导入处理 - 优化后的主函数
    async handleImport() {
      // 1. 验证数据
      if (!this.validateImportData()) return

      // 2. 检查分段设置是否已保存
      if (!this.isStagesSaved()) {
        this.$message.warning('请先保存分段设置')
        return
      }

      // 3. 确认操作
      try {
        await this.$confirm('导入名单文件时将清空当前名单列表，确定继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (error) {
        return // 用户取消操作
      }

      this.importing = true

      try {
        // 使用已保存的分段配置
        let _stageList = Hi.Object.copy(this.stageList)

        // 1. 清除现有数据
        await this.clearExistingData()

        // 2. 处理导入数据
        const { updateList, groupSet } = this.processImportData(_stageList)

        // 3. 创建新分组
        await this.createNewGroups(groupSet)

        // 4. 获取最新分组列表
        const groupList = await api.seglotterygroup.list({ where: { wallId: this.wall.id } })

        // 5. 创建导入记录
        await this.createImportRecords(updateList, groupList)

        this.$notify.success({ title: '成功', message: '导入名单成功' })
        this.handleClose()
        this.$emit('refresh')
      } catch (error) {
        this.$notify.error({
          title: '错误',
          message: error.message || error.msg || '导入失败，请稍后重试',
        })
      } finally {
        this.importing = false
      }
    },

    handleOpen() {
      this.importMode = 'multiple'
      this.nowStageList = Hi.Object.copy(this.stageList)
      this.importData = []
      this.fileName = ''
      this.showLimit = false
      this.importing = false
      this.savingStages = false
    },

    // 关闭弹窗
    handleClose() {
      this.localVisible = false
      this.clearFile()
      this.importing = false
      this.savingStages = false
    },
    showServiceQRcode() {
      showServiceQRcode('name')
    },
  },
}
</script>

<style scoped lang="scss">
.import-mode-section {
  margin-bottom: 20px;
  border-radius: 4px;
}

.multiple-mode,
.single-mode {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
}

.segment-settings {
  margin-bottom: 15px;
}

.segment-tip {
  margin-top: 10px;
  font-size: 12px;
  color: #6b7280;
  text-align: right;
}

.segment-rules {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.rules-table {
  margin-bottom: 10px;
}

.position-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;
  color: #1e40af;
  font-size: 14px;
  margin-top: 15px;
}

.import-progress {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
}

.progress-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.progress-header i {
  font-size: 16px;
  color: #3b82f6;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 16px 0 0 0;
  border-top: 1px solid #e5e7eb;

  .footer-info {
    .limit-current {
      font-size: 14px;
      font-weight: 500;
      color: #374151;
      text-align: left;
    }

    .limit-hint {
      font-size: 12px;
      color: #6b7280;
      line-height: 1.4;
      text-align: left;
    }
  }
}

.upload-section {
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;

  &.disabled-section {
    background-color: #f5f7fa;
    opacity: 0.7;
  }
}

.stage-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 14px;
  margin-bottom: 15px;

  i {
    font-size: 16px;
  }
}

.upload-tip {
  margin-top: 10px;
  font-size: 12px;
  color: #909399;
}

.uphint {
  // "升级高级活动"等链接样式
  cursor: pointer;
  text-decoration: none;
  color: #409eff;
  font-weight: 500; // 略微加粗
  &:hover {
    text-decoration: underline;
    color: #337ecc;
  }
}
</style>
