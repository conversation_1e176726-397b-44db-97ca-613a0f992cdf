import api from '@/api'
import { Hi } from '@/libs/common'
import { Notification } from 'element-ui'

export default {
  name: 'questionBank',
  namespaced: true,
  state: {
    // 题目难度相关
    subjectDifficultyList: [],
    // 题目分组相关
    subjectGroupList: [],
    // 题目相关
    questionList: [],
  },
  getters: {},
  actions: {
    // 初始化题库数据
    async initQuestionBank({ dispatch }) {
      try {
        await Promise.all([
          dispatch('fetchQuestions'), // 获取题目列表
          dispatch('fetchSubjectDifficulty'), // 获取题目难度列表
          dispatch('fetchSubjectGroup'), // 获取题目分组列表
        ])
      } catch (error) {
        console.error('初始化题库数据失败:', error)
        throw error
      }
    },

    // 获取题目难度列表
    async fetchSubjectGroup({ commit }) {
      try {
        const data = await api.subjectgroup.list()
        commit('setSubjectGroupList', data)
        return data
      } catch (error) {
        Notification.error({ title: '错误', message: error.msg || '获取题目难度列表失败' })
        throw error
      }
    },

    // 保存题目分组
    async saveSubjectGroup({ dispatch }, group) {
      try {
        if (group.id) {
          await api.subjectgroup.update({
            where: { id: group.id },
            update: {
              name: group.name,
            },
          })
        } else {
          await api.subjectgroup.add(group)
        }
      } catch (error) {
        Notification.error({ title: '错误', message: error.msg || '保存题目分组失败' })
        throw error
      } finally {
        await dispatch('fetchSubjectGroup')
      }
    },

    // 删除题目分组
    async deleteSubjectGroup({ dispatch }, id) {
      try {
        await api.subjectgroup.delete({ where: { id } })
        Notification.success({ title: '成功', message: '删除题目分组成功' })
      } catch (error) {
        Notification.error({ title: '错误', message: error.msg || '删除题目分组失败' })
        throw error
      } finally {
        await dispatch('fetchSubjectGroup')
      }
    },

    // 获取题目难度列表
    async fetchSubjectDifficulty({ commit }) {
      try {
        const data = await api.subjectdifficulty.list()
        commit('setSubjectDifficultyList', data)
        return data
      } catch (error) {
        Notification.error({ title: '错误', message: error.msg || '获取题目难度列表失败' })
        throw error
      }
    },

    // 保存题目难度
    async saveSubjectDifficulty({ state, dispatch }, difficulty) {
      try {
        let data
        if (difficulty.id) {
          const d1 = state.subjectDifficultyList.find((d) => d.id === difficulty.id)
          const update = Hi.Object.difference(d1, difficulty)
          if (update) {
            data = await api.subjectdifficulty.update({
              where: {
                id: difficulty.id,
              },
              update,
            })
            Notification.success({ title: '成功', message: '更新题目难度成功' })
          }
        } else {
          data = await api.subjectdifficulty.add(difficulty)
          Notification.success({ title: '成功', message: '保存题目难度成功' })
        }
        await dispatch('fetchSubjectDifficulty')
        return data
      } catch (error) {
        Notification.error({ title: '错误', message: error.msg || '保存题目难度失败' })
        throw error
      }
    },

    // 删除题目难度
    async deleteSubjectDifficulty({ dispatch }, id) {
      try {
        await api.subjectdifficulty.delete({
          where: { id },
        })
        Notification.success({ title: '成功', message: '删除题目难度成功' })
      } catch (error) {
        Notification.error({ title: '错误', message: error.msg || '删除题目难度失败' })
        throw error
      } finally {
        await dispatch('fetchSubjectDifficulty')
      }
    },

    // 获取题目列表
    async fetchQuestions({ commit }, params) {
      try {
        const _transform = (value) => {
          const data = Hi.Object.copy(value)
          if (data && Array.isArray(data)) {
            return data.map((item) => {
              try {
                return {
                  ...item,
                  options: JSON.parse(item.options || '[]'),
                  subjectContent: JSON.parse(item.subjectContent || '{}'),
                }
              } catch (err) {
                console.error('解析题目数据失败:', err)
                return item
              }
            })
          }
          return []
        }

        let where = {}
        if (params) {
          where = {
            ...(params.groupId && { groupId: params.groupId }),
            ...(params.difficultyId && { difficultyId: params.difficultyId }),
            ...(params.type && { type: params.type }),
          }
        }
        const data = await api.subject.list({
          where,
        })
        const list = _transform(data)
        commit('setQuestionList', list)
        return data
      } catch (error) {
        Notification.error({ title: '错误', message: error.msg || '获取题目列表失败' })
        throw error
      }
    },

    // 保存题目
    async saveQuestion({ state, commit, dispatch }, question) {
      if (!question) {
        throw new Error('题目数据不能为空')
      }

      try {
        const _transform = (question) => {
          const { subjectContent, options, ...rest } = question
          return {
            ...rest,
            subjectContent: JSON.stringify(subjectContent),
            options: JSON.stringify(options),
          }
        }

        const _update = async () => {
          // 更新题目
          const d1 = state.questionList.find((q) => q.id === question.id)
          const update = Hi.Object.difference(_transform(d1), _transform(question))
          if (update) {
            const result = await api.subject.update({
              where: {
                id: question.id,
              },
              update,
            })
            Notification.success({ title: '成功', message: '更新题目成功' })
            return result
          }
          return null
        }

        const _add = async () => {
          // 新增题目
          let result = _transform(question)
          await api.subject.add(result)
          Notification.success({ title: '成功', message: '新增题目成功' })
          return result
        }

        const result = question.id ? await _update() : await _add()
        return result
      } catch (error) {
        console.error(error)
        Notification.error({ title: '错误', message: error.msg || '保存失败' })
        throw error
      }
    },

    // 批量删除题目
    async deleteQuestions({ dispatch }, ids) {
      if (!ids || !ids.length) {
        throw new Error('请选择要删除的题目')
      }
      try {
        await api.subject.delete({
          where: { idList: ids },
        })
        await dispatch('initQuestionBank')
        Notification.success({ title: '成功', message: '删除题目成功' })
        return true
      } catch (error) {
        Notification.error({ title: '错误', message: error.msg || '删除题目失败' })
        throw error
      }
    },
  },
  mutations: {
    // 设置题目难度列表
    setSubjectDifficultyList(state, list) {
      state.subjectDifficultyList = list || []
    },

    // 设置题目分组列表
    setSubjectGroupList(state, list) {
      state.subjectGroupList = list || []
    },

    // 设置题目列表
    setQuestionList(state, list) {
      state.questionList = list || []
    },
  },
}
