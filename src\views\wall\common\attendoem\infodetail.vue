<template>
  <el-dialog title="提示" :visible.sync="dialogVisible" width="1140px" :before-close="handleClose" @close="$emit('close')" close-on-click-modal>
    <div class="info-box">
      <img src="./img/table.png" alt="">
    </div>
  </el-dialog>
</template>
<script>
import api from '@/api';

export default {
  name: 'info',
  components: {},
  props: ['type'],
  data() {
    return {
      dialogVisible: true,
      dataList: [
        { t1: '用户信息', t2: '授权后自动获取', t3: '参与者手动编辑头像昵称', t4: '参与者手动填写头像昵称' },
        {
          t1: '自动登录',
          t2: '支持',
          t3: '清理缓存后需重新登录',
          t4: '关闭网页后，在不清理缓存的情况下可自动登录，若清理缓存需重新登录(会生成新用户)',
        },
        {
          t1: '功能支持',
          t2: '全功能支持',
          t3: '不支持在线支付、现金红包奖品领取等功能',
          t4: '暂不支持 在线支付、现金红包奖品领取等功能',
        },
        {
          t1: '适用场景',
          t2: '中国大陆地区，主要以微信方式参与，少量其他平台多与',
          t3: '港澳台、海外地区活动 或 混合平台方式参与的活动',
        },
      ],
    };
  },
  computed: {},
  methods: {
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === 3) {
        if (columnIndex === 1) {
          return [1, 2];
        }
      }
    },
    async toDefault() {
      await api.wallconfig.update({
        where: { wallId: this.wall.id },
        update: { thirdAppScanBgUrl: '' },
      });
      await this.$emit('updateWallConfig');
    },
  },
  async mounted() { },
};
</script>
<style scoped lang="stylus">
.wechart
  >>>.el-table__header-wrapper .is-group tr:nth-child(1) th:nth-child(2)
    background #4886FF !important
  >>>.el-table__header-wrapper thead div
    background none !important
.other
  >>>.el-table__header-wrapper .is-group tr:nth-child(1) th:nth-child(3)
    background #4886FF !important
  >>>.el-table__header-wrapper thead div
    background none !important
</style>
