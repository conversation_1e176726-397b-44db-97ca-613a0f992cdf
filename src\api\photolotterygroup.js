import api from './api';
export default {
  list: (postData) => api.fetchBaseData('/pro/hxc/prophotolotterygroup/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/prophotolotterygroup/add.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/prophotolotterygroup/update.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/prophotolotterygroup/delete.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/prophotolotterygroup/batch.htm', postData),
};
