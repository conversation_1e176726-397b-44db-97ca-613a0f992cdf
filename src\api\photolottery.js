import api from './api'
export default {
  add: (v) => api.fetchBaseData('/pro/hxc/prophotolottery/add.htm', v),
  read: (v) => api.fetchBaseData('/pro/hxc/prophotolottery/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/prophotolottery/update.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/prophotolottery/list.htm', v),
  sort: (v) => api.fetchBaseData('/pro/hxc/prophotolottery/sort.htm', v),
}
