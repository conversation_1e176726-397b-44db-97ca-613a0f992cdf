<template>
  <div class="ambassador-box">
    <div class="content">
      <p class="content-tit" :class="{ center: isBind }">{{ isBind ? '暂无关联校园大使' : '校园大使信息' }}</p>
      <div class="no-relate" v-if="isBind">
        <p class="set-tit">设置关联</p>
        <div class="set-num">
          校园大使编号：
          <p>
            <el-input v-model.trim="ambNumber" placeholder="请输入大使编号"></el-input>
          </p>
          <el-button type="primary" @click="confirmBind">确认关联</el-button>
        </div>
        <p class="set-hint" v-show="!isExist">校园大使ID不存在</p>
      </div>
      <div class="has-relate" v-else>
        <div class="ambs-name">
          <p>大使编号：</p>
          {{ ambInfo.ambNumber }}
        </div>
        <div>
          <p class="right">联系人：</p>
          <div class="info">{{ ambInfo.contact }}</div>
          <p>联系电话：</p>
          {{ ambInfo.phone }}
        </div>
        <div class="info-last">
          <p class="right">邮箱：</p>
          <div class="info">{{ ambInfo.email }}</div>
          <p>QQ/微信：</p>
          {{ ambInfo.qq }}/{{ ambInfo.wechat }}
        </div>
        <el-button type="primary" @click="relateDialog = true">取消关联</el-button>
      </div>
    </div>
    <el-dialog :title="hasApply ? '提示' : '取消关联'" :visible.sync="relateDialog" :close-on-click-modal="false">
      <div v-if="!hasApply">
        <div class="cal-reason">
          <p>取消关联原因：</p>
          <div>
            <el-select v-model="unbindType">
              <el-option v-for="item in unbindReasons" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="cal-reason other-reason" v-if="isOther">
          <p>其他原因：</p>
          <div>
            <el-input type="textarea" :rows="2" v-model="unbindDescription" placeholder="请输入原因"> </el-input>
          </div>
        </div>
      </div>
      <div v-else>
        <p class="repeat"><i class="el-icon-warning"></i>您已提交过申请</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" v-if="!hasApply" @click="applyUnbind">提 交</el-button>
        <el-button type="primary" @click="relateDialog = false">{{ hasApply ? '关 闭' : '返 回' }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import api from '@/api'
export default {
  data() {
    return {
      ambInfo: {}, // 大使信息
      isBind: false, // 是否已经关联
      hasApply: false, // 是否已经提交过取消关联
      ambNumber: '', // 关联校园大使input
      isExist: true, // 关联校园大使是否存在
      relateDialog: false,
      unbindReasons: [
        {
          value: 'BIND_ERR',
          label: '关联错误',
        },
        {
          value: 'BAD_SERVICE',
          label: '服务不好',
        },
        {
          value: 'BAD_SUPPORT',
          label: '技术支持不够',
        },
        {
          value: 'OTHERS',
          label: '其他原因',
        },
      ],
      unbindType: 'BIND_ERR',
      unbindDescription: '', // 其他原因
      isOther: false,
    }
  },
  watch: {
    unbindType(val) {
      val === 'OTHERS' ? (this.isOther = true) : (this.isOther = false)
    },
  },
  methods: {
    async init() {
      await this.getBindInfo.call(this)
      if (this.ambInfo.ambNumber) {
        await this.getUnBindInfo.call(this)
      }
    },
    // 关联信息查询
    async getBindInfo() {
      try {
        this.ambInfo = await api.user.boundAmbInfo({})
        this.ambInfo.ambNumber ? (this.isBind = false) : (this.isBind = true)
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    // 取消关联申请查询
    async getUnBindInfo() {
      try {
        let unbindInfo = await api.ambschoolrelationapply.read({
          where: {
            state: 'UNAUDITED',
          },
        })
        unbindInfo ? (this.hasApply = true) : (this.hasApply = false)
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    // 关联校园大使
    async bindAmb() {
      try {
        let relate = await api.user.boundAmb({ ambNumber: this.ambNumber })
        this.isBind = false
        this.$notify.success({ title: '成功', message: '关联成功' })
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
        this.isBind = true
      }
    },
    // 确认关联
    async confirmBind() {
      await this.bindAmb.call(this)
      await this.getBindInfo.call(this)
    },
    // 取消关联
    async unbindAmb() {
      let param = {}
      this.$set(param, 'ambNumber', this.ambInfo.ambNumber)
      this.$set(param, 'unbindType', this.unbindType)
      if (this.unbindType === 'OTHERS' && !this.unbindDescription) {
        this.$notify.error({ title: '错误', message: '请输入原因' })
        return
      }
      this.unbindType === 'OTHERS' && this.$set(param, 'unbindDescription', this.unbindDescription)
      try {
        await api.user.unboundAmbApply(param)
        this.relateDialog = false
        this.$notify.success({ title: '成功', message: '取消关联申请成功' })
        this.hasApply = true
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
        this.relateDialog = false
        this.hasApply = false
      }
    },
    // 提交取消关联原因
    async applyUnbind() {
      if (this.hasApply) {
        this.relateDialog = true
        return
      } else {
        await this.unbindAmb.call(this)
      }
    },
  },
  created() {
    this.init.call(this)
  },
}
</script>
<style scoped lang="stylus">
.right {
  text-align: right;
}

.center {
  text-align: center;
  color: #666 !important;
}

.ambassador-box {
  width: 100%;
  height: calc(100% - 46px);
  min-width: 860px;
  background-color: #fff;
  border-radius: 7px;
  margin: 18px 0;
  padding-bottom: 10px;
}

.content {
  padding: 0px 25px;
  font-size: 14px;
  color: #666;
}

.content-tit {
  height: 74px;
  line-height: 74px;
  font-size: 16px;
  color: #333;
}

.set-tit {
  height: 32px;
}

.set-num {
  height: 30px;
  display: flex;
  align-items: center;

  p {
    width: 180px;
    margin: 0 10px;
  }
}

.set-hint {
  height: 40px;
  line-height: 40px;
  text-indent: 114px;
  color: #ff0000;
}

.has-relate {
  &>div {
    height: 30px;
    display: flex;
    align-items: center;
  }

  p {
    width: 74px;
    line-height: 30px;
  }
}

.info {
  width: 248px;
}

.info-last {
  margin-bottom: 20px;
}

.cal-reason {
  display: flex;
  align-items: center;

  p {
    width: 98px;
    text-align: right;
    margin-right: 10px;
    color: #666;
  }

  &>div {
    flex: 1;
  }

  .el-select {
    width: 100%;
  }
}

.other-reason {
  margin-top: 22px;
}

.repeat {
  height: 36px;
  display: flex;
  align-items: center;
  font-size: 18px;
  color: #333;

  i {
    color: #f7ba2a;
    margin-right: 14px;
    font-size: 36px;
  }
}
</style>
