import api from './api';
export default {
  read: v => api.fetchBaseData('/pro/hxc/prosignbook/read.htm', v),
  update: v => api.fetchBaseData('/pro/hxc/prosignbook/update.htm', v),
  add: v => api.fetchBaseData('/pro/hxc/prosignbook/add.htm', v),
  page: v => api.fetchBaseData('/pro/hxc/prosignbook/page.htm', v),
  list: v => api.fetchBaseData('/pro/hxc/prosignbook/list.htm', v),
  delete: postData => api.fetchBaseData('/pro/hxc/prosignbook/delete.htm', postData),
  finish: postData => api.fetchBaseData('/pro/hxc/prosignbook/finish.htm', postData),
};
