import api from './api'
export default {
  //你演我猜
  add: postData => api.fetchBaseData('/pro/hxc/proplayguess/add.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proplayguess/update.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/proplayguess/delete.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proplayguess/list.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/proplayguess/read.htm', postData),
}
