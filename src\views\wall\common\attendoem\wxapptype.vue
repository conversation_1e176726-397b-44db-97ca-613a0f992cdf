<template>
  <div>
    <div class="line">
      <label> <i class="disc">●</i>方式一：</label>
      <div class="value">请用微信扫码打开小程序参与现场互动</div>
    </div>
    <div class="line">
      <label></label>
      <div class="value flex flex-a-c">
        <div class="flex flex-a-fe">
          <div
            class="program"
            v-loading="miniprogramLoad2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
          >
            <hi-miniprogram-qrcode
              class="program"
              ref="minicodeImg"
              :qrcode="miniprogramUrl"
              :logo="miniprogramImg"
              :size="600"
              @loaded=";(miniprogramLoad = false), (miniprogramLoad2 = false)"
            >
            </hi-miniprogram-qrcode>
            <div class="uploadbox">
              <hi-upload-img :action="$hi.url.upload" :on-success="uploadSuccess" :show-file-list="false">
                <p>上传图片</p>
              </hi-upload-img>
            </div>
          </div>
          <hi-size-specification placement="top-start" :note="{ 推荐尺寸: '450*450(1:1)', 大小: '小于2M', 格式: 'jpg/png/bmp' }">
          </hi-size-specification>
        </div>
        <el-button type="primary" :loading="miniprogramLoad" @click="downloadMiniCode"> 下载小程序二维码图片 </el-button>
      </div>
    </div>
    <div class="line">
      <label> <i class="disc">●</i>方式二：</label>
      <div class="value">
        登录
        <a class="underline" href="https://mp.weixin.qq.com/" target="_blank">微信公众平台</a>，成功关联小程序后，(参见
        <a class="underline" href="https://www.yuque.com/hixianchang/gt1keq/pd81ki" target="_blank">公众号关联小程序教程</a>)
      </div>
    </div>
    <div class="line">
      <label></label>
      <div class="value">将小程序路径配置到“ <strong>自定义菜单</strong>”或“ <strong>公众号图文</strong>”</div>
    </div>
    <div class="line">
      <label></label>
      <div class="value flex flex-a-c">
        <strong class="width-150">小程序路径</strong>
        <el-input ref="programhref" type="text" readonly="readonly" :value="programHref"></el-input>
        <div class="block"></div>
        <el-button plain type="info" @click="copy('programhref')">复制</el-button>
      </div>
    </div>
    <div class="line">
      <label></label>
      <div class="value flex flex-a-c">
        <strong class="width-150">小程序AppID</strong>
        <el-input type="text" ref="programAppID" readonly="readonly" :value="programAppId"></el-input>
        <div class="block"></div>
        <el-button plain type="info" @click="copy('programAppID')">复制</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { Hi, env } from '@/libs/common'
import api from '@/api'
import QrcodeVue from 'qrcode.vue'
import HiMiniprogramQrcode from '@/components/miniprogram-qrcode'
export default {
  name: 'Wxapp',
  props: ['wall', 'route', 'actId'],
  components: {
    QrcodeVue,
    HiMiniprogramQrcode,
  },
  data() {
    return {
      wxauthconfig: {},
      miniprogramLoad: false,
      miniprogramLoad2: false,
      miniprogramImg: '',
      programAppId: env.miniprogram.appId,
    }
  },
  computed: {
    miniprogramUrl() {
      let value = encodeURIComponent(
        `mobileFlag=${this.wall.mobileFlag}${this.route ? '&route=' + this.route : ''}${this.actId ? '&id=' + this.actId : ''}`
      )
      return `/pro/hxc/web/proutilsalias/fetchQrcode.htm?key=hxc&value=${value}`
    },
    //小程序路径
    programHref() {
      return `${env.miniprogram.path}?mobileFlag=${this.wall.mobileFlag}${this.route ? '&route=' + this.route : ''}${
        this.actId ? '&id=' + this.actId : ''
      }`
    },
  },
  methods: {
    copy(cont) {
      this.$refs[cont].select()
      document.execCommand('Copy')
      this.$message({
        message: '复制成功',
        type: 'success',
      })
    },
    async fetchWxauthconfig() {
      this.wxauthconfig = await api.wxauthconfig.read({
        where: { appId: env.miniprogram.appId },
      })
    },
    // 小程序logo上传
    uploadSuccess(file, fileList) {
      file = file.data
      this.miniprogramImg = Hi.String.dealUrl(file.url)
      let data = api.wallconfig.update({
        where: { wallId: this.wall.id },
        update: { miniprogramImg: this.miniprogramImg },
      })
      //动态生成二维码
    },
    downloadMiniCode() {
      Hi.Save.downImg('小程序二维码.png', this.$refs.minicodeImg.getImg())
    },
  },
  async mounted() {
    //await this.fetchWxauthconfig.call(this)
    //读取miniprogramImg
    let wallconfig = await api.wallconfig.read({
      where: { wallId: this.wall.id },
    })
    this.miniprogramImg = wallconfig.miniprogramImg
  },
}
</script>
<style scoped lang="stylus">
.width-105
  width 105px
.width-150
  width 150px
.block
  width 10px
.clearbutton
  padding 0
  border 0
  &:hover
    background-color #fff
.disc
  color #4886ff
  font-style normal
  margin-right 5px
// 下载二维码按钮
.qrcode-down
  margin-top 8px
  margin-left 36px
  display block
  width 128px
  height 12px
  padding 9px 15px
  font-size 12px
  border-radius 3px
  color #fff
  text-align center
  background-color #409eff
  border-color #409eff
  line-height 12px
  border 0
  &:hover
    background #66b1ff
    border-color #66b1ff
    color #fff
.line
  margin 16px 0
  display flex
  label
    padding-left 5px
    padding-right 20px
    font-weight bold
    font-size 15px
    white-space nowrap
    width 80px
// 小程序二维码
.program
  position relative
  width 142px
  height 142px
  img
    width 142px
    height 142px
  .uploadbox
    position absolute
    top 40px
    left 40px
    width 63px
    height 63px
    border-radius 50%
    overflow hidden
    p
      position absolute
      left -8px
      bottom -14px
      z-index 1
      width 120%
      line-height 26px
      height 26px
      text-align center
      background-color rgba(0, 0, 0, 0.6)
      color #fff
      font-size 12px
      transform scale(0.83)
      margin 1em 0
    input
      position absolute
      z-index 2
      left 0
      bottom 0
      width 200%
      height 22px
      cursor pointer
      opacity 0
.ml36
  margin-left 36px
.mt10
  margin-top 10px
</style>
