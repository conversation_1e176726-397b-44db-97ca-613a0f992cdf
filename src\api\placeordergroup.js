import api from './api'
export default {
  //订货会-分组
  add: postData => api.fetchBaseData('/pro/hxc/proplaceordergroup/add.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/proplaceordergroup/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proplaceordergroup/update.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/proplaceordergroup/page.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/proplaceordergroup/delete.htm', postData),
  sort: postData => api.fetchBaseData('/pro/hxc/proplaceordergroup/sort.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proplaceordergroup/list.htm', postData),
}
