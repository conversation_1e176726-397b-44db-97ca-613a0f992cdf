import api from './api'
export default {
  // 业绩目标会v3 配置
  list: (postData) => api.fetchBaseData('/pro/hxc/properformancev3record/list.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/properformancev3record/page.htm', postData),
  clear: (postData) => api.fetchBaseData('/pro/hxc/properformancev3record/clear.htm', postData),

  add: (postData) => api.fetchBaseData('/pro/hxc/properformancev3record/add.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/properformancev3record/update.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/properformancev3record/delete.htm', postData),

  // 批量导入
  batchAdd: (postData) => api.fetchBaseData('/pro/hxc/properformancev3record/batchAdd.htm', postData),
}
