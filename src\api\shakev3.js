import api from './api'
export default {
  page: (postData) => api.fetchBaseData('/pro/hxc/proshakev3/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proshakev3/list.htm', postData),
  insert: (postData) => api.fetchBaseData('/pro/hxc/proshakev3/insert.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/proshakev3/read.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proshakev3/delete.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proshakev3/update.htm', postData),
}
