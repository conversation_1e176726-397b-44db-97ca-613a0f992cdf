<template>
  <el-dialog title="导入题目" :visible.sync="visible" :close-on-click-modal="false" width="470px" @close="handleClose">
    <div class="export-container">
      <ul class="step-list">
        <li class="step-item">
          <p class="step-title">
            下载导入模板
            <span class="tip-text">（下载后请勿更改模板格式）</span>
          </p>
          <a :href="templateDownloadUrl">
            <el-button type="primary" plain>下载</el-button>
          </a>
        </li>
        <li class="step-item">
          <p class="step-title">
            <span>上传题目文件</span>
          </p>
          <div>
            <hi-upload-excel @parsing="handleFileParsingStatus" @change="handleFileUpload">
              <el-button type="primary" plain>上传</el-button>
            </hi-upload-excel>
            <div v-if="fileName" class="upload-success-tip">
              <i class="fgreen el-icon-success"></i>
              <span class="tip-text">您已经成功上传：{{ fileName }}</span>
            </div>
          </div>
        </li>
      </ul>

      <!-- 导入进度显示 -->
      <div v-if="showImportProgress" class="import-progress">
        <p>正在导入题目，请稍候...</p>
        <el-progress :percentage="progress" :show-text="true"></el-progress>
      </div>
    </div>

    <span slot="footer">
      <el-button type="primary" @click="handleConfirmImport" :loading="isProcessing" :disabled="!canImport"> 确认导入 </el-button>
    </span>
  </el-dialog>
</template>
<script>
import api from '@/api'
import HiUploadExcel from '@/components/upload-excel'
import { env } from '@/libs/common'
import { mapActions, mapState, mapGetters } from 'vuex'

export default {
  name: 'QuestionExport',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    HiUploadExcel,
  },
  data() {
    return {
      fileParsingLoad: false,
      importData: [], // 导入数据
      groupList: [], // 导入数据分组
      difficultyList: [], // 导入数据难度
      fileName: '', // 导入文件名
      importLoad: false, // 导入进度
      progress: 0, // 导入进度百分比
    }
  },
  computed: {
    ...mapState('wall', ['wall']),
    ...mapState('questionBank', ['subjectGroupList', 'subjectDifficultyList']),
    ...mapGetters({
      wallFlagObj: 'wall/getWallFlagObj',
    }),

    // 当前墙体信息
    currentWall() {
      return this.wallFlagObj[this.$route.query.wallFlag]
    },

    // 模板下载链接
    templateDownloadUrl() {
      return `${env.static}/answerracev3/题目模板.xls`
    },

    // 是否显示导入进度
    showImportProgress() {
      return this.importLoad && this.progress > 0
    },

    // 是否正在处理中
    isProcessing() {
      return this.fileParsingLoad || this.importLoad
    },

    // 是否可以导入
    canImport() {
      return this.importData.length > 0 && !this.isProcessing
    },

    // 题目类型映射
    questionTypeMap() {
      return {
        单选题: 'RADIO',
        多选题: 'CHECKBOX',
      }
    },
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initQuestionBank()
      }
    },
  },
  methods: {
    ...mapActions('questionBank', ['initQuestionBank']),

    // 处理文件解析状态
    handleFileParsingStatus(status) {
      this.fileParsingLoad = status
    },

    // 处理文件上传
    async handleFileUpload(data, sheet, name) {
      await this.uploadExcel(data, sheet, name)
    },

    // 确认导入
    async handleConfirmImport() {
      await this.queryImport()
    },

    // 移除空行和表头
    removeEmptyAndHeader(importData) {
      const filteredData = importData.filter((row) => row.length > 0)
      if (filteredData.length === 0) {
        throw new Error('请检查导入文件,必填项不能为空')
      }
      // 移除表头
      filteredData.shift()
      return filteredData
    },

    // 上传Excel
    async uploadExcel(data, sheet, name) {
      // 重置数据
      this.importData = []
      this.groupList = []
      this.difficultyList = []
      const sheetName = Object.keys(sheet)

      try {
        this.fileParsingLoad = true

        for (let index = 0; index < sheetName.length; index++) {
          const sheetData = data[index]
          const questionType = this.questionTypeMap[sheetName[index]]

          if (sheetData.length === 0) {
            continue
          }

          console.log('开始导入数据---' + sheetName[index])
          const list = this.removeEmptyAndHeader(sheetData)

          // 处理每一行数据
          for (let rowIndex = 0; rowIndex < list.length; rowIndex++) {
            const rowData = list[rowIndex]

            // 验证数据有效性
            this.getSubject(rowData, questionType, rowIndex)
            this.getOption(rowData, questionType, rowIndex)
            this.getAnswer(rowData, questionType, rowIndex)
            this.getComment(rowData, questionType, rowIndex)
            this.getDifficulty(rowData, questionType)

            // 收集分组和难度信息
            this.groupList.push(this.getGroup(rowData, questionType))
            this.difficultyList.push(this.getDifficulty(rowData, questionType))
          }

          // 去重处理
          this.groupList = [...new Set(this.groupList.filter(Boolean))]
          this.difficultyList = [...new Set(this.difficultyList.filter(Boolean))]

          // 验证分组数量限制
          if (this.groupList.length > 50) {
            throw new Error('导入题目分组不能超过50个')
          }

          // 添加到导入数据
          this.importData.push({ key: questionType, list })
        }

        this.fileName = name
        this.fileParsingLoad = false
      } catch (error) {
        console.error('导入Excel数据失败:', error)
        this.$notify.error({
          title: '错误',
          message: error.message || '提交失败',
        })
        this.fileParsingLoad = false
        return false
      }

      return true
    },

    async queryImport() {
      if (this.importData.length === 0) {
        this.$notify.error({ title: '错误', message: '请先上传题目文件' })
        return
      }

      try {
        this.importLoad = true
        this.progress = 0

        // 创建新分组
        await this.createNewGroups()

        // 获取或创建默认难度
        const defaultDifficultyId = await this.getOrCreateDefaultDifficulty()

        // 处理导入数据
        const subjectList = await this.processImportData(defaultDifficultyId)

        // 分批导入题目
        await this.batchImportSubjects(subjectList)

        // 完成导入
        await this.completeImport()
      } catch (err) {
        console.error('导入失败:', err)
        this.importLoad = false
        this.$notify.error({
          title: '错误',
          message: err.message || '导入失败',
        })
      }
    },

    // 创建新分组
    async createNewGroups() {
      const existingGroupNames = this.subjectGroupList.map((group) => group.name.toLowerCase())
      const newGroups = this.groupList
        .filter((groupName) => groupName && !existingGroupNames.includes(groupName.toLowerCase()))
        .map((groupName) => ({
          wallId: this.currentWall.id,
          name: groupName,
        }))

      if (newGroups.length > 0) {
        await Promise.all(newGroups.map((group) => api.subjectgroup.add(group)))
      }
    },

    // 获取或创建默认难度
    async getOrCreateDefaultDifficulty() {
      const defaultDifficulty = this.subjectDifficultyList.find((item) => item.name === '默认难度')

      if (!defaultDifficulty) {
        return await api.subjectdifficulty.add({
          wallId: this.currentWall.id,
          name: '默认难度',
          rightScore: 1,
          wrongScore: 1,
          answerTime: 10,
        })
      }

      return defaultDifficulty.id
    },

    // 处理导入数据
    async processImportData(defaultDifficultyId) {
      // 确保获取最新的分组和难度列表
      await this.initQuestionBank()

      const subjectList = []

      for (const importItem of this.importData) {
        const { key, list } = importItem

        for (const rowData of list) {
          const processedData = this.handleImportData(rowData, key)
          const requestData = this.handleImportDataToSubmit(processedData, key, this.subjectGroupList, defaultDifficultyId)
          subjectList.push(requestData)
        }
      }

      return subjectList
    },

    // 分批导入题目
    async batchImportSubjects(subjectList) {
      const totalSubjects = subjectList.length
      const batchSize = 100
      const batchCount = Math.ceil(totalSubjects / batchSize)
      let importedCount = 0

      for (let i = 0; i < batchCount; i++) {
        const start = i * batchSize
        const end = Math.min((i + 1) * batchSize, totalSubjects)
        const batchData = subjectList.slice(start, end)

        await api.subject.batchAdd({
          wallId: this.currentWall.id,
          dataList: batchData,
        })

        importedCount += batchData.length
        this.progress = Math.floor((importedCount / totalSubjects) * 100)
        console.log(`导入进度：${this.progress}%`)
      }
    },

    // 完成导入
    async completeImport() {
      await this.initQuestionBank()
      this.resetImportState()
      this.$notify.success({ title: '成功', message: '导入数据成功' })
      this.$emit('close')
    },

    // 重置导入状态
    resetImportState() {
      this.importLoad = false
      this.importData = []
      this.groupList = []
      this.difficultyList = []
      this.fileName = ''
      this.progress = 0
    },

    // 处理导入数据
    handleImportData(importData, key) {
      const subject = this.getSubject(importData, key, 0)
      const options = this.getOption(importData, key, 0)
      const answer = this.getAnswer(importData, key, 0)
      const comment = this.getComment(importData, key, 0)
      const group = this.getGroup(importData, key)
      const difficulty = this.getDifficulty(importData, key)

      return {
        subject: subject || null,
        options: options || null,
        answer: answer || null,
        comment: comment || '',
        group: group || null,
        difficulty: difficulty || null,
      }
    },

    // 处理导入数据为提交数据
    handleImportDataToSubmit({ subject, options, answer, comment, group, difficulty }, type, subjectGroupList, defaultDifficultyId) {
      const subjectContent = {
        type: 'TEXT',
        content: subject,
        resource: '',
      }

      // 查找匹配的分组ID
      let groupId = this.subjectGroupList[0]?.id || null
      if (group && subjectGroupList.length > 0) {
        const matchedGroup = subjectGroupList.find((g) => g.name.toLowerCase() === group.toLowerCase())
        groupId = matchedGroup ? matchedGroup.id : groupId
      }

      // 查找匹配的难度ID
      let difficultyId = defaultDifficultyId
      if (difficulty && this.subjectDifficultyList.length > 0) {
        const matchedDifficulty = this.subjectDifficultyList.find((d) => d.name.toLowerCase() === difficulty.toLowerCase())
        difficultyId = matchedDifficulty ? matchedDifficulty.id : difficultyId
      }

      return {
        wallId: this.currentWall.id,
        type,
        subjectContent: JSON.stringify(subjectContent),
        options: this.formatOptions(options, answer),
        answer: this.convertNumberToLetter(answer),
        remark: comment || '',
        difficultyId,
        groupId,
      }
    },

    // 将数字转换为字母
    convertNumberToLetter(answer) {
      if (!answer || !Array.isArray(answer)) {
        return ''
      }
      return answer
        .map((number) => {
          const num = Number(number)
          if (num < 1 || num > 26) {
            return 'Invalid input'
          }
          return String.fromCharCode(64 + num)
        })
        .join(',')
    },

    // 格式化选项
    formatOptions(options, answer = []) {
      if (!options || !Array.isArray(options)) {
        return JSON.stringify([
          { img: '', rightAnswer: 'N', title: '' },
          { img: '', rightAnswer: 'N', title: '' },
        ])
      }

      const formattedOptions = options
        .filter((item) => item && item.trim())
        .map((item, index) => ({
          img: '',
          rightAnswer: answer && answer.includes((index + 1).toString()) ? 'Y' : 'N',
          title: item.trim() || '',
        }))

      return JSON.stringify(formattedOptions)
    },

    // 获取题目内容
    getSubject(item, key, index) {
      const subject = item[0] ? item[0].toString().trim() : ''

      if (!subject) {
        throw new Error(`${this.getErrorPrefix(key, index)}题目不能为空`)
      }

      if (subject.length > 500) {
        throw new Error(`${this.getErrorPrefix(key, index)}题目不能超过500个字符`)
      }

      return subject
    },

    // 获取选项
    getOption(item, key, index) {
      // 从列1到列6获取选项，最多6个选项
      const options = item.slice(1, 7).map((option) => (option ? option.toString().trim() : ''))

      // 过滤掉空选项
      const validOptions = options.filter(Boolean)

      if (validOptions.length < 2) {
        throw new Error(`${this.getErrorPrefix(key, index)}选项至少有两个`)
      }

      if (validOptions.some((option) => option.length > 20)) {
        throw new Error(`${this.getErrorPrefix(key, index)}选项不能超过20个字符`)
      }

      return validOptions
    },

    // 获取答案
    getAnswer(item, key, index) {
      const answer = item[7] ? item[7].toString().trim() : ''

      if (!answer) {
        throw new Error(`${this.getErrorPrefix(key, index)}答案不能为空`)
      }

      // 单选题的时候答案只能有一个
      if (key === 'RADIO' && answer.split(/[，,]/).length > 1) {
        throw new Error(`${this.getErrorPrefix(key, index)}单选题答案只能有一个`)
      }

      // 答案只能是数字 1-6 且不能重复，用逗号隔开
      if (!/^[1-6，,]+$/.test(answer)) {
        throw new Error(`${this.getErrorPrefix(key, index)}正确答案只能是数字 1-6 且不能重复,使用逗号隔开`)
      }

      // 检查答案是否引用了不存在的选项
      const options = this.getOption(item, key, index)
      const answerNumbers = answer.split(/[，,]/).map((num) => parseInt(num.trim()))

      // 检查每个答案编号是否超出了实际选项数量
      for (const num of answerNumbers) {
        if (num > options.length) {
          throw new Error(`${this.getErrorPrefix(key, index)}答案${num}超出了实际选项数量${options.length}`)
        }
      }

      return answer.split(/[，,]/).map((item) => item.trim())
    },

    // 获取注释
    getComment(item, key, index) {
      const comment = item[8] ? item[8].toString().trim() : ''

      if (comment && comment.length > 500) {
        throw new Error(`${this.getErrorPrefix(key, index)}注释不能超过500个字符`)
      }

      return comment
    },

    // 获取分组
    getGroup(item, key) {
      const group = item[9] ? item[9].toString().trim() : ''
      return group || null
    },

    // 获取难度
    getDifficulty(item, key) {
      const difficulty = item[10] ? item[10].toString().trim() : ''
      return difficulty || null
    },

    // 获取错误提示前缀
    getErrorPrefix(value, index) {
      const keyIndex = Object.values(this.questionTypeMap).findIndex((val) => val === value)
      if (keyIndex === -1) {
        throw new Error(`找不到匹配的值: ${value}`)
      }
      return `${Object.keys(this.questionTypeMap)[keyIndex]}第${(index || 0) + 1}题：`
    },

    handleClose() {
      this.$emit('close')
      this.resetImportState()
      this.fileParsingLoad = false
    },
  },
}
</script>
<style scoped lang="scss">
.export-container {
  padding-top: 15px;
  font-size: 14px;
  color: #333;
}

.step-list {
  padding-left: 50px;

  .step-item {
    width: 100%;
    position: relative;
    padding-left: 12px;
    padding-bottom: 28px;
    box-sizing: border-box;
    border-left: 1px solid #a7afbf;

    .step-title {
      margin-top: -10px;
      margin-bottom: 15px;

      .tip-text {
        color: #666;
      }
    }

    &::before {
      content: '';
      display: block;
      width: 7px;
      height: 7px;
      margin-left: -16px;
      border-radius: 50%;
      background-color: #a7afbf;
    }

    &:last-child {
      border-left: none;
      padding-bottom: 0;

      &::before {
        margin-left: -15px;
      }
    }
  }
}

.upload-success-tip {
  line-height: 32px;

  i {
    padding-right: 8px;
  }

  .tip-text {
    color: #666;
  }
}

.import-progress {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;

  p {
    margin: 0 0 10px 0;
    color: #606266;
    font-size: 14px;
  }
}
</style>
