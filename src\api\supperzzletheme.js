import api from './api'

export default {
  read: postData => api.fetchBaseData('/pro/hxc/prosupperzzletheme/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prosupperzzletheme/update.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prosupperzzletheme/list.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/prosupperzzletheme/batch.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/prosupperzzletheme/add.htm', postData),
}
