<template>
  <div class="prize-box">
    <div class="tit">
      <el-checkbox v-model="featureConfig.prizeReuse" true-label="Y" false-label="N" :disabled="disabled"> 允许所有轮次使用该奖品设置 </el-checkbox>
      <el-button type="primary" :disabled="disabled" @click="addPrize">添加奖品</el-button>
    </div>
    <el-table :data="nowAwardList">
      <el-table-column label="奖品图片">
        <template slot-scope="scope">
          <div>
            <img class="prizepic" v-if="!scope.row.awardImg" src="~@/assets/wall/interact/prize.png" />
            <img class="prizepic" v-else v-hiimg="scope.row.awardImg" />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="awardName" label="奖项名称"></el-table-column>
      <el-table-column label="发奖范围" :formatter="formatRank"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button class="fred" :class="{ ban: disabled }" :disabled="disabled" type="text" @click="del(scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="设置奖品" width="600px" :visible.sync="prizeDialog" :close-on-click-modal="false" @open="prizeSet">
      <div class="prize-set">
        <div class="prize-pic">
          <hi-upload-img list-type="picture-card" :action="$hi.url.upload" :on-success="(v) => (awardImg = v.data.url)" :show-file-list="false">
            <img v-if="!awardImg" src="~@/assets/wall/interact/prize.png" />
            <img v-else v-hiimg="awardImg" />
            <p>上传奖品图</p>
          </hi-upload-img>
        </div>
        <div class="prize-detail">
          <el-form :rules="rules" :model="awardObj" ref="awardObj" label-width="100px">
            <el-form-item label="奖项名称：" prop="awardName">
              <el-input v-model.trim="awardObj.awardName"></el-input>
            </el-form-item>
            <div class="p-relative">
              <el-form-item label="发奖范围：" prop="awardSatrtRanking">
                <div>
                  <el-select v-if="isTugwar" class="w-100" v-model="awardObj.awardScope" placeholder="请选择">
                    <el-option v-for="item in parzetarOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                  </el-select>
                  <div class="a">
                    <el-input-number
                      controls-position="right"
                      class="w82"
                      v-model="awardObj.awardSatrtRanking"
                      :min="1"
                      :precision="0"
                    ></el-input-number>
                    <span class="apart">~</span>
                  </div>
                </div>
              </el-form-item>
              <el-form-item class="rank-end" label="" prop="awardEndRanking">
                <el-input-number
                  controls-position="right"
                  class="w82"
                  v-model.trim="awardObj.awardEndRanking"
                  :disabled="awardObj.last"
                  :min="awardObj.awardSatrtRanking"
                  :precision="0"
                ></el-input-number>
                <span class="m12">名</span>
                <el-checkbox v-model="awardObj.last">至最后一名</el-checkbox>
              </el-form-item>
            </div>
            <el-form-item v-if="isTugwar" label="领奖说明" prop="awardRemark">
              <el-input type="textarea" :rows="2" placeholder="请输入领奖说明" v-model.trim="awardObj.awardRemark"> </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="prizeDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirm()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
/**
 * 奖品设置和活动分开，奖品设置依赖活动
 * 1，从接口读取奖品列表
 * 2，复制一份进行展示操作
 * 3，对比原始数据，确定新增和删除项目
 * 4，在外部调用接口更新奖品设置
 */

import { Hi } from '@/libs/common'
import api from '@/api'
const validateName = (rule, value, callback) => {
  if (!value) {
    callback(new Error('奖项名称不能为空'))
  } else {
    callback()
  }
}
const parzetarOptions = [
  { label: '获胜方', value: 'success' },
  { label: '失败方', value: 'lose' },
]
export default {
  props: {
    config: {
      default: () => ({}),
    },
    disabled: {
      default: false,
    },
  },
  inject: {
    elForm: {
      default: () => ({}),
    },
  },
  data() {
    return {
      Hi,
      parzetarOptions,
      awardList: [], // 原始数据
      nowAwardList: [], //展示数据
      featureConfig: Hi.Object.copy(this.config),
      prizeDialog: false,
      awardImg: '',
      awardObj: {
        awardName: '一等奖',
        awardSatrtRanking: 1,
        awardEndRanking: '',
        last: false,
        awardScope: 'success',
        awardRemark: '请及时联系主办方兑奖',
      },
      rules: {
        awardName: [{ required: 'true', validator: validateName, trigger: 'blur' }],
        awardSatrtRanking: [{ required: 'true', validator: this.validateStart, trigger: 'blur' }],
        awardEndRanking: [{ validator: this.validateEnd, trigger: 'blur' }],
        awardRemark: [{ required: true, message: '领奖说明不能为空', trigger: ['blur', 'change'] }],
      },
    }
  },
  computed: {
    apiName() {
      return this.$route.name.replace('wall-interact-', '') + 'awards'
    },
    idName() {
      return this.$route.name.replace('wall-interact-', '') + 'Id'
    },
    isTugwar() {
      return this.$route.name.replace('wall-interact-', '') === 'tugwar'
    },
  },
  watch: {
    awardList: {
      immediate: true,
      async handler(v) {
        if (v.length) {
          this.nowAwardList = Hi.Object.copy(this.awardList)
        } else {
          this.nowAwardList = []
        }
      },
    },
    nowAwardList: {
      deep: true,
      handler(v, o) {
        if (!o.length && v.length) {
          return
        }
        let addOption = []
        let delOption = []
        let nowConfig = {}
        let awardObj = {}
        v.forEach((item) => {
          if (!item.id) {
            addOption.push(item)
          } else {
            nowConfig[item.id] = item
          }
        })
        this.awardList.length &&
          this.awardList.forEach((item) => {
            if (!nowConfig[item.id]) {
              delOption.push(item.id)
            }
          })
        awardObj.add = addOption
        awardObj.del = delOption
        this.awardChange.call(this, awardObj)
      },
    },
    'featureConfig.prizeReuse'(v) {
      this.$emit('configChange', { prizeReuse: v })
    },
  },
  methods: {
    addPrize() {
      if (this.isTugwar) {
        let res = this.elForm.verify()
        if (res) this.prizeDialog = true
      } else {
        this.prizeDialog = true
      }
    },
    awardChange(awardObj) {
      this.$emit('awardChange', awardObj)
    },
    validateStart(rule, value, callback) {
      if (!value) {
        callback(new Error('起始名次不能为空'))
      }
      callback()
    },
    validateEnd(rule, value, callback) {
      if (!value) {
        callback(new Error('终止名次不能为空'))
      }
      if (Hi.Math.accSub(value, this.awardObj.awardSatrtRanking) < 0) {
        callback(new Error('发奖范围设置错误'))
      }
      let len = this.nowAwardList.length
      let prizeEnd = this.awardObj.awardEndRanking
      let prizeStart = this.awardObj.awardSatrtRanking
      let isLast = 'N'
      len && (isLast = this.nowAwardList[len - 1].lastSwitch)
      for (var i = 0; i < len; i++) {
        let start = Number(this.nowAwardList[i].awardSatrtRanking)
        let end = Number(this.nowAwardList[i].awardEndRanking)
        if (isLast === 'Y') {
          callback(new Error(`已存在发奖范围在第${this.nowAwardList[len - 1].awardSatrtRanking}名~最后一名的奖项，请修改发奖范围`))
        }
        if (
          (Hi.Math.accSub(prizeStart, start) <= 0 && Hi.Math.accSub(prizeEnd, start) >= 0) ||
          (Hi.Math.accSub(prizeStart, start) >= 0 && Hi.Math.accSub(prizeEnd, end) <= 0) ||
          (Hi.Math.accSub(prizeStart, end) <= 0 && Hi.Math.accSub(prizeEnd, end) >= 0)
        ) {
          if (start === end) {
            callback(new Error(`已存在发奖范围在第${start}名的奖项，请修改发奖范围`))
            return
          } else {
            callback(new Error(`已存在发奖范围在第${start}~${end}名之间的奖项，请修改发奖范围`))
            return
          }
        }
      }
      callback()
    },
    formatRank(r, c, v) {
      let start = r.awardSatrtRanking
      let end = r.awardEndRanking
      if (r.lastSwitch === 'Y') {
        return `第 ${start} ~ 最后一名`
      } else {
        if (start === end) {
          return `第 ${start} 名`
        } else {
          return `第 ${start} ~ ${end} 名`
        }
      }
    },
    // dialog open 事件
    prizeSet() {
      this.awardImg = ''
      this.awardObj.last = false
      let list = this.nowAwardList
      let len = list.length
      if (!len) {
        this.awardObj.awardSatrtRanking = 1
        this.awardObj.awardEndRanking = 1
      } else {
        this.awardObj.awardSatrtRanking = Number(list[len - 1].awardEndRanking) + 1
        this.awardObj.awardEndRanking = this.awardObj.awardSatrtRanking
      }
    },
    // 删除
    async del(index) {
      let start = this.nowAwardList[index].awardSatrtRanking
      let end = this.nowAwardList[index].awardEndRanking
      let isLast = this.nowAwardList[index].lastSwitch
      let hint
      if (isLast === 'Y') {
        hint = `删除后第 ${start} ~ 最后一名将无法获奖`
      } else {
        hint = start === end ? `删除后第 ${start} 名将无法获奖` : `删除后第 ${start} ~ ${end} 名将无法获奖`
      }
      await this.$confirm(`${hint}确定要删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      try {
        if (this.featureConfig.id) {
          await api.moneyv3awards.delete({
            where: {
              moneyv3Id: this.featureConfig.id,
              id: this.nowAwardList[index].id,
              wallId: this.featureConfig.wallId,
            },
          })
          await this.readList.call(this)
          this.$emit('awardChange')
        } else {
          this.nowAwardList.splice(index, 1)
        }
      } catch (e) {
        console.log(e)
      }
    },
    // 添加
    async confirm() {
      await this.$refs.awardObj.validate()
      let awardObj = {
        awardImg: this.awardImg,
        awardName: this.awardObj.awardName,
        awardSatrtRanking: Number(this.awardObj.awardSatrtRanking),
        awardEndRanking: Number(this.awardObj.awardEndRanking),
        lastSwitch: this.awardObj.last ? 'Y' : 'N',
        awardType: 'KIND',
      }
      this.isTugwar && (awardObj.awardScope = this.awardObj.awardScope)
      if (this.featureConfig.id) {
        // 调用接口
        awardObj.awardType = 'KIND'
        await api.moneyv3awards.add({
          wallId: this.featureConfig.wallId,
          moneyv3Id: this.featureConfig.id,
          ...awardObj,
        })
        await this.readList.call(this)
        this.$emit('awardChange')
      } else {
        this.nowAwardList.push(awardObj)
        this.nowAwardList.sort(function (a, b) {
          return a.awardSatrtRanking - b.awardSatrtRanking
        })
      }
      this.prizeDialog = false
      this.$refs.awardObj.resetFields()
    },
    async readList() {
      let where = {}
      where[this.idName] = this.featureConfig.id
      try {
        this.awardList = await api[this.apiName].list({ where, sort: { id: 'asc' } })
      } catch (e) {
        console.log(e)
        this.$notify.error({
          title: '错误',
          message: e ? e.msg || '获取奖品列表失败，请稍后重试！' : '获取奖品列表失败，请稍后重试！',
        })
      }
    },
  },
  async mounted() {
    if (this.featureConfig.id) {
      await this.readList.call(this)
    }
  },
}
</script>
<style scoped lang="stylus">
.p-relative
  position relative
.w-100
  margin-bottom 18px
  width 100px
.w82
  width 82px
.m12
  margin 0 12px
.fred
  color #f56c6c
.tit
  height 60px
  display flex
  align-items center
  justify-content space-between
.prizepic
  margin 12px 0
  width 70px
  height 70px
  object-fit contain
  object-position center
.ban
  color #999
.prize-set
  margin 15px 0
  display flex
  justify-content space-between
  .prize-pic
    width 86px
    height 86px
    border 1px solid #bfcbd9
    position relative
    img
      width 100%
      height 100%
      object-fit contain
      object-position center
    p
      position absolute
      left 0
      top 0
      width 100%
      height 100%
      background-color rgba(0, 0, 0, 0.5)
      color #fff
      line-height 86px
      visibility hidden
  .prize-pic:hover
    p
      visibility visible
  .prize-detail
    flex 1
.apart
  margin-right 4px
  display inline-block
  width 32px
  line-height 32px
  text-align center
.prize-box
  >>>.el-upload--picture-card
    width 86px
    height 86px
    border 0
.rank
  display flex
.rank-end
  >>>.el-form-item__content
    margin-left 0 !important
    position absolute
    bottom 18px
    left 220px
</style>
