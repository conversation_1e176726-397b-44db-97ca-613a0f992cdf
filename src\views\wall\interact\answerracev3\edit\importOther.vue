<template>
  <el-dialog width="600px" title="其他轮次导入" :visible="importantDialog" :close-on-click-modal="false" @close="closeDialog">
    <div class="top flex flex-a-c">
      <p>轮次选择：</p>
      <el-select v-model="answerraceId" placeholder="请选择" @change="renderPage">
        <el-option v-for="item in actList" :key="item.id" :label="item.title" :value="item.id"> </el-option>
      </el-select>
    </div>
    <div v-loading="loading">
      <el-table ref="multipleTable" :data="dataList" tooltip-effect="dark" max-height="300px" @selection-change="selectionChange">
        <el-table-column type="selection" width="100"></el-table-column>
        <el-table-column type="index" label="序号" width="120"></el-table-column>
        <el-table-column prop="address" label="题目">
          <template v-slot="{ row }">
            <div class="flex flex-a-c">
              <template v-if="row.subjectContent.resource">
                <p v-if="row.subjectContent.type === 'IMAGE'" class="title-img"><img :src="row.subjectContent.resource" /></p>
              </template>
              <p class="title-cont">{{ row.subjectContent.content }}</p>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" :disabled="disabledSave" @click="saveData">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import api from '@/api'
import { mapGetters } from 'vuex'
export default {
  name: 'addQUestion',
  props: {
    actConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      importantDialog: true,
      loading: false,
      answerraceId: null,
      actList: [], //轮次列表
      dataList: [],
      selQuesList: [], //选择的数据
    }
  },
  computed: {
    ...mapGetters({
      wallFlagObj: 'wall/getWallFlagObj',
    }),
    _wallFlag() {
      return this.$route.query.wallFlag
    },
    wall() {
      return this.wallFlagObj[this._wallFlag]
    },
    disabledSave() {
      return !this.selQuesList.length
    },
  },
  methods: {
    saveData() {
      this.$emit('save', this.selQuesList)
      this.closeDialog()
    },
    cancel() {
      this.closeDialog()
    },
    selectionChange(val) {
      this.selQuesList = val
    },
    async fetchActList() {
      try {
        const data = await api.answerracev3.list({
          where: {
            wallId: this.wall.id,
          },
        })
        this.actList = data.filter((item) => item.id !== this.actConfig.id)
        if (this.actList.length) {
          this.answerraceId = this.actList[0].id
        }
      } catch (err) {
        console.log(err)
      }
    },
    async renderPage() {
      try {
        if (this.answerraceId) {
          this.loading = true
          let data = await api.answerracev3subject.list({
            where: {
              answerracev3Id: this.answerraceId,
              wallId: this.wall.id,
            },
            sort: { sort: 'asc' },
          })
          data = data.map((item) => {
            return {
              ...item,
              wallId: this.wall.id,
              answerracev3Id: this.actConfig.id,
              subjectContent: JSON.parse(item.subjectContent),
            }
          })
          this.dataList = data
        }
      } catch (err) {
        console.log(err)
      }
      this.loading = false
    },
    closeDialog() {
      this.$emit('close')
    },
  },
  async mounted() {
    await this.fetchActList()
    await this.renderPage()
  },
}
</script>

<style lang="stylus" scoped>
.top
  margin-bottom 15px
.title-img
  width 40px
  height 40px
  img
    width 100%
    height 100%
    object-fit contain
    object-position center
.title-cont
  height min-content
  margin-left 5px
  width calc(100% - 45px)
  line-height 1.5
</style>
