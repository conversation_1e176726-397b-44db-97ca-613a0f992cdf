<template>
  <div class="gemelottery-set-basic">
    <el-tabs v-model="actTab" type="card" v-if="actConfig.gameMode === 'MOBILE'">
      <el-tab-pane label="大屏幕" name="SCREEN"></el-tab-pane>
      <el-tab-pane label="移动端" name="MOBILE"></el-tab-pane>
    </el-tabs>
    <el-form v-show="actTab === 'SCREEN'" :model="actConfig" label-width="120px">
      <el-form-item label="背景：">
        <div class="flex flex-a-c">
          <hi-upload-img :action="$hi.url.upload" :show-file-list="false" :on-success="uploadBgPic">
            <el-button type="primary">上传替换</el-button>
          </hi-upload-img>
          <el-button @click="setDefault('bgPic')" class="mrg-l-10">恢复默认</el-button>
          <div class="mrg-l-10">
            <hi-size-specification placement="top-start" :note="{ 推荐图片比例: '368*800', 大小: '小于5M', 格式: `jpg/bmp/png/gif/webp` }">
            </hi-size-specification>
          </div>
        </div>
      </el-form-item>
      <el-form-item :label="`${themeName}样式：`">
        <el-radio-group v-model="actConfig.size">
          <el-radio label="MIDDLE">中</el-radio>
          <el-radio label="BIG">大</el-radio>
          <el-radio label="SMALL">小</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="`${themeName}布局：`">
        <el-radio-group v-model="actConfig.position">
          <el-radio label="ALL">全屏</el-radio>
          <el-radio label="LEFT">居左</el-radio>
          <el-radio label="RIGHT">居右</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="中奖记录：">
        <div class="flex flex-a-c">
          <hi-switch active-value="Y" inactive-value="N" v-model="actConfig.showRecord" active-text="开启" inactive-text="关闭"></hi-switch>
          <p class="mrg-l-15">大屏中奖记录展示</p>
        </div>
      </el-form-item>
      <el-form-item label="奖池播报：" v-if="actConfig.position !== 'ALL'">
        <div class="flex flex-a-c">
          <hi-switch active-value="Y" inactive-value="N" v-model="actConfig.showAwards" active-text="开启" inactive-text="关闭"></hi-switch>
          <p class="mrg-l-15">大屏活动奖池展示</p>
        </div>
      </el-form-item>
      <el-form-item label="游戏规则：" v-if="actConfig.position !== 'ALL'">
        <div class="flex flex-a-c">
          <hi-switch active-value="Y" inactive-value="N" v-model="actConfig.showRule" active-text="开启" inactive-text="关闭"></hi-switch>
          <p class="mrg-l-15">大屏游戏规则展示</p>
        </div>
      </el-form-item>
      <el-form-item label="背景音乐：">
        <div class="flex flex-a-c mrg-b-10">
          <el-button @click="setDefault('bgMusic')" class="mrg-r-10">恢复默认</el-button>
          <hi-upload-audio :on-success="uploadBgMusic">
            <el-button type="primary">上传替换</el-button>
          </hi-upload-audio>
        </div>
        <hi-audio-player :src="actConfig.bgMusic"></hi-audio-player>
      </el-form-item>
      <el-form-item :label="`${themeName}开启音效：`">
        <div class="flex flex-a-c mrg-b-10">
          <el-button @click="setDefault('openingMusic')" class="mrg-r-10">恢复默认</el-button>
          <hi-upload-audio :on-success="uploadOpeningMusic">
            <el-button type="primary">上传替换</el-button>
          </hi-upload-audio>
        </div>
        <hi-audio-player :src="actConfig.openingMusic"></hi-audio-player>
      </el-form-item>
      <div class="advance-tit flex">
        <span>个性化{{ themeName }}元素，展现您的品牌</span>
      </div>
      <div class="position-r" style="padding-top: 30px">
        <el-form-item :label="`${themeName}元素：`">
          <el-radio-group v-model="actConfig.elementPicMode">
            <el-radio label="DEFAULT">默认</el-radio>
            <el-radio label="CUSTOM">自定义</el-radio>
          </el-radio-group>
          <div v-show="actConfig.elementPicMode === 'DEFAULT'" class="mrg-b-30 mrg-t-20 flex flex-a-c">
            <div class="mrg-r-20 element-pic" v-for="item in elementPicTheme" :key="item.key" @click="actConfig.elementPic = item.key">
              <img :src="item.image" class="img" :alt="item.name" />
              <div class="checked" v-show="actConfig.elementPic === item.key">
                <i class="hi-check"></i>
              </div>
            </div>
          </div>
          <div v-show="actConfig.elementPicMode === 'CUSTOM'">
            <div>最多上传5个{{ themeName }}元素</div>
            <hi-size-specification placement="top-start" :note="{ 推荐图片比例: '200 * 230', 大小: '小于2M', 格式: `jpg/bmp/png/gif/webp` }">
            </hi-size-specification>
            <div class="custom-element-pic">
              <div class="mrg-r-10 item" v-for="(item, index) in customElementPicArr" :key="index">
                <img class="img" :src="item.url" />
                <i class="el-dialog__close el-icon el-icon-close icon-del" @click="removeElementPic(index)"></i>
              </div>
              <hi-upload-img
                v-show="customElementPicArr.length < 5"
                :action="$hi.url.upload"
                :show-file-list="false"
                :on-success="uploadElementPic"
                multiple
              >
                <div class="add">
                  <i class="el-icon-plus"></i>
                </div>
              </hi-upload-img>
            </div>
          </div>
        </el-form-item>
        <hi-shade v-if="config.customizelogolimit === 'N'" vat-key="customizelogolimit" :wall="wall" @readConfig="fetchConfig" :actInfo="config" />
      </div>
    </el-form>
    <el-form v-show="actTab === 'MOBILE'" :model="actConfig" label-width="120px">
      <el-form-item label="背景：">
        <div class="flex flex-a-c">
          <hi-upload-img :action="$hi.url.upload" :show-file-list="false" :on-success="uploadMobileBgPic">
            <el-button type="primary">上传替换</el-button>
          </hi-upload-img>
          <el-button @click="setDefault('mobileBgPic')" class="mrg-l-10">恢复默认</el-button>
          <div class="mrg-l-10">
            <hi-size-specification mode="image"></hi-size-specification>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="样式：">
        <el-radio-group v-model="actConfig.mobileShowStyle">
          <el-radio label="ALL">总览</el-radio>
          <el-radio label="ONE">单个展示</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { Hi } from '@/libs/common'
import { mapActions, mapGetters, mapState } from 'vuex'

export default {
  name: 'WallMysteryboxEditDesign',
  data() {
    return {
      actTab: 'SCREEN',
      timefrequencyArr: [
        { label: '最多', value: 'ATMOST' },
        { label: '每天', value: 'EVERYDAY' },
      ],
      customElementPicArr: [],
    }
  },
  computed: {
    ...mapState({
      wall: (state) => state.MysteryboxEdit.wall,
      config: (state) => state.MysteryboxEdit.config,
      actConfig: (state) => state.MysteryboxEdit.actConfig,
    }),
    ...mapGetters({
      themeName: 'MysteryboxEdit/themeName',
      emptyPrizeNum: 'MysteryboxEdit/emptyPrizeNum',
      isOem: 'user/isOem',
    }),
    elementPicTheme() {
      let elementPicTheme = [
        {
          key: 'hxc',
          image: require('@/assets/wall/interact/mysterybox/element-pic-hxc.png'),
          name: 'hi现场',
        },
         {
          key: 'snake',
          image: require('@/assets/wall/interact/mysterybox/element-pic-snake.png'),
          name: '蛇年',
        },
        {
          key: 'tigger',
          image: require('@/assets/wall/interact/mysterybox/element-pic-tigger.png'),
          name: '虎年',
        },
        {
          key: 'tigger2',
          image: require('@/assets/wall/interact/mysterybox/element-pic-tigger2.png'),
          name: '虎年2',
        },
        {
          key: 'rabbit',
          image: require('@/assets/wall/interact/mysterybox/element-pic-rabbit.png'),
          name: '兔年',
        },
      ]
      return this.isOem ? elementPicTheme.filter((item) => item.key !== 'hxc') : elementPicTheme
    },
  },
  watch: {
    'actConfig.position'(val) {
      // 切换到全屏时隐藏奖池播报和游戏规则，切换到居左居右时打开
      this.actConfig.showAwards = val === 'ALL' ? 'N' : 'Y'
      this.actConfig.showRule = val === 'ALL' ? 'N' : 'Y'
    },
    customElementPicArr(val) {
      this.actConfig.customElementPic = JSON.stringify(val || [])
    },
  },
  methods: {
    ...mapActions('MysteryboxEdit', ['setEmptyAwards', 'setDefault', 'fetchConfig']),
    // 上传pc背景
    uploadBgPic(file) {
      const url = Hi.String.dealUrl(file.data.url)
      this.actConfig.bgPic = url
    },
    // 上传移动端背景
    uploadMobileBgPic(file) {
      const url = Hi.String.dealUrl(file.data.url)
      this.actConfig.mobileBgPic = url
    },
    // 上传背景音乐
    uploadBgMusic(file) {
      this.actConfig.bgMusic = Hi.String.dealUrl(file.data.url)
    },
    // 上传红包开启音效
    uploadOpeningMusic(file) {
      this.actConfig.openingMusic = Hi.String.dealUrl(file.data.url)
    },
    // 上传红包元素
    uploadElementPic(file) {
      const url = Hi.String.dealUrl(file.data.url)
      if (this.customElementPicArr.length < 5) {
        this.customElementPicArr.push({ url })
      }
    },
    removeElementPic(index) {
      this.customElementPicArr.splice(index, 1)
    },
  },
  async mounted() {
    this.customElementPicArr = JSON.parse(this.actConfig.customElementPic || '[]')
  },
}
</script>
<style scoped lang="stylus">
.advance-tit
  height 87px
  background-color #f2f6ff
  align-items center
  padding 0 34px
  border-bottom 1px solid #e4edff
  box-shadow 0 2px #e7efff

.element-pic
  position relative

  .img
    height 100px
    box-sizing border-box
    object-fit cover
    background-color #FED756

  .checked
    position absolute
    width 0
    height 0
    border-left 30px solid transparent
    top 0
    right 0
    border-top 30px solid #4886ff

    i
      position absolute
      top -26px
      right -2px
      font-size 12px
      color #fff

.custom-element-pic
  display flex
  align-items center
  position relative

  .item
    position relative
    height 80px

  .img, .add
    display inline-block
    width 80px
    height 80px
    object-fit contain
    border 1px dashed #4f84eb
    box-sizing border-box
    border-radius 2px
    background-color #f3f7fd

  .icon-del
    position absolute
    top 2%
    right 2%
    width 20px
    height 20px
    color white
    cursor pointer
    background: rgba(0, 0, 0, 0.4);
    text-align: center;
    line-height: 20px;
    border-radius: 20px;

  .add
    display flex
    align-items center
    justify-content center

    i
      color #447ced
      font-size 30px
</style>
