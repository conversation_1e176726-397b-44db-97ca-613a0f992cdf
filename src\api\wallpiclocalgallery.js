import api from './api'
export default {
  list: postData => api.fetchBaseData('/pro/hxc/prowallpiclocalgallery/list.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/prowallpiclocalgallery/page.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/prowallpiclocalgallery/batch.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prowallpiclocalgallery/update.htm', postData),
  setGroup: postData => api.fetchBaseData('/pro/hxc/prowallpiclocalgallery/setGroup.htm', postData),
}
