<template>
  <hi-wall-set-right class="wall-set-actConfig-box">
    <div class="edit-box" v-if="showPage">
      <div class="content flex">
        <wall-mysterybox-edit-preview :act-tab="actTab" :awardObj="awardObj" />
        <div class="set">
          <el-tabs v-model="actTab">
            <el-tab-pane label="基础设置" name="Basic">
              <wall-mysterybox-edit-basicset ref="basicset"></wall-mysterybox-edit-basicset>
            </el-tab-pane>
            <el-tab-pane label="奖品设置" name="Prize">
              <hi-prize-lottery
                :actConfig="actConfig"
                :disabled="disabled"
                @create="(cb) => cb(save)"
                @awardListChange="awardListChange"
              ></hi-prize-lottery>
            </el-tab-pane>
            <el-tab-pane label="样式设计" name="Design" lazy>
              <wall-mysterybox-edit-design></wall-mysterybox-edit-design>
            </el-tab-pane>
            <!--  移动端才显示 参与条件限制 -->
            <el-tab-pane label="参与条件限制" name="Limit" lazy v-if="actConfig.gameMode === 'MOBILE'">
              <div class="advance-tit flex position-r pad-r-34 mrg-b-20">
                <span>参与条件限制：可选择分组，分组内人员可按照设置参与互动</span>
              </div>
              <hi-limit :actConfig="actConfig" :wall="wall" :config="config" @configChange="configChange" @updateConfig="fetchConfig()"> </hi-limit>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <div slot="control" class="control">
      <el-button plain @click="backList" :disabled="false">返回</el-button>
      <el-button type="primary" @click="save()" :disabled="!enableSave" :loading="savelIng">保存</el-button>
    </div>
  </hi-wall-set-right>
</template>
<script>
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex'
import { env } from '@/libs/common'
import { wallMixin } from '@/libs/mixins'
import HiLimit from '@/views/wall/interact/common/limit.vue'
import WallMysteryboxEditDesign from '@/views/wall/interact/mysterybox/edit/design.vue'
import WallMysteryboxEditBasicset from '@/views/wall/interact/mysterybox/edit/basicset.vue'
import WallMysteryboxEditPrizeset from '@/views/wall/interact/mysterybox/edit/prizeset.vue'
import WallMysteryboxEditPreview from '@/views/wall/interact/mysterybox/edit/preview.vue'
import HiPrizeLottery from '@/views/wall/interact/common/prize/index.vue'

export default {
  name: 'WallMysteryboxEdit',
  mixins: [wallMixin],
  components: {
    WallMysteryboxEditPreview,
    WallMysteryboxEditPrizeset,
    WallMysteryboxEditBasicset,
    WallMysteryboxEditDesign,
    HiLimit,
    HiPrizeLottery,
  },
  data() {
    return {
      env,
      actTab: 'Basic',
      savelIng: false,
      isLoad: false,
      showPage: false,
      awardObj: {},
    }
  },
  computed: {
    ...mapState({
      actConfig: (state) => state.MysteryboxEdit.actConfig,
      awards: (state) => state.MysteryboxEdit.awards,
      config: (state) => state.MysteryboxEdit.config,
    }),
    ...mapGetters({
      themeName: 'MysteryboxEdit/themeName',
      enableSave: 'MysteryboxEdit/enableSave',
      payTotalCount: 'MysteryboxEdit/payTotalCount',
      defaultChange: 'MysteryboxEdit/defaultChange',
      emptyPrizeNum: 'MysteryboxEdit/emptyPrizeNum',
      awardsTotalNum: 'MysteryboxEdit/awardsTotalNum',
    }),
    disabled() {
      return ['IN', 'PAUSE', 'DATA_ING', 'FINISH'].includes(this.actConfig.activityState)
    },
  },
  methods: {
    ...mapMutations('MysteryboxEdit', ['setWall']),
    ...mapActions('MysteryboxEdit', ['fetchAllData', 'saveAllData', 'fetchConfig']),
    awardListChange(awardObj) {
      this.awardObj = awardObj
    },
    //保存
    async save() {
      // @ts-ignore
      await this.$refs.basicset.$refs.basicSetForm.validate()
      if (!this.actConfig.title) {
        this.$notify.error({ title: '错误', message: '标题不能为空' })
        return false
      }
      if (this.actConfig.elementPicMode === 'CUSTOM') {
        const customElementPic = JSON.parse(this.actConfig.customElementPic || '[]')
        if (customElementPic.length < 1) {
          this.$notify.error({ title: '错误', message: `自定义${this.themeName}元素最少上传1张` })
          return false
        }
      }
      let isEdit = !!this.actConfig.id
      try {
        if (this.payTotalCount) {
          // 红包充值
          let payObj = { tradeType: 'REDPACK_RECHARGE', payType: 'redpackpay' }
          let items = [
            {
              vatKey: 'redpackrecharge',
              vatName: '红包充值',
              vatPrice: this.payTotalCount,
              count: 1,
            },
          ]
          // @ts-ignore
          await this.$hi.pay({ payObj, items, type: 'order' })
        }
        await this.saveAllData()
        this.$notify.success({
          title: '成功',
          message: isEdit ? '更新成功' : `新增${this.themeName}抽奖成功`,
        })
      } catch (err) {
        console.log(err)
        if (err !== 'cancel') {
          this.$notify.error({
            title: '错误',
            message: err.msg || '提交失败！',
          })
        }
      }
    },
    configChange(obj) {
      for (const [k, v] of Object.entries(obj)) {
        this.$set(this.actConfig, k, v)
      }
    },
    async backList() {
      if (this.enableSave && this.defaultChange) {
        this.$confirm('是否保存您所做的更改？', '提示', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            await this.save()
            this.$router.replace({
              name: 'wall-interact-mysterybox',
              query: { wallFlag: this.$route.query.wallFlag },
            })
          })
          .catch((err) => {
            if (err === 'cancel') {
              this.$router.replace({
                name: 'wall-interact-mysterybox',
                query: { wallFlag: this.$route.query.wallFlag },
              })
            }
          })
      } else {
        this.$router.replace({
          name: 'wall-interact-mysterybox',
          query: { wallFlag: this.$route.query.wallFlag },
        })
      }
    },
    //初始化
    async initRouterData() {
      await this.fetchAllData({
        theme: this.$route.query.theme,
        mysteryboxId: this.$route.query.id,
      })
      await this.$nextTick()
      this.actConfig.gameMode = this.$route.query.gameMode
      this.actConfig.theme = this.$route.query.theme
      this.showPage = true
    },
  },
  async mounted() {
    // @ts-ignore
    await this.wallHandler()
    // @ts-ignore
    await this.setWall(this.wall)
    await this.initRouterData()
  },
}
</script>
<style scoped lang="stylus">
.wall-set-actConfig-box
  width 100%

.edit-box
  min-height 100%
  min-width 1100px
  padding 16px 0px 10px 40px !important
  background #fff
  box-sizing border-box

  .title
    margin-bottom 5px
    height 46px
    line-height 46px
    border-bottom 1px solid #ebeef5
    font-size 16px

  .content
    width 100%
    margin-bottom 10px

  .set
    flex 1
    margin-right 20px

  .advance-tit
    height 87px
    background-color #f2f6ff
    display flex
    align-items center
    padding-left 34px
    border-bottom 1px solid #e4edff
    box-shadow 0 2px #e7efff

    .mr-10
      margin-right 10px

  .blank
    height 22px

  .hint-blank
    height 5px
</style>
