<template>
  <div class="judge-box" v-loading="loading">
    <div class="judge-group flex flex-a-c flex-j-sb">
      <el-tabs type="border-card" v-model="judgeGroupTab">
        <el-tab-pane v-for="(item, index) in judgeGroupList" :label="item.name" :name="index.toString()" :key="item.id + item.name"></el-tab-pane>
      </el-tabs>
      <el-button type="primary" class="mrg-l-15 mrg-r-15" @click="judgeGroupDialog = true">分组管理</el-button>
    </div>
    <div v-if="curJudgeGroup">
      <div class="flex flex-a-c hint mrg-b-30 mrg-t-10">
        <i class="el-icon-warning"></i>
        所有轮次将使用以下相同的评委设置，请谨慎编辑！
      </div>
      <div class="flex mrg-b-20">
        <p>参评方式：</p>
        <el-radio-group v-model="curJudgeGroup.joinWay">
          <el-radio label="SCAN_CODE">扫码验证参评</el-radio>
          <el-radio label="SCAN_DIRECTLY">扫码直接参评</el-radio>
        </el-radio-group>
      </div>
      <div class="flex">
        <div class="code">
          <hi-qrcode :value="entryGuest(wall)" size="120" level="L"></hi-qrcode>
          <p>评委通道二维码</p>
          <a v-if="!ban" class="qrcode-down" :href="entryGuest(wall) | qrcode | downloadQrcode('qrcode')"> 下载二维码 </a>
          <el-button class="mar-b-40" v-else disabled type="primary">下载二维码</el-button>
        </div>
        <div>
          <div class="describe">
            <template v-if="curJudgeGroup.joinWay !== 'SCAN_CODE'">
              <p>评委通过<strong>微信</strong>扫码或打开链接进入评分点赞页面， 可直接对参赛者进行评分，<span class="fred">不验证评委身份</span></p>
            </template>

            <template v-else>
              <p>评委扫码或打开链接进入评分点赞页面， 验证通过后可进行评分。</p>
              <p class="fred mrg-t-5">测试活动最多支持添加2名评委，发布活动后最多支持添加1000名评委</p>
            </template>
            <p class="link">评委通道链接</p>
            <div class="flex">
              <el-input type="text" :value="entryGuest(wall)" ref="wallhref" readonly></el-input>
              <el-button plain type="info" :disabled="ban" @click="copy()">复制</el-button>
            </div>
          </div>
          <div class="mrg-t-20" v-if="curJudgeGroup.joinWay !== 'SCAN_CODE'">
            <!-- <p>评委数量</p> -->
            <div class="flex flex-a-c mrg-t-10">
              <!-- <el-input-number
                v-model="curJudgeGroup.maxNum"
                controls-position="right"
                :min="1"
                :max="curJudgeMaxNum"
                :precision="0"
              ></el-input-number> -->
              <span class="mrg-l-15 font-14 fred">测试活动最多支持添加2名评委，发布活动后最多支持添加3000名评委</span>
            </div>
          </div>
        </div>
      </div>
      <div class="judge-tit flex flex-j-fe" style="margin-top: -44px" v-if="curJudgeGroup.joinWay === 'SCAN_CODE'">
        <div>
          <el-button type="primary" @click="handleImportJudge">批量导入</el-button>
          <el-button type="primary" @click="handleAddJudge" :disabled="judgeListLength === 1000">添加评委 </el-button>
          <el-button @click="getMetters">物料下载</el-button>
          <el-button @click="exportTable" :loading="exportLoad">数据导出</el-button>
        </div>
      </div>
    </div>
    <el-table :data="_judgeList" border ref="table" class="mrg-t-10">
      <el-table-column label="头像" :width="150">
        <template slot-scope="scope">
          <div class="head-pic">
            <img :src="scope.row.headImg || require('@/assets/user/default.png')" />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="评委姓名">
        <template slot="header"> 姓名 </template>
      </el-table-column>
      <el-table-column prop="code" :width="362" label="验证码" v-if="curJudgeGroup.joinWay === 'SCAN_CODE'">
        <template slot="header" slot-scope="{}">
          <section class="flex flex-a-c">
            <span>验证码</span>
            <el-tooltip class="item" effect="dark" placement="top">
              <p slot="content"><span class="ques">验证码自定义输入，用来验证评委身份，不可重复。</span></p>
              <img class="ask" src="~@/assets/wall/interact/ask.png" />
            </el-tooltip>
          </section>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="206" align="center">
        <template slot-scope="scope">
          <div class="flex flex-a-c flex-j-c">
            <el-button type="text" v-if="curJudgeGroup.joinWay === 'SCAN_CODE'" class="mrg-lr-10" @click="editJudge(scope.$index)" :disabled="ban"
              >编辑
            </el-button>
            <el-button type="text" class="fred mrg-lr-10" @click="delJudge(scope.$index)" :disabled="ban">删除 </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :title="isEdit ? '编辑评委' : '添加评委'" :visible.sync="judgeDialog" width="600px" :close-on-click-modal="false">
      <div class="flex">
        <div class="judge-headpic" v-loading="uploading">
          <img v-hiimg="judgeObj.headImg" />
          <hi-upload-img
            list-type="picture-card"
            :action="$hi.url.upload"
            :show-file-list="false"
            :on-success="judegePicSuccess"
            :on-progress="(v) => (uploading = true)"
            :on-error="(v) => (uploading = false)"
          >
            <p class="upload">上传替换</p>
          </hi-upload-img>
          <div class="mrg-t-10">
            <hi-size-specification placement="top-start" :note="{ 推荐尺寸: `300*300(1:1)`, 大小: '小于2M', 格式: 'jpg/png/bmp' }">
            </hi-size-specification>
          </div>
        </div>
        <div class="mrg-l-40">
          <div class="judge-con flex-1 flex flex-a-c">
            <p class="flex flex-a-c flex-j-fe">
              <span class="fred">*</span>
              <span>姓名：</span>
            </p>
            <el-input v-model.trim="judgeObj.name" maxlength="10" placeholder="最多10个字"></el-input>
          </div>
          <div class="judge-con flex-1 flex flex-a-c">
            <p class="flex flex-a-c flex-j-fe">
              <span class="fred">*</span>
              <span>验证码：</span>
            </p>
            <el-input v-model.number="judgeObj.code" maxlength="11" placeholder="最多11个字符"></el-input>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click=";(judgeDialog = false), refreshJudge()">取 消</el-button>
        <el-button type="primary" :disabled="!judgeObj.name || !judgeObj.code" @click="saveJudge">保 存</el-button>
      </span>
    </el-dialog>
    <el-dialog title="评委分组管理" :visible.sync="judgeGroupDialog" width="600px" :close-on-click-modal="false">
      <el-button type="primary" @click="addJudgeGroup" :disabled="tempJudgeGroupList.length >= 10" class="mrg-b-20">新增分组 </el-button>
      <el-table :data="tempJudgeGroupList" border>
        <el-table-column prop="name" label="分组名称">
          <template slot-scope="scope">
            <el-input v-model="scope.row.name" :maxlength="10" />
          </template>
        </el-table-column>
        <el-table-column prop="weight" label="分组权重">
          <template slot-scope="scope">
            <div class="flex flex-a-c">
              <el-input-number :controls="false" :precision="0" :min="1" :max="1000" v-model="scope.row.weight" size="small"> </el-input-number>
              <span class="mrg-l-5">%</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="optiongroupId" label="评分项分组">
          <template slot-scope="scope">
            <el-select v-model="scope.row.optiongroupId">
              <el-option :key="item.id" :label="item.name" :value="item.id" v-for="item in gradeGroupList">
                {{ item.name }}
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" class="fred" @click="delJudgeGroup(scope)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="judgeGroupDialog = false">取 消</el-button>
        <el-button type="primary" @click="saveJudegeGroup" :disabled="!diffJudgeGroupList">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="物料下载"
      :visible.sync="matterDialog"
      width="690px"
      :close-on-click-modal="false"
      @closed="
        dataURL = ''
        batchScreenshots = []
        tempJudgeList = []
      "
    >
      <div
        v-loading="drawLoad"
        :element-loading-text="drawLoad ? `正在生成第${currentBatchIndex}/${totalBatches}批图片... ${screenshotProgress}%` : ''"
      >
        <!-- 分批截图结果展示 -->
        <div class="batch-screenshots">
          <div class="batch-header">
            <p class="matter-hint">共生成 {{ batchScreenshots.length }} 张图片，每张包含 {{ batchSize }} 个评委信息</p>
            <div class="batch-actions">
              <el-button type="primary" @click="downloadAllImages">下载全部图片</el-button>
            </div>
          </div>
          <div class="batch-list">
            <div v-for="screenshot in batchScreenshots" :key="screenshot.index" class="batch-item">
              <div class="batch-info">
                <h4>第{{ screenshot.index }}批 (评委{{ screenshot.range }})</h4>
                <p>包含{{ screenshot.count }}个评委</p>
                <el-button size="small" @click="downloadSingleImage(screenshot)">下载此图片</el-button>
              </div>
              <div class="batch-preview">
                <img :src="screenshot.dataURL" alt="预览图" @click="previewImage(screenshot.dataURL)" />
              </div>
            </div>
          </div>
        </div>

        <!-- 单张截图展示 -->
        <div>
          <div class="matter-con">
            <div class="imageWrapper" ref="imageWrapper">
              <img v-if="dataURL" class="real_pic" :src="dataURL" />
              <div v-if="!dataURL" class="matter-warp flex flex-w-w">
                <div class="flex flex-j-sb" v-for="item in tempJudgeList.length > 0 ? tempJudgeList : judgeList" :key="item.code">
                  <div class="info">
                    <p class="tips">{{ item.name }}</p>
                    <p class="code-info">验证码：{{ item.code }}</p>
                  </div>
                  <div class="code-container">
                    <hi-qrcode :value="entryGuest(wall)" size="96" level="L"></hi-qrcode>
                    <p>扫码后请输入</p>
                    <p>验证码进入评分</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <Hi-export
      v-if="dialogExport"
      name="MarkJudge"
      :judgeList.sync="judgeList"
      :guestgroupId="curJudgeGroup.id"
      @close="dialogExport = false"
    ></Hi-export>
  </div>
</template>

<script>
import { env, Hi, timer } from '@/libs/common'
import html2canvas from 'html2canvas'
import { exportMixin } from '@/libs/mixins'
import HiExport from './export.vue'
import { mapActions, mapMutations, mapState } from 'vuex'
import { previewImage } from '@/libs/common'

export default {
  name: 'MarkJudge',
  props: {
    wall: {
      type: Object,
      default: () => ({}),
    },
  },
  components: { HiExport },
  mixins: [exportMixin],
  data() {
    return {
      loading: true,
      judgeObj: {},
      judgeDialog: false,
      isEdit: false,
      drawLoad: false,
      dataURL: '',
      matterDialog: false,
      uploading: false,
      currentIndex: 0,
      judgeGroupDialog: false,
      tempJudgeGroupList: [],
      judgeGroupTab: 0,
      curJudgeGroup: {},
      judgeList: [],
      ban: false,
      dialogExport: false,
      // 分批截图相关
      batchScreenshots: [],
      currentBatchIndex: 0,
      totalBatches: 0,
      screenshotProgress: 0,
      batchSize: 50, // 每批处理50个
      tempJudgeList: [], // 临时存储当前批次数据，避免直接修改Vuex状态
    }
  },
  computed: {
    ...mapState('MarkEdit', ['config', 'allJudgeList', 'judgeGroupList', 'actConfig', 'gradeGroupList', 'resultCount']),
    diffJudgeGroupList() {
      return (
        this.tempJudgeGroupList.length !== this.judgeGroupList.length ||
        JSON.stringify(this.tempJudgeGroupList) !== JSON.stringify(this.judgeGroupList)
      )
    },
    curJudgeGroupId() {
      return this.curJudgeGroup.id
    },
    _judgeList() {
      return this.judgeList.filter((item) => (this.curJudgeGroup.joinWay === 'SCAN_CODE' ? !item.wxUserId : item.wxUserId))
    },
    isTest() {
      return this.wall.wallVersion && this.wall.wallVersion === 'test'
    },
    curJudgeMaxNum() {
      if (this.isTest) {
        return 2
      }
      return 3000
    },
    hasData() {
      return this.actConfig.id && this.resultCount > 0
    },
    judgeListLength() {
      return Object.values(this.allJudgeList).reduce((acc, cur) => acc + cur.filter((item) => item.code).length, 0)
    },
  },
  watch: {
    judgeGroupDialog(val) {
      if (val) {
        this.tempJudgeGroupList = Hi.Object.copy(this.judgeGroupList)
      }
    },
    judgeGroupTab: {
      immediate: true,
      handler(val) {
        const curJudgeGroup = this.judgeGroupList[val]
        if (!curJudgeGroup) {
          setTimeout(() => {
            this.judgeGroupTab = '0'
          }, 1000)
          return
        }
        this.curJudgeGroup = Hi.Object.copy(curJudgeGroup)
        const judgeList = this.allJudgeList && this.allJudgeList[curJudgeGroup.id]
        this.judgeList = judgeList || []
      },
    },
    judgeList: {
      deep: true,
      handler(val) {
        if (!this.curJudgeGroupId) return
        const obj = this.allJudgeList
        obj[this.curJudgeGroupId] = val
        this.setAllJudgeList(obj)
      },
    },
    judgeGroupList: {
      handler(val) {
        let judgeGroupTab = this.judgeGroupTab
        if (judgeGroupTab > val.length - 1) {
          judgeGroupTab = val.length - 1
          this.judgeGroupTab = judgeGroupTab || '0'
        }
        this.curJudgeGroup = val[judgeGroupTab] || {}
      },
    },
    'curJudgeGroup.joinWay'(val, oldVal) {
      if (!val || !oldVal) return
      const list = Hi.Object.copy(this.judgeGroupList) || []
      if (!list.length) return
      list.splice(this.judgeGroupTab, 1, {
        ...this.curJudgeGroup,
        joinWay: val,
      })
      this.setJudgeGroupList(list)
      this.saveJudgeGroupList()
    },
    'curJudgeGroup.maxNum'(val, oldVal) {
      if (!val || !oldVal) return
      const list = Hi.Object.copy(this.judgeGroupList) || []
      if (!list.length) return
      list.splice(this.judgeGroupTab, 1, {
        ...this.curJudgeGroup,
        maxNum: val,
      })
      this.setJudgeGroupList(list)
      this.saveJudgeGroupList()
    },
  },
  methods: {
    ...mapActions('MarkEdit', ['saveJudgeGroupList', 'initJudgeGroup', 'initGradeGroup', 'fetchJudgeList', 'initAllGroup']),
    ...mapMutations('MarkEdit', ['setJudgeGroupList', 'setAllJudgeList']),
    previewImage,
    copy() {
      this.$refs.wallhref.select()
      document.execCommand('Copy')
      this.$message({
        message: '复制成功',
        type: 'success',
      })
    },
    entryGuest(wall) {
      const scanType = {
        SCAN_CODE: 0,
        SCAN_DIRECTLY: 1,
      }
      const { joinWay = 'SCAN_CODE', id = '' } = this.judgeGroupList[this.judgeGroupTab] || {}
      return `${env.wwwdomain}pro/mobile/index.html?/#/judges/mark/index.html?mobileFlag=${wall.mobileFlag}&scanType=${scanType[joinWay]}&markGroupId=${id}`
    },
    async refreshJudge() {
      this.judgeObj = {
        headImg: 'https://res3.hixianchang.com/qn/vote/default.png',
        name: '',
        code: '',
        wallId: this.wall.id,
      }
    },
    async toImg() {
      return new Promise((resolve, reject) => {
        // 检查DOM元素是否存在
        const element = this.$refs.imageWrapper
        if (!element) {
          reject(new Error('截图元素不存在'))
          return
        }

        // 检查元素是否在DOM中
        if (!document.body.contains(element)) {
          reject(new Error('截图元素未挂载到DOM'))
          return
        }

        html2canvas(element, {
          backgroundColor: null,
          width: 610,
          useCORS: true,
          scale: 1,
          logging: false, // 关闭日志避免控制台污染
        })
          .then((canvas) => {
            try {
              // 使用JPEG格式和压缩质量来减少文件大小
              const dataURL = canvas.toDataURL('image/jpeg', 0.8)
              resolve(dataURL)
            } catch (error) {
              reject(error)
            }
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    async getMetters() {
      this.matterDialog = true
      if (!this.judgeList.length) {
        return
      }

      // 检查数据量，决定是否分批处理
      await this.generateBatchScreenshots()
    },

    // 分批生成截图
    async generateBatchScreenshots() {
      this.batchScreenshots = []
      this.totalBatches = Math.ceil(this.judgeList.length / this.batchSize)
      this.currentBatchIndex = 0
      this.screenshotProgress = 0
      this.drawLoad = true

      // 保存原始数据
      const originalJudgeList = [...this.judgeList]

      try {
        for (let i = 0; i < this.totalBatches; i++) {
          this.currentBatchIndex = i + 1
          this.screenshotProgress = Math.round((i / this.totalBatches) * 100)

          const start = i * this.batchSize
          const end = Math.min(start + this.batchSize, this.judgeList.length)
          const batchData = originalJudgeList.slice(start, end)

          // 使用tempJudgeList避免直接修改Vuex状态
          this.tempJudgeList = batchData
          await this.$nextTick()

          // // 等待DOM完全更新和二维码渲染
          await new Promise((resolve) => setTimeout(resolve, 500))

          // 确保元素存在且可见
          const element = this.$refs.imageWrapper
          if (!element) {
            throw new Error(`第${i + 1}批截图元素不存在`)
          }
          if (!document.body.contains(element)) {
            throw new Error(`第${i + 1}批截图元素未挂载到DOM`)
          }

          // 检查是否有内容需要截图
          const matterWarp = element.querySelector('.matter-warp')
          if (!matterWarp || matterWarp.children.length === 0) {
            throw new Error(`第${i + 1}批没有可截图的内容`)
          }

          const dataURL = await this.toImg()
          this.batchScreenshots.push({
            index: i + 1,
            dataURL,
            count: batchData.length,
            range: `${start + 1}-${end}`,
          })
        }

        this.screenshotProgress = 100
        this.$notify.success({
          title: '成功',
          message: `已生成${this.totalBatches}张图片，每张包含最多${this.batchSize}个评委信息`,
        })
      } catch (error) {
        console.error('批量截图失败:', error)
        this.$notify.error({ title: '错误', message: `批量截图失败: ${error.message}` })
      } finally {
        // 清空临时数据
        this.tempJudgeList = []
        this.drawLoad = false
      }
    },

    // 下载单张图片
    downloadSingleImage(screenshot) {
      const link = document.createElement('a')
      link.download = `评委物料_第${screenshot.index}批_${screenshot.range}.jpg`
      link.href = screenshot.dataURL
      link.click()
    },

    // 下载所有图片
    downloadAllImages() {
      this.batchScreenshots.forEach((screenshot, index) => {
        setTimeout(() => {
          this.downloadSingleImage(screenshot)
        }, index * 200) // 延迟下载避免浏览器阻止
      })
    },
    async judegePicSuccess(file) {
      this.judgeObj.headImg = Hi.String.dealUrl(file.data.url)
      await this.$nextTick()
      this.uploading = false
    },
    async editJudge(index) {
      this.judgeObj = Hi.Object.copy(this.judgeList[index])
      this.currentIndex = index
      this.isEdit = true
      this.judgeDialog = true
    },
    async delJudge(index) {
      await this.$confirm('提示', {
        title: '提示',
        message: '删除将清空该评委所有轮次评分数据',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false,
      })
      this.judgeList.splice(index, 1)
    },
    async exportData() {
      await timer(200)
      return { name: '评委', data: this.judgeList }
    },
    async saveJudge() {
      if (!/^\d+$/.test(this.judgeObj.code)) {
        this.$notify.error({ title: '错误', message: '验证码必须为数字' })
        return
      }
      if (this.isEdit) {
        let nums = []
        Object.keys(this.allJudgeList).forEach((guestgroupId) => {
          if (this.judgeObj.guestgroupId === guestgroupId) {
            nums = nums.concat(this.judgeList.filter((_, index) => index !== this.currentIndex).map((item) => item.code + ''))
            return
          }
          nums = nums.concat(this.allJudgeList[guestgroupId].map((item) => item.code + ''))
        })
        if (nums.includes(this.judgeObj.code.toString())) {
          this.$notify.error({ title: '错误', message: '验证码已存在' })
          return
        }
        if (this.judgeObj.id) {
          const updatedJudge = this.judgeList.find((item) => item.id === this.judgeObj.id)
          if (updatedJudge) {
            updatedJudge.code = this.judgeObj.code.toString()
            updatedJudge.headImg = this.judgeObj.headImg
            updatedJudge.name = this.judgeObj.name
          }
        } else {
          let arr = ['headImg', 'name', 'code']
          arr.forEach((key) => {
            this.judgeList[this.currentIndex][key] = this.judgeObj[key].toString()
          })
        }
        this.judgeList[this.currentIndex].guestgroupId = this.curJudgeGroup.id
      } else {
        let nums = []
        Object.keys(this.allJudgeList).forEach((key) => {
          nums = nums.concat(this.allJudgeList[key].map((item) => item.code + ''))
        })
        if (nums.includes(this.judgeObj.code.toString())) {
          this.$notify.error({ title: '错误', message: '验证码已存在' })
          return
        }
        let len = this.judgeList.length
        if (len >= 1000) {
          this.$notify.error({ title: '错误', message: '最多添加1000个评委' })
          return
        }
        this.judgeObj.code = this.judgeObj.code.toString()
        this.judgeObj.guestgroupId = this.curJudgeGroup.id
        this.judgeList.push(this.judgeObj)
      }
      this.judgeDialog = false
      await this.refreshJudge()
    },
    async addJudgeGroup() {
      this.tempJudgeGroupList.push({
        name: '',
        weight: 100,
        joinWay: 'SCAN_CODE',
        maxNum: 200,
        wallId: this.wall.id,
      })
    },
    async delJudgeGroup({ $index: index }) {
      if (this.tempJudgeGroupList.length === 1) {
        await this.$notify.info({ title: '提示', message: '最少要保留1个分组' })
        return
      }
      this.tempJudgeGroupList.splice(index, 1)
    },
    async saveJudegeGroup() {
      if (this.hasData) {
        this.$notify.error({ title: '错误', message: '需清除本轮数据后操作' })
        return
      }
      // 判断是否有不符合规则的数据
      const list = this.tempJudgeGroupList.filter((item) => item.name)
      if (list.length !== this.tempJudgeGroupList.length) {
        this.$notify.error({ title: '错误', message: '分组名称不能为空' })
        return
      }
      const list2 = this.tempJudgeGroupList.filter((item) => item.optiongroupId)
      if (list2.length !== this.tempJudgeGroupList.length) {
        this.$notify.error({ title: '错误', message: '评分项分组必须选择' })
        return
      }
      try {
        const batchArr = Hi.Object.batchSave(this.judgeGroupList, this.tempJudgeGroupList)
        if (batchArr && batchArr.find((item) => item.type === 'del')) {
          await this.$confirm('提示', {
            title: '提示',
            message: '删除将清空该分组内评委所有轮次产生的评分数据',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            closeOnClickModal: false,
          })
        }
        await this.setJudgeGroupList(this.tempJudgeGroupList)
        await this.saveJudgeGroupList()
        await this.$nextTick()
        this.judgeGroupDialog = false
      } catch (err) {
        if (err === 'cancel') return
        console.log(err)
        this.$notify.error({ title: '错误', message: err.msg })
      }
    },
    async handleAddJudge() {
      if (this.judgeListLength >= this.curJudgeMaxNum) {
        this.$notify.warning({ title: '警告', message: this.isTest ? '请发布活动添加更多评委' : '评委数量已达上限' })
        return
      }
      this.judgeDialog = true
      await this.refreshJudge()
      this.isEdit = false
    },
    async handleImportJudge() {
      if (this.isTest) {
        this.$notify.warning({ title: '警告', message: '请发布活动添加更多评委' })
        return
      }
      if (this.judgeListLength >= this.curJudgeMaxNum) {
        this.$notify.warning({ title: '警告', message: this.isTest ? '请发布活动添加更多评委' : '评委数量已达上限' })
        return
      }
      this.dialogExport = true
    },
  },
  async mounted() {
    this.loading = true
    await this.initAllGroup()
    this.loading = false
  },
}
</script>

<style scoped lang="stylus">
.mrg-lr-10 {
  margin: 0 10px;
}

.mar-b-14 {
  margin-bottom: 14px;
}

.judge-box {
  width: 100%;

  .judge-group {
    min-height: 40px;
    padding-bottom: 1px;
    background-color: #F5F7FA;
    border: 1px solid #DCDFE6;
    border-bottom: none;

    /deep/ .el-tabs--border-card {
      box-shadow: none;
      border: none;
      width: 90%;

      > .el-tabs__header {
        border-bottom: none;
      }
    }

    /deep/ .el-tabs__content {
      display: none;
    }
  }

  .hint {
    color: #ed3e3a;
    transform: translateY(9px);

    i {
      margin-right: 6px;
      font-size: 16px;
    }
  }

  .code {
    margin-right: 20px;
    width: 120px;

    .code-box {
      width: 120px;
      height: 120px;
      background-color: rgba(0, 0, 0, 0.4);
      color: #fff;
    }

    p {
      height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      color: #333;
    }

    .qrcode-down {
      margin: 0 auto 18px;
      display: block;
      width: 106px;
      height: 30px;
      font-size: 12px;
      border-radius: 3px;
      color: #fff;
      text-align: center;
      background-color: #409eff;
      border-color: #409eff;
      line-height: 30px;
      border: 0;

      &:hover {
        background: #66b1ff;
        border-color: #66b1ff;
        color: #fff;
      }
    }
  }

  .describe {
    padding-top: 10px;

    p {
      height: 20px;
      line-height: 20px;

      &.link {
        margin-top: 8px;
        height: 34px;
        line-height: 34px;
        color: #333;
      }
    }

    >>> .el-input {
      margin-right: 10px;
      width: 290px;
    }
  }

  .judge-tit {
    margin-bottom: 14px;
  }
}

.ask {
  margin-left: 12px;
  cursor: pointer;
}

.ques {
  padding: 0 4px;
  height: 18px;
  line-height: 18px;
}

.head-pic {
  margin: 12px 0;
  width: 70px;
  height: 70px;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
  }
}

.judge-headpic {
  position: relative;
  width: 98px;
  height: 98px;
  border: 1px solid #c0c6d2;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
  }

  .upload {
    width: 100%;
    height: 20px;
    line-height: 20px;
    text-align: center;
    color: #f4f4f4;
    background-color: rgba(0, 0, 0, 0.6);
  }

  >>> .el-upload--picture-card {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 98px;
    height: 20px;
    border: 0;
  }
}

.judge-con {
  padding-top: 10px;
  margin: 10px 0;

  p {
    padding-right: 6px;
    width: 100px;
    box-sizing: border-box;
  }

  .fred {
    margin-right: 5px;
    color: #ff2222;
  }
}

.matter-hint {
  margin-bottom: 30px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  font-size: 16px;
}

.matter-con {
  width: 100%;
  box-sizing: border-box;
  padding-left: 10px;
  height:0px;
  overflow-y: auto;

  .imageWrapper {
    padding-left: 2px;

    img {
      width: 100%;
    }
  }
}

.batch-screenshots {
  .batch-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;

    .matter-hint {
      margin-bottom: 0;
    }
  }

  .batch-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .batch-item {
    display: flex;
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 4px;

    .batch-info {
      flex: 1;
      padding-right: 15px;

      h4 {
        margin: 0 0 5px 0;
        color: #333;
        font-size: 14px;
      }

      p {
        margin: 0 0 10px 0;
        color: #666;
        font-size: 12px;
      }
    }

    .batch-preview {
      width: 120px;
      height: 80px;
      border: 1px solid #ddd;
      border-radius: 4px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.matter-warp {
  width: 605px;

  > div {
    width: 258px;
    padding: 18px 20px 15px 22px;

    &:nth-child(2n) {
      border-left: 0;
    }

    &:nth-child(1) {
      border-top: 1px dashed #dbdde1;
    }

    &:nth-child(2) {
      border-top: 1px dashed #dbdde1;
    }

    border: 1px dashed #dbdde1;
    border-top: 1px dashed #fff;

    .info {
      width: 126px;
      padding-top: 8px;

      .tips {
        line-height: 24px;
        font-size: 18px;
        color: #333;
        font-weight: bold;
      }

      .code-info {
        margin-top: 24px;
      }
    }

    .code-container {
      p {
        height: 18px;
        line-height: 18px;
        text-align: center;
        font-size: 13px;
        color: #333;

        &:first-child {
          margin-top: 14px;
        }
      }
    }
  }
}
</style>
