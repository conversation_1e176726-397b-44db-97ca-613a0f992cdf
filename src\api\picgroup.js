import api from './api'
export default {
  add: v => api.fetchBaseData('/pro/hxc/prowallpicgroup/add.htm', v),
  read: v => api.fetchBaseData('/pro/hxc/prowallpicgroup/read.htm', v),
  update: v => api.fetchBaseData('/pro/hxc/prowallpicgroup/update.htm', v),
  list: v => api.fetchBaseData('/pro/hxc/prowallpicgroup/list.htm', v),
  delete: v => api.fetchBaseData('/pro/hxc/prowallpicgroup/delete.htm', v),
}
