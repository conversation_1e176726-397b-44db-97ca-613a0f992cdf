<template>
  <div class="login-box flex flex-a-c flex-j-c" :style="bg">
    <div>
      <video v-if="isVideo" autoplay loop muted :src="offerObj.lgBgimg || require('@/assets/common/bg.mp4')"></video>
      <div class="login-form">
        <div class="logo">
          <img :src="offerObj.lgLogo" />
        </div>
        <el-form :model="userObj" :rules="loginRule" ref="loginForm">
          <el-form-item label="" prop="username">
            <div class="icon-box">
              <img src="@/assets/common/user.png" />
            </div>
            <el-input type="text" v-model.trim="userObj.username" placeholder="账号"></el-input>
          </el-form-item>
          <el-form-item label="" prop="pwd">
            <div class="icon-box">
              <img src="@/assets/common/pwd.png" />
            </div>
            <el-input type="password" v-model.trim="userObj.pwd" placeholder="密码"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" @click="login" :loading="load">立即登录</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-dialog title="提示" :visible.sync="tipDialog" :close-on-click-modal="false" width="400px">
        <p class="warn-box flex flex-a-c flex-j-c">
          <i class="el-icon-warning"></i>
          <span>{{ tips }}</span>
        </p>
      </el-dialog>
    </div>
    <!-- <p class="owner flex flex-a-c flex-j-c">
      {{ offerObj.lgCopyright }}
    </p> -->
  </div>
</template>
<script>
import api from '@/api'
import { Hi } from '@/libs/common'
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'UserAuthorize',
  data() {
    return {
      load: false,
      userObj: {
        name: '',
        pwd: '',
      },
      loginRule: {
        username: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
      },
      tipDialog: false,
      tips: '',
    }
  },
  computed: {
    ...mapGetters({
      offerObj: 'user/getDeploy',
    }),
    isVideo() {
      if (this.offerObj.lgBgimg) {
        let index = this.offerObj.lgBgimg.lastIndexOf('.')
        let pattern = this.offerObj.lgBgimg.slice(index + 1)
        const patteenArr = ['mp4']
        return patteenArr.includes(pattern)
      } else {
        return true
      }
    },
    _angntId() {
      return this.$route.params.agentId
    },
    bg() {
      return {
        'background-image': `url(${Hi.String.dealUrl(this.offerObj.lgBgimg)})`,
        'background-repeat': 'no-repeat',
        'background-size': 'cover',
        'background-position': 'center',
      }
    },
  },
  methods: {
    ...mapActions({
      fetchDeploy: 'user/fetchDeploy',
    }),
    async login() {
      await this.$refs.loginForm.validate()
      this.load = true
      try {
        // let result = await this.$captcha((v) => (this.loading = v))

        const userName = this.userObj.username
        const postObj = {
          passwd: this.userObj.pwd,
          userName,
          agentId: this._angntId,
          // ...result,
        }
        await api.user.oemuserlogin(postObj)
        // 缓存记录用户当前的用户名
        localStorage.setItem('login-name', userName)

        // gio 处理
        if (typeof window.gio !== 'undefined') {
          try {
            let userInfo = await api.user.info()
            let obj = {}
            obj.phone = userInfo.phone
            window.gio('setUserId', userInfo.id)
            window.gio('people.set', obj)
            window.gio('send')
          } catch (error) {
            console.error(error)
          }
        }
        this.$refs.loginForm.resetFields()
        try {
          if (window.parent.document.domain === document.domain) {
            window.parent && window.parent.postMessage('close', '*')
          }
          // @ts-ignore
          if (!window.parent || !window.parent.loginSuccess) {
            let query = this.$route.query || {}
            let name = (query.route === 'user-login' ? 'wall-list' : query.route) || 'wall-list'
            delete query.route
            this.$router.replace({ name, query })
            this.$notify.success({ title: '成功', message: '登录成功' })
          }
        } catch (error) {
          let query = this.$route.query || {}
          let name = (query.route === 'user-login' ? 'wall-list' : query.route) || 'wall-list'
          delete query.route
          this.$router.replace({ name, query })
          this.$notify.success({ title: '成功', message: '登录成功' })
        }
      } catch (err) {
        console.error(err)
        this.userObj.imgvcode = ''
        if (err && err.msg) {
          this.$notify.error({ title: '错误', message: err.msg })
        } else {
          this.$notify.error({ title: '错误', message: '登录失败，稍后重试' })
        }
      } finally {
        this.load = false
      }
    },
  },
  async mounted() {
    if (this.$route.params.agentId !== 'null') {
      await this.fetchDeploy()
    } else {
      await this.$alert('访问地址有误，请联系服务商通过客户业务系统入口访问', '提示', {
        confirmButtonText: '确定',
      })
    }
  },
}
</script>
<style lang="scss" scoped>
.w-156 {
  width: 156px;
}

.login-box {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  > div {
    transform: translateY(-80px);
  }

  video {
    display: block;
    position: fixed;
    left: 0;
    top: 0;
    z-index: -100;
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
}

.login-form {
  width: 274px;
  margin: 0 auto;

  ::v-deep .el-input__inner {
    height: 44px;
    line-height: 44px;
    background-color: #01112f !important;
    border-color: #0099ff;
    color: #fff;
    text-indent: 36px;
    caret-color: #fff;

    &:focus {
      border-color: #0099ff;
    }

    &:-webkit-autofill {
      -webkit-box-shadow: 0 0 0px 1000px #01112f inset;
      -webkit-text-fill-color: #fff;
      -webkit-border-color: #0099ff;
    }

    &::-webkit-input-placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  ::v-deep .el-button {
    padding: 14px 20px;
    width: 100%;
  }

  .icon-box {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    width: 52px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .code-box {
    width: 96px;
    height: 36px;

    img {
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
  }

  .logo {
    padding: 15px;
    width: 100%;
    box-sizing: border-box;

    img {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }
  }
}

.warn-box {
  height: 24px;
  padding-bottom: 24px;
  font-size: 14px;

  i {
    margin-right: 20px;
    font-size: 24px;
    color: #e6a23c;
  }
}

.owner {
  position: fixed;
  left: 0;
  bottom: 0;
  font-size: 12px;
  color: #757a84;
  width: 100%;
  height: 64px;
}
</style>
