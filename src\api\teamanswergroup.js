import api from './api';

export default {
  update: (postData) => api.fetchBaseData('/pro/hxc/proteamanswergroup/update.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/proteamanswergroup/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proteamanswergroup/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proteamanswergroup/add.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proteamanswergroup/delete.htm', postData),
};
