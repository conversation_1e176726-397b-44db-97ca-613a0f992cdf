<template>
  <div class="menu-box flex flex-d-c">
    <ul class="wrap">
      <li v-for="(item, index) in menuObj.options" :key="item.barId">
        <div class="title flex flex-a-c flex-j-sb" :class="{ active: currentBar === item.barId }" @click="toggle(index, item)">
          <div class="flex flex-d-c mar-r-5">
            <span class="header-title">{{ item.name }}</span>
            <span v-if="merchantKey !== 'vzan' && item.subtitle && !item.fold" class="sub">{{ item.subtitle }}</span>
          </div>
          <i v-if="item.children" :id="'arrow' + index" class="arrow el-icon-arrow-up" :style="_getInversionStyle(item)"></i>
          <img v-if="item.isNew" :src="require('@/assets/wall/new2.png')" />
        </div>
        <el-collapse-transition v-if="item.children">
          <div v-show="item.fold">
            <ul class="child-menu">
              <template v-for="list in item.children">
                <li v-if="!list.isIndependent" :class="[item.barId !== 'cj' ? 'content' : 'child-content']" :key="list.barId">
                  <template v-if="item.barId === 'cj'">
                    <div class="child-box flex flex-a-c flex-j-c" @click="list.fold = !list.fold">
                      <i v-if="list.children" :id="'arrow' + index" class="arrow el-icon-caret-top" :style="_getInversionStyle(list)"></i>
                      <div class="img-box mrg-l-10">
                        <transition name="fade">
                          <img v-if="isActChild(list.barId)" :key="1" :src="require(`@/assets/wall/interact/menu/act_${list.barId}.png`)" alt="" />
                          <img v-else :key="2" :src="require(`@/assets/wall/interact/menu/${list.barId}.png`)" alt="" />
                        </transition>
                      </div>
                      <span class="w-90">{{ list.name }}</span>
                    </div>
                    <!-- 子孙路由 -->
                    <el-collapse-transition v-if="list.children">
                      <div v-show="list.fold">
                        <template v-for="child in list.children">
                          <div
                            class="child-item"
                            :class="{ active: currentAct === child.route, new: child.isNew }"
                            @click="go(child.route, child.barId)"
                          >
                            {{ child.name }}
                            <img v-if="child.isNew" class="new-icon" :src="require('@/assets/wall/new.png')" />
                            <img v-if="child.isHot" class="hot-icon" :src="require('@/assets/wall/hot.png')" />
                          </div>
                        </template>
                      </div>
                    </el-collapse-transition>
                  </template>
                  <template v-else>
                    <div
                      class="flex flex-a-c realtive"
                      :class="{ active: currentAct === list.route }"
                      @mouseover="currenthover = list.route"
                      @mouseleave="currenthover = null"
                      @click="go(list.route, item.barId)"
                    >
                      <div class="img-box">
                        <transition name="fade">
                          <img v-if="isact(list.route)" :key="1" :src="getActIcon(list)" />
                          <img v-else :key="2" :src="getIcon(list)" />
                        </transition>
                      </div>
                      <span class="relative">
                        {{ list.name }}
                        <div v-if="list.isProVersion" class="pro-version">专业版</div>
                      </span>
                      <img v-if="list.isNew" class="new-pic" :src="require('@/assets/wall/new.png')" />
                      <img v-if="list.isHot" class="new-pic" :src="require('@/assets/wall/hot.png')" />
                      <div v-if="list.isRushToAnswer" class="rushToAnswer-pic">抢答</div>
                      <div v-if="list.route === 'shakev3'" class="rushToAnswer-pic" :style="{ right: '5px' }">赛车 赛马</div>
                    </div>
                  </template>
                </li>
              </template>
            </ul>
          </div>
        </el-collapse-transition>
      </li>
    </ul>
    <div v-if="!isOem" class="flex-1 blank">
      <router-link :to="{ name: 'wall-interact-independent', query: $route.query }">
        <div v-if="isBtn" class="append-btn"><i class="el-icon-plus"></i> 更多高级互动功能</div>
      </router-link>
    </div>
  </div>
</template>
<script>
import { routerMixin } from '@/libs/mixins'
import { mapGetters } from 'vuex'

export default {
  name: 'HiMenu',
  mixins: [routerMixin],
  props: {
    menuObj: {
      type: Object,
      default: () => ({}),
    },
    isBtn: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currenthover: null,
      currentBar: null,
    }
  },
  computed: {
    ...mapGetters({
      isOem: 'user/isOem',
      merchantKey: 'user/getMerchantKey',
    }),
    currentAct() {
      return this.$route.name.split('-')[2]
    },
    routePrefix() {
      return this.$route.name.split('-')[1]
    },
    _isInteract() {
      return this.routePrefix === 'interact'
    },
    _getInversionStyle() {
      return (item) => {
        return {
          transform: item.fold ? 'rotate(180deg)' : 'rotate(0deg)',
          transition: 'transform 0.3s ease-in-out',
        }
      }
    },
  },
  watch: {
    currentAct(v) {
      let startRoute = this.menuObj.options[0].children ? this.menuObj.options[0].children[0].route : this.menuObj.options[0].route
      if (v === startRoute) {
        this.menuObj.options.forEach((item) => {
          if (item.children) {
            item.children.forEach((list) => {
              item.fold = false
            })
          } else {
            item.fold = false
          }
        })
        this.currentBar = this.menuObj.options[0].barId
        this.menuObj.options[0].fold = true
      }
    },
  },
  methods: {
    getActIcon(item) {
      if (['staffmanage', 'data', 'interact'].includes(this.routePrefix)) {
        return require(`@/assets/wall/${this.routePrefix === 'staffmanage' ? 'staffmanage' : 'interact'}/menu/act_${item.route}.png`)
      } else {
        return require(`@/assets/wall/${this.routePrefix}/menu/act_${item.route}.png`)
      }
    },
    getIcon(item) {
      if (['staffmanage', 'data', 'interact'].includes(this.routePrefix)) {
        return require(`@/assets/wall/${this.routePrefix === 'staffmanage' ? 'staffmanage' : 'interact'}/menu/${item.route}.png`)
      } else {
        return require(`@/assets/wall/${this.routePrefix}/menu/${item.route}.png`)
      }
    },
    toggle(orderNum, data) {
      let arr = this.menuObj.options
      let openNum = null
      if (data.children) {
        if (data.fold) {
          this.menuObj.options[orderNum].fold = false
        } else {
          arr.forEach((item, index) => {
            if (item.fold) {
              openNum = index
            }
          })
          if (typeof openNum === 'number') {
            this.menuObj.options[openNum].fold = false
            this.menuObj.options[orderNum].fold = true
          } else {
            this.menuObj.options[orderNum].fold = true
          }
        }
      } else {
        this.go(data.route, data.barId)
      }
    },
    go(route, barId) {
      this.currentBar = barId ? barId : 'cj'
      if (this.$route.name === `wall-interact-${route}`) {
        this.updateRouteKey()
        return
      }
      let query = this.$route.query
      query.id && delete query.id
      this.$router.push({
        name: `${this.menuObj.modulePrefix}-${route}`,
        query: { wallFlag: query.wallFlag },
      })
    },
    isact(route) {
      return this.currentAct === route || this.currenthover === route
    },
    isActChild(barId) {
      return this.menuObj.options.some(
        (item) =>
          item.children &&
          item.children.some((list) => list.barId === barId && list.children && list.children.some((child) => child.route === this.currentAct))
      )
    },
  },
  async mounted() {
    this.menuObj.options.forEach((item) => {
      const checkChildren = (children, parent) => {
        children.forEach((child) => {
          if (child.children) {
            checkChildren(child.children, child)
          } else if (child.route === this.currentAct) {
            this.currentBar = item.barId
            if (parent) parent.fold = true
            item.fold = true
          }
        })
      }
      if (item.children) {
        checkChildren(item.children)
      } else if (item.route === this.currentAct) {
        this.currentBar = item.barId
      }
    })
  },
}
</script>
<style scoped lang="scss">
.w-90 {
  width: 90px;
}
.realtive {
  position: relative;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.menu-box {
  min-height: 100%;
  background-color: #fff;
  font-size: 14px;
}

.mar-r-10 {
  margin-right: 10px;
}

.title {
  position: relative;
  height: 60px;
  color: #333;
  cursor: pointer;
  box-sizing: border-box;
  border-bottom: 1px solid #e0e6ed;
  border-right: 1px solid #e0e6ed;
  background-color: #fff;
  padding: 0 20px;
  transition: all 0.2s linear;

  &:hover {
    background-color: #eef0ff;
  }

  &.active {
    background-color: #4a86fd;
    color: #fff;
    border-bottom: 1px solid #4a86fd;
    border-right: 1px solid #4a86fd;

    .sub {
      color: #fff;
    }
  }

  img {
    position: absolute;
    top: 10px;
    right: 12px;
  }
}

.child-menu {
  width: 179px;
  padding: 18px 0;
  background-color: #f5f6ff;
  border-right: 1px solid #e0e6ed;
  position: relative;
  color: black;
}

.content {
  display: block;
  margin-bottom: 6px;
  height: 38px;
  cursor: pointer;
  font-size: 14px;

  > div {
    margin: 0 auto;
    width: 150px;
    height: 100%;
    padding-left: 12px;
    box-sizing: border-box;
    color: #666;
    transition: all 0.2s linear;

    &:hover {
      border-radius: 38px;
      background-color: #eef0ff;
    }

    &.active {
      border-radius: 38px;
      background-color: #fff;
      box-shadow: 0 1px 4px rgba(74, 134, 253, 0.2);
      color: #333;
    }
  }

  .new-pic {
    position: relative;
    left: 4px;
    top: -6px;
  }
}

.blank {
  border-right: 1px solid #e0e6ed;
}

.relative {
  position: relative;
}
// 专业版
.pro-version {
  position: absolute;
  top: -9px;
  right: -44px;
  width: 30px;
  border-radius: 10px;
  font-size: 10px;
  color: #fff;
  padding: 2px 6px;
  background-color: #ff6666;
}

.rushToAnswer-pic {
  padding: 2px 6px;
  background-color: #ff6666;
  position: absolute;
  right: 19px;
  top: 0;
  border-radius: 10px;
  font-size: 12px;
  color: #fff;
}

.append-btn {
  width: 140px;
  padding-bottom: 20px;
  line-height: 25px;
  text-align: center;
  cursor: pointer;
  border-radius: 5px;
  transition: all 0.2s linear;
  padding: 8px 10px;
  color: #4a86fd;
  border: 1px solid #4a86fd;
  font-size: 12px;
  box-sizing: border-box;
  margin: 10px auto 10px;
}

.header-title {
  font-size: 14px;
  font-weight: 700;
}

.sub {
  width: 119px;
  margin-top: 5px;
  font-size: 12px;
  color: #92959b;
  overflow: hidden;
  white-space: nowrap;
}

.child-content {
  display: block;
  cursor: pointer;
  transition: all 0.2s linear;
}

.child-box {
  min-height: 38px;
  display: flex;
  align-items: center;
}

.child-item {
  display: block;
  padding: 7px 0;
  cursor: pointer;
  font-size: 14px;
  width: 135px;
  margin: 0 auto 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #92959b;
  transition: all 0.2s linear;
  position: relative;

  &:hover {
    border-radius: 38px;
    background-color: #ffffff;
    box-shadow: 0 1px 4px rgba(74, 134, 253, 0.3);
    color: #4a86fd;
  }

  &.active {
    border-radius: 38px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(74, 134, 253, 0.3);
    color: #4a86fd;
  }

  .new-icon {
    position: absolute;
    right: 11px;
    top: 4px;
  }

  .hot-icon {
    position: absolute;
    right: 11px;
    top: 4px;
  }
}

.img-box {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  position: realtive;
  img {
    position: absolute;
  }
}
</style>
