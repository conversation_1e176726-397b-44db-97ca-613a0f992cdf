import api from './api';
export default {
  read: (v) => api.fetchBaseData('/pro/hxc/prowishrandom/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/prowishrandom/update.htm', v),
  add: (v) => api.fetchBaseData('/pro/hxc/prowishrandom/add.htm', v),
  page: (v) => api.fetchBaseData('/pro/hxc/prowishrandom/page.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/prowishrandom/list.htm', v),
  delete: (postData) => api.fetchBaseData('/pro/hxc/prowishrandom/delete.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/prowishrandom/batch.htm', postData),
};
