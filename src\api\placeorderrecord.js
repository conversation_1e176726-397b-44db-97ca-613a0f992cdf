import api from './api'
export default {
  //订货会-统计
  page: postData => api.fetchBaseData('/pro/hxc/proplaceorderrecord/page.htm', postData),
  detail: postData => api.fetchBaseData('/pro/hxc/proplaceorderrecord/detail.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proplaceorderrecord/list.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/proplaceorderrecord/add.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proplaceorderrecord/update.htm', postData),
  total: postData => api.fetchBaseData('/pro/hxc/proplaceorderrecord/total.htm', postData),
}
