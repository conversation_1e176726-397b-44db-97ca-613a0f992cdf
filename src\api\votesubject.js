import api from './api'
export default {
  page: postData => api.fetchBaseData('/pro/hxc/prowallvotesubject/page.htm', postData),
  insert: postData => api.fetchBaseData('/pro/hxc/prowallvotesubject/insert.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prowallvotesubject/update.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/prowallvotesubject/read.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/prowallvotesubject/delete.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prowallvotesubject/list.htm', postData),
  addvoteswitch: postData => api.fetchBaseData('/pro/hxc/prowallvotesubject/addvoteswitch.htm', postData),
}
