import api from './api'
export default {
  page: (postData) => api.fetchBaseData('/pro/hxc/proseglottery/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proseglottery/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proseglottery/add.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/proseglottery/read.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proseglottery/delete.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proseglottery/update.htm', postData),
  sort: (postData) => api.fetchBaseData('/pro/hxc/proseglottery/sort.htm', postData),
}
