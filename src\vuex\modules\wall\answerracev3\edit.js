import api from '@/api'
import { Hi, fenToYuan, env } from '@/libs/common'

// 默认轮次配置
const defaultActConfig = {
  title: '答题',
  countDown: 10, // 答题时间
  gameMode: 'score',
  awardSwitch: 'N', //奖品开关Y
  answerraceTime: 30, // 答题时间
  rank: 10, // 结果显示前几名
  theme: 'purple', // 主题色
  prepareType: 'Preset', // 准备页标题类型 Preset Selfset
  presetType: 'redpack', // 预设标题 redpack knowledge barrier brain
  prepareImg: '', //准备页标题
  screenImg: '', //大屏幕背景
  mobileImg: '', //手机背景
  answerracegrouplimitSwitch: 'N', // 分组限制
  optionShowType: 'RATE',
  autoSwitch: 'N', //是否自动切题
  autoSwitchtime: 5, //切题时间
  score: 1, //每题分值
  awardsAgainSwitch: 'N',
  awardsAgainLimitRank: 1,
  limitOption: 'ALLOW',
  l1: 0,
  l2: 0,
  l3: 0,
  module: 'all',
  endMusic: `https://res3.hixianchang.com/qn/up/8cf0bf00e25375a3906bba3ecd7d9857.mp3`,
  inMusic: `https://res3.hixianchang.com/qn/up/df8280bcc57aaec1c486a522ede25eed.mp3`,
  waitMusic: `https://res3.hixianchang.com/qn/up/a44c1605238f55d7eaf0070e3cded97f.mp3`,
}
/**
 * 编辑页面中的逻辑数据
 */
export default {
  name: 'Answerracev3Edit',
  namespaced: true,
  state: {
    config: {}, // 主配置
    originActConfig: {}, // 元轮次数据
    actConfig: {}, // 业务轮次数据
    originQuesAndOptArr: [], //题目+选项业务数据
    quesAndOptArr: [], //题目+选项业务数据
  },
  getters: {
    isUpdate(state) {
      return !!Hi.Object.difference(state.originActConfig, state.actConfig) || !!Hi.Object.batchSave(state.originQuesAndOptArr, state.quesAndOptArr)
    },
    defaultChange(state) {
      return Hi.Object.difference(defaultActConfig, state.actConfig)
    },
  },
  actions: {
    //查询互动配置
    async fetchConfig({ commit, rootState }) {
      const config = await api.answerracev3config.read({ where: { wallId: rootState.wall.wall.id } })
      commit('setConfig', config)
    },
    //查询所有信息
    async fetchAllData({ commit, dispatch }, id) {
      if (id) {
        // 编辑
        await dispatch('fetchActConfig', id)
        await dispatch('fetchQuestionAndOptData', id)
      } else {
        // 新增
        commit('setOriginActConfig', {})
        commit('setActConfig', Hi.Object.copy(defaultActConfig))
        commit('setOriginQuesAndOptArr', [])
        commit('setQuesAndOptArr', [])
      }
    },
    //保存所有信息
    async saveAllData({ dispatch }, actState) {
      let actId = await dispatch('saveActConfig', actState)
      await dispatch('saveQuestionAndOpt', actId)
    },
    //查询轮次信息
    async fetchActConfig({ state, commit, rootState }, id) {
      let actConfig = await api.answerracev3.read({ where: { id, wallId: rootState.wall.wall.id } })
      commit('setOriginActConfig', actConfig)
      commit('setActConfig', Hi.Object.copy(actConfig))
    },
    //查询兑奖方式数据
    async fetchClaimData({ rootState }, prizeList) {
      let arr = Hi.Object.copy(prizeList)
      if (!arr || !arr.length) return []
      let claimObj = {}
      let idList = []
      arr.forEach((item) => {
        idList.push(item.id)
      })
      let data = await api.acceptawardsconfig.list({
        where: {
          wallId: rootState.wall.wall.id,
          module: 'answerracev3',
          moduleAwardsIdList: idList,
        },
      })
      data.forEach((item) => {
        claimObj[item.moduleAwardsId] = item
      })
      arr.forEach((item) => {
        item.acceptawardsConfig = claimObj[item.id] || {}
      })
      return arr
    },
    //查询题目+选项数据
    async fetchQuestionAndOptData({ state, commit, dispatch, rootState }, id) {
      let questionAndOpt = []
      let quesIndexCache = {} //暂存问题的index,方便选项挂载时能找到问题的位置
      let questionData = await api.answerracev3question.list({
        where: {
          answerraceId: id,
          wallId: rootState.wall.wall.id,
        },
      })
      let questionIdList = []
      questionData.forEach((item, index) => {
        questionIdList.push(item.id)
        item.optionArr = []
        questionAndOpt.push(item)
        quesIndexCache[item.id] = index
      })
      //查询选项数据
      let optionData = []
      if (questionIdList.length) {
        optionData = await api.answerracev3answer.list({ where: { questionIdList } })
        optionData.forEach((item) => {
          let index = quesIndexCache[item.questionId]
          item.quesUuid = questionAndOpt[index].uuid //将问题uuid挂倒选项上
          questionAndOpt[index].optionArr.push(item)
        })
      }
      commit('setOriginQuesAndOptArr', Hi.Object.copy(questionAndOpt))
      commit('setQuesAndOptArr', questionAndOpt)
    },
    //保存轮次数据
    async saveActConfig({ state, dispatch, rootState }, actState) {
      let id = state.actConfig.id
      // 轮次处理
      let update = Hi.Object.difference(state.originActConfig, state.actConfig, { ignoreKey: ['updateDate'] })
      if (update && Object.keys(update)) {
        if (id) {
          // 修改
          await api.answerracev3.update({
            where: { id, wallId: rootState.wall.wall.id },
            update: update,
          })
        } else {
          // 新增
          id = await api.answerracev3.insert({ ...update, wallId: rootState.wall.wall.id })
        }
        await dispatch('fetchActConfig', id)
      }
      return id
    },

    //保存题目+选项数据
    async saveQuestionAndOpt({ state, dispatch, rootState }, actId) {
      let { updateQues, updateOpt } = getDiffQuesAndOpt(state.originQuesAndOptArr, state.quesAndOptArr)
      if (!updateQues && !updateOpt) return
      let addQuestionIdObj = {} //新增问题id数据
      if (updateQues && updateQues.length) {
        // 处理新增数据
        updateQues.forEach((item) => {
          if (item.type === 'add') {
            item.data.forEach((data) => {
              data.answerraceId = actId
              data.wallId = rootState.wall.wall.id
            })
          }
        })
        updateQues.push({ type: 'actId', data: actId }) //需要加入轮次id
        addQuestionIdObj = await api.answerracev3question.batch(updateQues)
      }
      if (updateOpt && updateOpt.length) {
        // 处理新增数据
        updateOpt.forEach((item) => {
          if (item.type === 'add') {
            item.data.forEach((optItem) => {
              optItem.answerraceId = actId
              optItem.wallId = rootState.wall.wall.id
              //新增题目、选项时，题目id从addQuestionIdObj读取，只新增选项时，已经在新增组件中添加了questionId
              !optItem.questionId && (optItem.questionId = addQuestionIdObj[optItem.quesUuid])
              delete optItem.quesUuid
            })
          }
        })
        updateOpt.push({ type: 'actId', data: actId }) //需要加入轮次id
        await api.answerracev3answer.batch(updateOpt)
      }
      await dispatch('fetchQuestionAndOptData', actId)
    },
  },
  mutations: {
    setConfig(state, v) {
      state.config = v
    },
    setOriginActConfig(state, v) {
      state.originActConfig = v
    },
    setActConfig(state, v) {
      state.actConfig = v
    },
    setOriginQuesAndOptArr(state, v) {
      state.originQuesAndOptArr = v
    },
    setQuesAndOptArr(state, v) {
      state.quesAndOptArr = v
    },
  },
}

function getDiffQuesAndOpt(orignData, data) {
  let orignOption = [],
    option = [],
    orignQuestion = [],
    question = []
  orignData.forEach((item) => {
    orignOption.push(...item.optionArr)
    let ques = Hi.Object.copy(item)
    delete ques.optionArr
    orignQuestion.push(ques)
  })
  data.forEach((item) => {
    option.push(...item.optionArr)
    let ques = Hi.Object.copy(item)
    delete ques.optionArr
    question.push(ques)
  })
  let updateQues = Hi.Object.batchSave(orignQuestion, question)
  let updateOpt = Hi.Object.batchSave(orignOption, option)
  return {
    updateQues,
    updateOpt,
  }
}
