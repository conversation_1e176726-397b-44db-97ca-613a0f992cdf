import api from './api'
export default {
  //订货会配置
  add: postData => api.fetchBaseData('/pro/hxc/properformancev3rule/add.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/properformancev3rule/update.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/properformancev3rule/read.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/properformancev3rule/list.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/properformancev3rule/page.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/properformancev3rule/delete.htm', postData),
}
