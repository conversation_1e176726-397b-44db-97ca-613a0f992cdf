import api from './api'
export default {
  // 盲盒抽奖、红包墙，配置
  read: (v) => api.fetchBaseData('/pro/hxc/propacketwallconfig/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/propacketwallconfig/update.htm', v),
  add: (v) => api.fetchBaseData('/pro/hxc/propacketwallconfig/add.htm', v),
  page: (v) => api.fetchBaseData('/pro/hxc/propacketwallconfig/page.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/propacketwallconfig/list.htm', v),
  delete: (postData) => api.fetchBaseData('/pro/hxc/propacketwallconfig/delete.htm', postData),
  finish: (postData) => api.fetchBaseData('/pro/hxc/propacketwallconfig/finish.htm', postData),
}
