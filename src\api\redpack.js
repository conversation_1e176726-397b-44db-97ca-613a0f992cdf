import api from './api'
export default {
  page: postData => api.fetchBaseData('/pro/hxc/prowallredpack/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prowallredpack/list.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/prowallredpack/delete.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/prowallredpack/add.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/prowallredpack/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prowallredpack/update.htm', postData),
  closed: postData => api.fetchBaseData('/pro/hxc/prowallredpack/closed.htm', postData),
}
 