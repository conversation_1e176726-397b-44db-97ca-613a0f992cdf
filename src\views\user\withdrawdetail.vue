<template>
  <div class="withdrawdetail-box">
    <div class="content">
      <h1>记录明细</h1>
      <div class="record">
        <p>订单号 : {{ tradeNo }}</p>
        <p class="partie">
          <label>当前状态 :</label>
          <hi-emoji :cont="lastState" fontSize="12px"></hi-emoji>
        </p>
        <ul>
          <li>
            <p class="operate">操作时间</p>
            <p class="state">状态</p>
            <p class="operate">交易金额</p>
          </li>
          <li v-for="item in stateList" :key="item.id">
            <p class="operate">{{ item.createDate }}</p>
            <p class="state">
              <hi-emoji :cont="item.remark" fontSize="12px"></hi-emoji>
            </p>
            <p class="operate">
              <span v-if="item.changeAmount === 0">---</span>
              <span v-else>
                <span class="fgreen" v-if="item.operationType === 'UNAUDITED' || item.operationType === 'AUDIT_PASS'">
                  - {{ addPoint(item.changeAmount / 100) }}
                </span>
                <span class="fred" v-if="item.operationType === 'AUDIT_NOT_PASSED' || item.operationType === 'FAIL'">
                  + {{ addPoint(item.changeAmount / 100) }}
                </span>
              </span>
            </p>
          </li>
        </ul>
      </div>
      <div class="back">
        <el-button type="primary">
          <router-link :to="{ name: 'user-center-accountdetail' }">
            <span>返回</span>
          </router-link>
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
import payorderApi from '@/api/payorderoperation'
import wxuserApi from '@/api/wxuser'
import { yuanToFen } from '@/libs/common'
export default {
  data() {
    return {
      tradeNo: this.$route.query.tradeNo,
      lastState: '', // 当前状态
      stateList: [],
      wxUserList: [],
    }
  },
  watch: {},
  methods: {
    // 查询粉丝信息
    async getUserInfo() {
      try {
        this.wxUserList = await wxuserApi.listTwo({
          where: {
            openIdList: [this.$route.query.buy],
          },
        })
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
  },
  computed: {
    addPoint() {
      return (num) => {
        let numString = num.toString()
        let len = numString.length
        if (numString.indexOf('.') > -1) {
          let index = numString.indexOf('.')
          if (len - index > 2) {
            return num
          }
          if (len - index == 2) {
            return num + '0'
          }
        } else {
          return num + '.00'
        }
      }
    },
  },
  async created() {
    // 查询  记录明细
    try {
      this.stateList = await payorderApi.list({
        where: {
          tradeNo: this.tradeNo,
        },
        sort: {
          id: 'asc',
        },
      })
      let len = this.stateList.length
      this.lastState = this.stateList[len - 1].remark
      await this.getUserInfo.call(this)
    } catch (e) {
      console.log(e)
      this.$notify.error({ title: '错误', message: e.msg })
    }
  },
}
</script>
<style scoped lang="stylus">
a
  text-decoration none
  color #fff
.withdrawdetail-box
  width 100%
  height 100%
  background-color #ececec
  color #818181
.content
  width 890px
  padding 15px 50px
  background-color #fff
  margin 0 auto
  h1
    padding 0
    height 36px
    line-height 36px
    margin-bottom 10px
    font-weight normal
  .back
    height 30px
    text-align center
  .el-button
    span
      padding 0 100px
.record
  width 100%
  padding 30px 50px
  margin-bottom 20px
  border 1px solid #ddd
  box-sizing border-box
  font-size 14px
  display flex
  flex-direction column
  p
    height 20px
    line-height 20px
    margin-top 0
    &.partie
      margin-bottom 18px
      display flex
  ul
    flex 1
    border 1px solid #ddd
    border-bottom 0
    box-sizing border-box
    li
      border-bottom 1px solid #ddd
      box-sizing border-box
      display flex
      p
        height 100%
        line-height 40px
        text-align center
      .state
        width 346px
        border-left 1px solid #ddd
        border-right 1px solid #ddd
        display flex
        justify-content center
      .operate
        flex 1
.partie
  label
    width 78px
  span
    justify-content flex-start
.content .back a
.content .back a:hover
  color #fff
</style>
