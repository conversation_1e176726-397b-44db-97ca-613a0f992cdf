<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <meta name="renderer" content="webkit">
  <!-- 引入样式 -->
  <link rel="stylesheet" href="<%= BASE_URL %>static/cdn/normalize.css@8.0.0/normalize.css" />
  <link rel="stylesheet" href="<%= BASE_URL %>static/cdn/element-ui@2.9.1_1/theme-chalk/index.css" />
  <title></title>
  <script type="text/javascript">
    window._AMapSecurityConfig = { securityJsCode: '95ebf6746ad0466d5af020cd5813f401' }
  </script>
</head>

<body>
  <div id="app"></div>
  <script type="text/javascript" src="<%= BASE_URL %>static/cdn/vue@2.5.16/vue<%= VUE_APP_ENV === 'dev' ? '' : '.min' %>.js"></script>
  <script type="text/javascript" src="<%= BASE_URL %>static/cdn/vuex@3.6.2/vuex<%= VUE_APP_ENV === 'dev' ? '' : '.min' %>.js"></script>
  <script type="text/javascript" src="<%= BASE_URL %>static/cdn/vue-router@3.0.1/vue-router<%= VUE_APP_ENV === 'dev' ? '' : '.min' %>.js"></script>
  <script type="text/javascript" src="<%= BASE_URL %>static/cdn/element-ui@2.9.1_1/index.js"></script>
  <script type="text/javascript" src="<%= BASE_URL %>static/cdn/axios@0.18.0/axios<%= VUE_APP_ENV === 'dev' ? '' : '.min' %>.js"></script>
  <script>Vue.prototype.$ELEMENT = { size: 'small', zIndex: 10000 };</script>
</body>

</html>
