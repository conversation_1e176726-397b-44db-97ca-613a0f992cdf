<template>
  <div class="apply-wrap" v-loading="loading">
    <div class="top">
      <el-button icon="el-icon-d-arrow-left" @click="$emit('changeCurrent', 'HiInvoiceManage')" type="text">返回自助开票列表</el-button>
    </div>
    <div class="content">
      <el-form :model="invoice" :rules="invoiceRules" ref="invoiceForm" label-width="250px">
        <hi-collapse title="基础信息">
          <el-form-item v-if="isEnterprise" label="开票类型：">
            <div class="flex">
              <el-select class="w-120" v-model="selectType" placeholder="请选择发票类型" @change="changeSelectType">
                <el-option v-for="item in provinceTypeList" :key="item.type" :label="item.name" :value="item.type"></el-option>
              </el-select>
              <span v-if="selectType === 'GEREN'" class="mrg-l-10">发票名称为下方填写的“联系人姓名”</span>
            </div>
          </el-form-item>
          <el-form-item label="发票抬头：" prop="invoiceTitle" v-if="selectType !== 'GEREN'">
            <el-input
              v-if="isEnterprise"
              class="w500"
              v-model.trim="invoice.invoiceTitle"
              placeholder="企业类型发票信息抬头需填写公司营业执照上的全称"
              maxlength="128"
            ></el-input>
            <div v-else>
              {{ invoice.invoiceTitle }}
            </div>
            <el-button v-if="isEnterprise" class="mrg-l-10" type="primary" @click="explrotVisible = true">从常用抬头中导入</el-button>
          </el-form-item>
          <el-form-item
            v-if="selectType === 'DEFAULT' || selectType === 'GONGHUI'"
            class="toUpperCase"
            label="公司统一社会信用代码："
            :prop="selectType !== 'GONGHUI' ? 'creditCode' : ''"
          >
            <el-input
              v-if="isEnterprise"
              class="w500"
              v-model.trim="invoice.creditCode"
              placeholder="为企业营业执照上面的统一社会信用代码"
              :maxlength="20"
            ></el-input>
            <div v-else>
              {{ invoice.creditCode }}
            </div>
          </el-form-item>
          <el-form-item label="发票面额：">
            <span>{{ `￥${totalAmount}` }}</span>
          </el-form-item>
          <el-form-item label="注册地址：" prop="registeredAddr" v-if="selectType !== 'GEREN'">
            <el-input class="w500" v-model.trim="invoice.registeredAddr" maxlength="512"></el-input>
          </el-form-item>
          <el-form-item label="注册电话：" prop="registeredPhone" v-if="selectType !== 'GEREN'">
            <el-input class="w500" v-model.trim="invoice.registeredPhone" maxlength="64"></el-input>
          </el-form-item>
          <el-form-item label="开户行：" prop="bankAddr" v-if="selectType !== 'GEREN'">
            <el-input class="w500" v-model.trim="invoice.bankAddr" maxlength="128"></el-input>
          </el-form-item>
          <el-form-item label="银行账号：" prop="cardNo" v-if="selectType !== 'GEREN'">
            <el-input class="w500" v-model.trim="invoice.cardNo"></el-input>
          </el-form-item>
          <el-form-item label="发票备注：" prop="invoiceRemark" v-if="selectType !== 'GEREN'">
            <el-input class="w500" v-model.trim="invoice.invoiceRemark"></el-input>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item v-if="invoiceType === 'PAPER_INVOICE' || invoiceType === 'DEDICATED_INVOICE'" class="w400" label="邮寄地址：" prop="city">
            <div class="flex station-box">
              <p class="country"><img :src="require('@/assets/user/flag.png')" />中国</p>
              <el-select class="w-120 mrg-l-10" v-model="invoice.province" placeholder="请选择省份">
                <el-option v-for="item in provinceArr" :key="item.code" :label="item.name" :value="item.name"></el-option>
              </el-select>
              <el-select class="w-120 mrg-l-10" v-model="invoice.city" placeholder="请选择城市">
                <el-option v-for="item in cityArr" :key="item.code" :label="item.name" :value="item.name"></el-option>
              </el-select>
              <el-select class="w-120 mrg-l-10" v-model="invoice.county" placeholder="请选择地区">
                <el-option v-for="item in countyArr" :key="item.code" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </div>
          </el-form-item>

          <div style="position: relative" v-if="invoiceType === 'PAPER_INVOICE' || invoiceType === 'DEDICATED_INVOICE'">
            <el-form-item class="w400" label="" prop="detaileAddr">
              <el-input class="w-360" v-model.trim="invoice.detaileAddr" placeholder="详细地址"></el-input>
            </el-form-item>
            <el-form-item class="postcode w400" label="" prop="detaileAddr">
              <el-input class="w-130 mrg-l-10" v-model.trim="invoice.postcode" placeholder="邮编"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="联系人姓名：" prop="contactName">
            <el-input class="w500" v-model.trim="invoice.contactName"></el-input>
          </el-form-item>
          <el-form-item label="联系手机：" prop="contactPhone">
            <el-input class="w500" v-model.trim="invoice.contactPhone"></el-input>
          </el-form-item>
          <el-form-item label="联系邮箱：" prop="contactEmail">
            <el-input class="w500" v-model.trim="invoice.contactEmail"></el-input>
          </el-form-item>
        </hi-collapse>

        <hi-collapse title="所含订单">
          <hi-bill :invoiceList="invoiceList" v-if="invoiceList.length"></hi-bill>
        </hi-collapse>

        <hi-collapse title="附件资料上传" v-if="!(selectType === 'GEREN' && invoiceType === 'ELEC_INVOICE')">
          <el-form-item label="" prop="taxcertificateImgpath" v-if="selectType !== 'GEREN'">
            <div class="up-box">
              <p class="f-16">税务登记证副本/营业执照副本电子版（三证合一）：</p>
              <p class="img-tips">支持jpg/bmp/png/gif/webp等</p>
              <hi-upload-img
                class="uploader"
                show-loading
                :action="$hi.url.upload"
                :show-file-list="false"
                :on-success="
                  (res) => {
                    return uploadSuccess(res, 'taxcertificateImgpath')
                  }
                "
              >
                <img v-if="invoice.taxcertificateImgpath" v-hiimg="invoice.taxcertificateImgpath" class="img" />
                <i v-else class="el-icon-plus uploader-icon"></i>
              </hi-upload-img>
            </div>
          </el-form-item>
          <el-form-item label="" prop="hiInvoiceImgpath" v-if="invoiceType === 'PAPER_INVOICE'">
            <div class="up-box">
              <p class="f-16">
                Hi现场小额发票邮寄费用支付详情截图：<span class="look-postage" @click="postageVisble = true">点击查看邮费支付流程</span>
              </p>
              <p class="f-14 red">如开票金额未满400元，请提供附带订单号的支付宝支付邮费截图</p>
              <p class="img-tips">支持jpg/bmp/png/gif/webp等</p>
              <hi-upload-img
                class="uploader"
                show-loading
                :action="$hi.url.upload"
                :show-file-list="false"
                :on-success="
                  (res) => {
                    return uploadSuccess(res, 'hiInvoiceImgpath')
                  }
                "
              >
                <img v-if="invoice.hiInvoiceImgpath" v-hiimg="invoice.hiInvoiceImgpath" class="img" />
                <i v-else class="el-icon-plus uploader-icon"></i>
              </hi-upload-img>
              <p class="f-14 red">注意事项：纸质发票需要退回原件才可重开，（邮费自理）</p>
            </div>
          </el-form-item>
        </hi-collapse>
      </el-form>
      <el-button class="query-apply" type="primary" @click="postApply">提交申请</el-button>
    </div>

    <el-dialog title="导入常用抬头" :visible.sync="explrotVisible" width="750px" v-if="explrotVisible" :close-on-click-modal="false">
      <el-table :data="TitleData" style="width: 99.9%" height="400">
        <el-table-column label="抬头">
          <template v-slot="{ row }">
            <el-radio v-model="selectInvoiceTitle" :label="row">{{ row.invoiceTitle }}</el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="creditCode" label="信用代码"></el-table-column>
      </el-table>
      <div class="footer" :slot="footer">
        <el-button @click="explrotVisible = false">取消</el-button>
        <el-button type="primary" @click="explrotTitle" :disabled="!selectInvoiceTitle.id">确认导入</el-button>
      </div>
    </el-dialog>

    <el-dialog title="支付邮费流程" :visible.sync="postageVisble" width="900px" :close-on-click-modal="false">
      <img style="width: 850px" :src="require('@/assets/user/postage.png')" />
    </el-dialog>

    <el-dialog title="提示" :visible.sync="repeatVisible" width="750px" :close-on-click-modal="false">
      <p class="repeat-tips">以下订单已在其他已提交的开票申请中，请检查确认后重新申请</p>
      <hi-bill :invoiceList="repeatList" :showTotal="false"></hi-bill>
    </el-dialog>

    <hi-query-apply
      v-if="showQuery"
      :selectType="selectType"
      :invoice="invoice"
      :wxUserOBj="wxUserOBj"
      @close="showQuery = false"
      @query="queryApply"
      :invoiceList="invoiceList"
      :invoiceType="invoiceType"
    ></hi-query-apply>
  </div>
</template>

<script>
var validatePhone = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('联系手机不能为空'))
  } else {
    if (!/^[0-9]{11}$/.test(value)) {
      callback(new Error('联系手机格式不正确'))
    }
    callback()
  }
}
import api from '@/api'
import { Hi, timer } from '@/libs/common'
import { cityData } from '../common/citydata'
import { fenToYuan, yuanToFen } from '@/libs/common'
import HiBill from '../common/bill'
import HiQueryApply from './queryapply'
export default {
  name: 'applyInvoice',
  props: {
    invoiceType: { type: String, default: 'ELEC_INVOICE' },
    invoiceList: { type: Array, default: [] },
    applyType: { type: String },
    id: { type: Number },
    invoiceNo: { type: Number },
    enterpriseConfig: { type: Object, default: null },
  },
  components: {
    HiQueryApply,
    HiBill,
  },
  data() {
    return {
      loading: false,
      showQuery: false,
      postageVisble: false,
      repeatVisible: false,
      uploadingTaxcert: false,
      uploadingHiinvioce: false,
      fenToYuan,
      TitleData: [], //常用抬头table
      selectInvoiceTitle: {}, //所选抬头
      provinceArr: cityData,
      cityArr: [],
      countyArr: [],
      //发票信息
      invoice: {
        invoiceTitle: '',
        creditCode: '',
        registeredAddr: '',
        registeredPhone: '',
        bankAddr: '',
        cardNo: '',
        taxcertificateImgpath: '',
        invoiceType: this.invoiceType,
        tradeNos: [],
        country: '中国',
        province: '',
        city: '',
        county: '',
        detaileAddr: '',
        postcode: '',
        contactName: '',
        contactPhone: '',
        contactEmail: '',
        hiInvoiceImgpath: '',
        invoiceRemark: '',
      },
      //发票类型选项
      provinceTypeList: [
        { name: '默认', type: 'DEFAULT' },
        { name: '工会委员会', type: 'GONGHUI' },
        { name: '个人', type: 'GEREN' },
      ],
      selectType: 'DEFAULT', //发票类型
      explrotVisible: false,
      repeatList: [], //重开的订单
    }
  },
  computed: {
    totalAmount() {
      let total = 0
      this.invoiceList.forEach((item) => {
        total = total + (['REDPACK_RECHARGE', 'REDPACK_AWARDS_RECHARGE'].includes(item.tradeType) ? item.realFee - item.totalFee : item.realFee)
      })
      total = fenToYuan(total).toFixed(2)
      return total
    },
    isEnterprise() {
      return !(
        this.invoiceType === 'DEDICATED_INVOICE' &&
        !!(this.enterpriseConfig && this.enterpriseConfig.id && this.enterpriseConfig.auditState === 'pass')
      )
    },
    invoiceRules() {
      if (this.invoiceType === 'PAPER_INVOICE' || this.invoiceType === 'DEDICATED_INVOICE') {
        return {
          city: [{ required: true, message: '邮寄地址需填写完整', trigger: 'blur' }],
          detaileAddr: [{ required: true, message: '邮寄地址需填写完整', trigger: 'blur' }],
          postcode: [{ required: true, message: '邮寄地址需填写完整', trigger: 'blur' }],
          contactName: [{ required: true, message: '联系人不能为空', trigger: 'blur' }],
          contactPhone: [
            { required: true, message: '手机号不能为空', trigger: 'blur' },
            { validator: validatePhone, trigger: 'blur' },
          ],
          contactEmail: [
            { required: true, message: '邮箱地址不能为空', trigger: 'blur' },
            { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
          ],
          invoiceTitle: [{ required: true, message: '发票抬头不能为空', trigger: 'blur' }],
          creditCode: [
            { required: true, message: '信用代码不能为空', trigger: 'blur' },
            {
              pattern: /^(?:[A-Za-z0-9]{15}|[A-Za-z0-9]{17}|[A-Za-z0-9]{18}|[A-Za-z0-9]{20})$/,
              message: '统一社会信用代码格式不正确',
              trigger: 'blur',
            },
          ],
        }
      } else {
        return {
          contactName: [{ required: true, message: '联系人不能为空', trigger: 'blur' }],
          contactPhone: [
            { required: true, message: '手机号不能为空', trigger: 'blur' },
            { validator: validatePhone, trigger: 'blur' },
          ],
          contactEmail: [
            { required: true, message: '邮箱地址不能为空', trigger: 'blur' },
            { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
          ],
          invoiceTitle: [{ required: true, message: '发票抬头不能为空', trigger: 'blur' }],
          creditCode: [
            { required: true, message: '信用代码不能为空', trigger: 'blur' },
            {
              pattern: /^(?:[A-Za-z0-9]{15}|[A-Za-z0-9]{17}|[A-Za-z0-9]{18}|[A-Za-z0-9]{20})$/,
              message: '统一社会信用代码格式不正确',
              trigger: 'blur',
            },
          ],
        }
      }
    },
  },
  watch: {
    'invoice.province': {
      handler(v) {
        let arr = []
        this.provinceArr.forEach((item) => {
          if (item.name === v) {
            this.cityArr = item.city
          }
        })
        this.invoice.city = this.cityArr[0].name
      },
    },
    'invoice.city': {
      handler(v) {
        let arr = []
        this.cityArr.forEach((item) => {
          if (item.name === v) {
            this.countyArr = item.area
          }
        })
        this.invoice.county = this.countyArr[0].name
      },
    },
  },
  methods: {
    //重开情况的发票信息获取
    async fetchInvoiveData() {
      try {
        this.loading = true
        let data = await api.invoicemanage.read({
          where: { id: this.id },
        })
        Object.keys(this.invoice).forEach((key) => {
          if (data[key]) {
            this.invoice[key] = data[key]
          }
        })
      } catch (err) {
        console.log(err)
        this.$notify.error({ title: '错误', message: err ? err.msg || '获取数据失败！' : '获取数据失败！' })
      }
    },
    //重开情况的发票包含订单
    async renderBillList() {
      let data = await api.invoicetradeno.list({
        where: {
          invoiceNo: this.invoiceNo,
        },
      })
      let tradeNoList = []
      data.forEach((item) => {
        tradeNoList.push(item.tradeNo)
      })
      this.invoiceList = await api.hipay.list({
        where: {
          tradeNoList,
        },
      })
    },
    //提交申请
    async postApply() {
      try {
        this.$refs.invoiceForm.clearValidate()
        await this.$refs.invoiceForm.validate()
        if (this.invoiceType === 'PAPER_INVOICE' && this.totalAmount < 400 && !this.invoice.hiInvoiceImgpath) {
          this.$notify.error({ title: '错误', message: '请上传发票邮寄费用支付截图！' })
          return
        }
        this.showQuery = true
      } catch (err) {
        console.log(err)
        this.$notify.error({ title: '错误', message: '必填项不能为空！' })
      }
    },
    //确认申请
    async queryApply() {
      try {
        //查询订单重开情况
        let data = await api.invoicetradeno.issuedlist({
          where: {
            tradeNoList: this.invoice.tradeNos,
          },
        })
        if (data && data.length) {
          let tradeNoList = []
          data.forEach((item) => {
            tradeNoList.push(item.tradeNo)
          })
          let repeatList = await api.hipay.list({
            where: {
              tradeNoList,
            },
            sort: { id: 'asc' },
          })
          this.repeatVisible = true
          this.repeatList = repeatList
          return
        } else {
          let postData = Hi.Object.copy(this.invoice)
          let data = await api.invoicemanage.add({
            ...postData,
            ownerType: this.selectType,
            totalAmount: yuanToFen(this.totalAmount),
          })
          this.$notify.success({ title: '成功', message: '提交成功！' })
          this.$emit('changeCurrent', 'HiInvoiceManage')
        }
      } catch (err) {
        console.log(err)
        this.$notify.error({ title: '错误', message: err ? err.msg || '提交失败！' : '提交失败！' })
      }
    },
    //渲染title列表
    async renderTitleTable() {
      try {
        this.loading = true
        let data = await api.invoice.list({
          where: {},
        })
        this.TitleData = data
      } catch (error) {
        console.log(error)
        this.$notify.error(error.msg)
      }
      this.loading = false
    },
    //导入抬头
    explrotTitle() {
      this.$refs.invoiceForm.clearValidate()
      this.selectInvoiceTitle.invoiceTitle && (this.invoice.invoiceTitle = this.selectInvoiceTitle.invoiceTitle)
      this.selectInvoiceTitle.creditCode && (this.invoice.creditCode = this.selectInvoiceTitle.creditCode)
      this.selectInvoiceTitle.registeredAddr && (this.invoice.registeredAddr = this.selectInvoiceTitle.registeredAddr)
      this.selectInvoiceTitle.registeredPhone && (this.invoice.registeredPhone = this.selectInvoiceTitle.registeredPhone)
      this.selectInvoiceTitle.bankAddr && (this.invoice.bankAddr = this.selectInvoiceTitle.bankAddr)
      this.selectInvoiceTitle.cardNo && (this.invoice.cardNo = this.selectInvoiceTitle.cardNo)
      this.selectInvoiceTitle.taxcertificateImgpath && (this.invoice.taxcertificateImgpath = this.selectInvoiceTitle.taxcertificateImgpath)
      this.explrotVisible = false
    },
    async uploadSuccess(res, type) {
      let url = Hi.String.dealUrl(res.data.url)
      if (type === 'taxcertificateImgpath') {
        this.invoice.taxcertificateImgpath = url
      } else {
        this.invoice.hiInvoiceImgpath = url
      }
    },
    changeSelectType() {
      let obj = {
        invoiceTitle: '',
        creditCode: '',
        registeredAddr: '',
        registeredPhone: '',
        bankAddr: '',
        cardNo: '',
        taxcertificateImgpath: '',
        country: '中国',
        province: '',
        city: '',
        county: '',
        detaileAddr: '',
        postcode: '',
        contactName: '',
        contactPhone: '',
        contactEmail: '',
        hiInvoiceImgpath: '',
        invoiceRemark: '',
      }
      this.invoice = Object.assign({}, this.invoice, obj)
    },
  },
  async mounted() {
    this.renderTitleTable()
    this.invoiceList.forEach((item) => {
      this.invoice.tradeNos.push(item.tradeNo)
    })
    if (this.id && this.applyType === 'again') {
      this.loading = true
      await Promise.all([this.fetchInvoiveData(), this.renderBillList()])
      this.invoiceList.forEach((item) => {
        this.invoice.tradeNos.push(item.tradeNo)
      })
      this.loading = false
    }
    if (this.enterpriseConfig.id) {
      this.invoice.invoiceTitle = this.enterpriseConfig.companyName
      this.invoice.creditCode = this.enterpriseConfig.creditCode
    }
  },
}
</script>

<style scoped lang="stylus">
.apply-wrap {
  width: 100%;

  .mrg-l-10 {
    margin-left: 10px;
  }

  .mrg-l-15 {
    margin-left: 15px;
  }

  .mrg-t-40 {
    margin-top: 40px;
  }

  .mrg-t-20 {
    margin-top: 20px;
  }

  .w-120 {
    width: 132px;
  }

  .w-360 {
    width: 360px;
  }

  .w-130 {
    width: 130px;
  }

  .red {
    color: red;
  }

  .f-16 {
    font-size: 16px;
  }

  .f-14 {
    font-weight: 14px;
  }

  .top {
    position: relative;
  }

  .expload {
    position: absolute;
  }

  .content {
    margin-top: 15px;

    .form-title {
      font-size: 18px;
      font-weight: 600;
      margin: 15px 0 25px 0;
    }

    .w500 {
      width: 500px;
    }

    .up-box {
      margin-left: -160px;
      line-height: 1.5 !important;
    }

    .uploader {
      width: 195px;
    }

    .uploader >>>.el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      margin-top: 10px;
    }

    .uploader >>>.el-upload:hover {
      border-color: #409EFF;
    }

    .uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      line-height: 178px;
      text-align: center;
    }

    .img {
      width: 178px;
      height: 178px;
      display: block;
    }

    .img-tips {
      color: #999;
    }

    .station-box {
      width: 500px;

      .country {
        width: 70px;
        height: 30px;
        border: 1px solid #DCDFE6;
        border-radius: 4px;
        text-align: center;
        color: #606266;
      }
    }

    .look-postage {
      color: #4886FF;
      cursor: pointer;
      margin-left: 10px;
      font-size: 12px;
    }
  }

  .query-apply {
    margin: 10px 0 20px 200px;
  }

  .footer {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
  }

  .repeat-tips {
    width: 100%;
    background: rgb(255, 250, 221);
    height: 30px;
    line-height: 30px;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
  }

  >>>.el-dialog__body {
    position: relative;
  }

  .postcode {
    position: absolute;
    left: 610px;
    top: 0px;

    >>>.el-form-item__content {
      margin-left: 0 !important;
    }
  }
}

.toUpperCase >>> .el-input__inner {
  text-transform: uppercase !important;
}
</style>
