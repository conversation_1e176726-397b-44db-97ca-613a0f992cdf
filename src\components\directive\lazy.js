import Vue from 'vue'

/**
 * 基础配置
 */
const CONFIG = {
  rootMargin: '50px',
  threshold: 0.1,
  retryAttempts: 2,
  retryDelay: 1000,
}

/**
 * 生成轻量级SVG占位图
 * @param {number} width - 宽度
 * @param {number} height - 高度
 * @returns {string} SVG数据URL
 */
const createPlaceholder = (width = 300, height = 200) => {
  // 使用简洁的SVG，避免中文字符编码问题
  const svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#f2f6ff;stop-opacity:1" /><stop offset="50%" style="stop-color:#e4edff;stop-opacity:1" /><stop offset="100%" style="stop-color:#d6e4ff;stop-opacity:1" /></linearGradient></defs><rect width="100%" height="100%" fill="url(#grad)" /><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#999" font-family="Arial" font-size="14">Loading...</text></svg>`

  // 直接使用URL编码，更兼容且避免Base64编码问题
  return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`
}

/**
 * 获取元素尺寸
 * @param {HTMLElement} el - DOM元素
 * @returns {Object} 尺寸对象
 */
const getElementSize = (el) => {
  const rect = el.getBoundingClientRect()
  return {
    width: Math.max(rect.width || el.offsetWidth || 300, 100),
    height: Math.max(rect.height || el.offsetHeight || 200, 100),
  }
}

/**
 * 图片加载处理
 * @param {HTMLElement} img - 图片元素
 * @param {string} src - 图片源地址
 * @param {number} retryCount - 重试次数
 */
const loadImage = (img, src, retryCount = 0) => {
  const newImg = new Image()

  newImg.onload = () => {
    img.setAttribute('src', src)
    img.classList.remove('lazy-loading')
    img.classList.add('lazy-loaded')
  }

  newImg.onerror = () => {
    if (retryCount < CONFIG.retryAttempts) {
      // 重试加载
      setTimeout(() => {
        loadImage(img, src, retryCount + 1)
      }, CONFIG.retryDelay * (retryCount + 1))
    } else {
      // 加载失败，显示错误状态
      img.classList.remove('lazy-loading')
      img.classList.add('lazy-error')
    }
  }

  newImg.src = src
}

/**
 * 创建 IntersectionObserver 实例
 * @returns {IntersectionObserver} Observer实例
 */
const createObserver = () => {
  return new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target
          const src = img.getAttribute('data-src')

          if (src && img instanceof HTMLImageElement) {
            loadImage(img, src)
            observer.unobserve(img)
          }
        }
      })
    },
    {
      rootMargin: CONFIG.rootMargin,
      threshold: CONFIG.threshold,
    }
  )
}

// 全局观察器实例
let observer = null

/**
 * 初始化观察器
 */
const initObserver = () => {
  if (!observer && window.IntersectionObserver) {
    observer = createObserver()
  }
}

/**
 * 处理图片懒加载
 * @param {HTMLElement} el - 图片元素
 * @param {Object} binding - Vue指令绑定对象
 */
const handleLazy = (el, binding) => {
  initObserver()

  // 确保是img元素并进行类型转换
  const img = el

  if (!observer) {
    // 不支持 IntersectionObserver，直接加载图片
    img.setAttribute('src', binding.value || createPlaceholder())
    return
  }

  // 获取元素尺寸并生成占位图
  const size = getElementSize(img)
  const placeholder = createPlaceholder(size.width, size.height)

  // 设置占位图
  img.setAttribute('src', placeholder)

  // 保存真实图片地址
  img.setAttribute('data-src', binding.value)

  // 添加加载中的样式类
  img.classList.add('lazy-loading')

  // 开始观察元素
  observer.observe(img)
}

/**
 * 清理懒加载观察
 * @param {HTMLElement} el - 图片元素
 */
const cleanupLazy = (el) => {
  if (observer) {
    observer.unobserve(el)
  }

  // 清理数据和样式
  el.removeAttribute('data-src')
  el.classList.remove('lazy-loading', 'lazy-loaded', 'lazy-error')
}

// 注册全局自定义指令 v-lazy
Vue.directive('lazy', {
  bind(el, binding) {
    // 确保是img元素
    if (el.tagName.toLowerCase() !== 'img') {
      console.warn('[v-lazy] directive can only be used on img elements')
      return
    }

    handleLazy(el, binding)
  },

  update(el, binding) {
    // 如果图片地址发生变化
    if (binding.value !== binding.oldValue) {
      // 先清理旧的观察
      cleanupLazy(el)

      // 重新设置懒加载
      if (binding.value) {
        handleLazy(el, binding)
      }
    }
  },

  unbind(el) {
    cleanupLazy(el)
  },
})

// 添加默认样式
const style = document.createElement('style')
style.textContent = `
  .lazy-loading {
    opacity: 0.6;
    transition: opacity 0.3s ease;
  }

  .lazy-loaded {
    opacity: 1;
    transition: opacity 0.3s ease;
  }

  .lazy-error {
    opacity: 0.8;
    filter: grayscale(100%);
  }
`
document.head.appendChild(style)
