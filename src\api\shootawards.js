import api from './api'

export default {
  list: postData => api.fetchBaseData('/pro/hxc/proshootawards/list.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/proshootawards/batch.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/proshootawards/add.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/proshootawards/delete.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proshootawards/update.htm', postData),
}
