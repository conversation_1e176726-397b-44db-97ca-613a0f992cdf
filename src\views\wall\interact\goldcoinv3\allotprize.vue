<template>
  <div class="allot-prize-box pad-t-10">
    <el-form :model="awardObj" :rules="rules" ref="allot" label-width="116px">
      <el-form-item label="奖池总额：" prop="redpackAmount">
        <el-input-number
          v-model="awardObj.redpackAmount"
          controls-position="right"
          :precision="2"
          :min="0.3"
          :step="0.1"
          :max="100000"
          @change="changeTotal"
          :disabled="isEdit || _disable"
        ></el-input-number>
        <span class="margin-space">元</span>
        <span class="f999">红包账户余额：</span>
        <span class="fred">{{ accountData.redpackCount | fenToYuan }}元</span>
        <p v-if="isShowError" class="el-form-item__error" style="height: auto">
          <span
            >您当前设置的红包总额≥20000，为确保资金发放顺利，建议<a style="text-decoration: underline" href="javascript:;" @click="customerService"
              >联系客服</a
            >报备</span
          >
        </p>
      </el-form-item>
      <el-form-item label="奖励规则：" prop="singleAmount">
        <span>用户每获得 <strong class="fred">1</strong> 积分，</span>
        <span>获得现金奖励</span>
        <el-input class="w-80" v-model.trim="awardObj.singleAmount" :disabled="isEdit || _disable"></el-input>
        <span class="margin-space">元</span>
        <span>
          <el-button type="text" style="font-size: 14px" :disabled="isEdit" @click.stop="handlePayment">立即支付</el-button>
        </span>
      </el-form-item>
      <el-form-item label="主办方名称：" prop="redpackSellername">
        <div class="position-r w-348">
          <el-input v-model.trim="awardObj.redpackSellername" placeholder="不要超过15个字" :maxlength="15"></el-input>
          <span class="text-hint">{{ awardObj.redpackSellername.length }}/15</span>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import api from '@/api'
import { Hi, fenToYuan, yuanToFen } from '@/libs/common'
import { mapActions, mapGetters } from 'vuex'

export default {
  name: 'AllotPrize',
  props: {
    actConfig: {
      type: Object,
      default: () => ({}),
    },
    wall: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      initAwards: {}, //奖品元数据
      awardObj: {
        type: 'REDPACK',
        redpackAmount: 1,
        singleAmount: 0.01,
        redpackSellername: '主办方',
        forbidDraw: 'N', // 禁止抽中
        mode: 'FIXED',
        count: 1,
      },
      awardList: [],
      once: false,
    }
  },
  computed: {
    ...mapGetters({
      accountData: 'useraccount/getAccount',
    }),
    isEdit() {
      return this.awardObj.status && this.awardObj.status !== 'WAIT_PAY'
    },
    isTest() {
      return this.wall.wallVersion && this.wall.wallVersion === 'test'
    },
    isShowError() {
      return !this.isTest && this.awardObj.redpackAmount >= 20000 && fenToYuan(this.accountData.redpackCount) >= 20000
    },
    rules() {
      return {
        redpackAmount: [{ required: true, validator: this.valiTotal, trigger: 'blur' }],
        singleAmount: [{ validator: this.valiSingle, trigger: 'blur' }],
      }
    },
    _moduleName() {
      return this.$route.name.split('-')[2]
    },
    _disable() {
      if (this.awardList.length > 0 && this.actConfig.id) {
        return true
      }
      return false
    },
  },
  watch: {
    awardObj: {
      deep: true,
      immediate: true,
      handler(v) {
        if (this.actConfig.id) {
          const pass = !Hi.Object.difference(this.awardObj, this.initAwards)
          this.$emit('limit', pass)
        }
        this.$emit('allotChange', v)
      },
    },
    'actConfig.id': {
      async handler(v) {
        // 有值则代表创建轮次成功 并且没有通过组件创建的轮次
        if (v && this.awardList.length === 0) {
          await this.addAwards(v)
          await this.featchAwardList()
          await this.handlePayment(v)
        }
      },
    },
    'awardObj.redpackAmount': {
      handler(v) {
        if (this.isTest && v > 20) {
          this.$emit('limit', true)
          return
        }
        this.$emit('limit', false)
      },
    },
  },
  methods: {
    ...mapActions({
      fetchBlance: 'useraccount/fetchBlance',
    }),
    customerService() {
      this.$alert(
        `<img
        style="display: block; margin: auto;width: 200px;"
        src="https://res3.hixianchang.com/qn/up/eadd61987c6343c7d238853465b5ff2b.png"
        alt=""
      />
      <p style="text-align:center;">微信扫描二维码添加</p>
      `,
        '客服微信',
        {
          dangerouslyUseHTMLString: true,
        }
      )
    },
    valiTotal(rule, value, callback) {
      if (!value) {
        callback(new Error('奖池总额不能为空'))
      } else {
        if (this.isTest && value > 20) {
          callback(new Error('测试活动红包总额仅可设置1.00~20.00'))
          return
        }
        // if (value > fenToYuan(this.accountData.redpackCount)) {
        //   callback(new Error('奖池总额不能超过红包账户余额'))
        //   return
        // }
        if (value > 100000) {
          callback(new Error('奖池总额最大金额为100000'))
        }
        callback()
      }
    },
    valiSingle(rule, value, callback) {
      if (!value) {
        callback(new Error('单次数钱金额不能为空'))
      } else {
        let reg = /^([1-9][\d]{0,4}|0)(\.[\d]{1,2})?$/
        if (!reg.test(value)) {
          callback(new Error('单次数钱金额必须为数字，且最多精确到分'))
        }
        if (value < 0.01) {
          callback(new Error('单次数钱金额最低0.01元'))
        }
        if (value > this.awardObj.redpackAmount) {
          callback(new Error('单次数钱金额不能大于奖池总额'))
        }
        if (value > 100) {
          callback(new Error('单次数钱金额最大金额为100'))
        }
        callback()
      }
    },
    changeTotal() {
      this.$refs.allot.validate('singleAmount')
    },
    async validate() {
      await this.$refs.allot.validate()
    },
    async featchAwardList() {
      if (!this.actConfig.id) return
      try {
        this.awardList = await api.awards.list({
          where: {
            wallId: this.wall.id,
            module: this._moduleName,
            moduleId: this.actConfig.id,
          },
          sort: { id: 'asc' },
        })
        let obj = Hi.Object.copy(this.awardList[0])
        obj.redpackAmount = fenToYuan(obj.redpackAmount)
        obj.singleAmount = fenToYuan(obj.singleAmount)
        this.awardObj = Hi.Object.copy(obj)
        this.initAwards = Hi.Object.copy(obj)
      } catch (e) {
        console.log(e)
        this.$notify.error({
          title: '错误',
          message: e ? e.msg || '获取奖品失败，请稍后重试！' : '获取奖品失败，请稍后重试！',
        })
      }
    },
    async addAwards(moduleId) {
      try {
        await api.awards.add({
          ...this.awardObj,
          moduleId,
          wallId: this.wall.id,
          module: this._moduleName,
          redpackAmount: yuanToFen(this.awardObj.redpackAmount),
          singleAmount: yuanToFen(this.awardObj.singleAmount),
        })
      } catch (e) {
        console.log(e)
        this.$notify.error({
          title: '错误',
          message: e?.msg || '添加奖品失败，请稍后重试！',
        })
      }
    },
    //提交奖品信息
    async updataAwards() {
      try {
        if (this.awardObj.id) {
          const update = Hi.Object.difference(this.awardObj, this.initAwards)
          if (update) {
            await api.awards.update({
              where: {
                id: this.awardObj.id,
                wallId: this.wall.id,
                module: this._moduleName,
                moduleId: this.actConfig.id,
              },
              update,
            })
            await this.featchAwardList.call(this)
          }
        }
      } catch (e) {
        console.log(e)
      }
    },
    async handlePayment(id) {
      try {
        if (!this.actConfig.id) {
          await this.$emit('save')
          return
        }
        const isPreOrder = this.awardObj.unifiedorderFlag === 'Y' && this.awardObj.tradeNo !== null
        if (isPreOrder) {
          await this.processPreOrderPayment()
        } else {
          await this.processRegularPayment(id)
        }
      } finally {
        await this.featchAwardList()
      }
    },

    async processPreOrderPayment() {
      const orderInfo = await api.hipay.orderAgain({ tradeNo: this.awardObj.tradeNo })
      const money = this.awardObj.redpackAmount
      const commission = Hi.Math.accDiv(Math.ceil(Hi.Math.accMul(Hi.Math.accDiv(Number(money), 100), 100)), 100).toFixed(2)
      const realFee = Hi.Math.accAdd(Number(money), Number(commission)).toFixed(2)
      const pay = {
        payObj: {
          payType: orderInfo.payType,
          orderId: orderInfo.orderId,
          realFee,
          codeUrl: orderInfo.codeUrl,
        },
        type: 'qrcodepay',
      }
      await this.$hi.pay(pay)
    },

    async processRegularPayment(id) {
      const payObj = {
        tradeType: 'REDPACK_AWARDS_RECHARGE',
        payType: 'alipay',
      }
      const items = [
        {
          vatKey: this.awardObj.id || id,
          vatName: '红包充值',
          vatPrice: this.awardObj.redpackAmount,
          count: 1,
        },
      ]
      await this.$hi.pay({ payObj, items, type: 'order' })
    },
  },
  async mounted() {
    await this.fetchBlance.call(this)
    if (this.actConfig.id) {
      await this.featchAwardList.call(this)
    }
  },
}
</script>
<style scoped lang="stylus">
.pad-t-10 {
  padding-top: 10px;
}

.w-80 {
  width: 80px;
}

.w-348 {
  width: 348px;
}

.position-r {
  position: relative;
}

.text-hint {
  position: absolute;
  right: 4px;
  top: 0;
  color: #c0c4cc;
}

.margin-space {
  margin: 0 16px 0 14px;
}

.mar-l-6 {
  margin-left: 6px;
}

.mar-r-10 {
  margin-right: 10px;
  width: 168px;
}
</style>
