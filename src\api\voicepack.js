import api from './api'
export default {
  page: postData => api.fetchBaseData('/pro/hxc/provoicepack/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/provoicepack/list.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/provoicepack/add.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/provoicepack/update.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/provoicepack/delete.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/provoicepack/read.htm', postData),
  voicelist: postData => api.fetchBaseData('/pro/hxc/provoicepack/voicelist.htm', postData),
  voicedelete: postData => api.fetchBaseData('/pro/hxc/provoicepack/voicedelete.htm', postData),
  voiceadd: postData => api.fetchBaseData('/pro/hxc/provoicepack/voiceadd.htm', postData),
}
