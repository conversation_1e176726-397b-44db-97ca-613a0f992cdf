<template>
  <el-dialog width="715px" :title="title" :visible="visible" :close-on-click-modal="false" @open="handleOpen" @close="handleClose" top="8vh">
    <el-form ref="questionForm" :model="editObject" :rules="rules" label-width="80px">
      <!-- 题目类型 -->
      <el-form-item label="类型" prop="type">
        <el-radio-group v-model="editObject.type" @change="handleChangeType">
          <el-radio label="RADIO">单选</el-radio>
          <el-radio label="CHECKBOX">多选</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 题目内容 -->
      <el-form-item label="题目" prop="title">
        <div class="question-content">
          <div v-if="editObject.subjectContent.type !== 'TEXT'" class="question-media">
            <!-- 图片显示 -->
            <div v-if="editObject.subjectContent.type === 'IMAGE'" class="question-img-view">
              <img :src="editObject.subjectContent.resource" alt="题目图片" />
              <p class="delete-btn" @click="handleDeleteResource('IMAGE')">
                <i class="el-icon-delete"></i>
              </p>
            </div>
            <!-- 视频显示 -->
            <div v-else-if="editObject.subjectContent.type === 'VIDEO'" class="question-video-view">
              <video :src="editObject.subjectContent.resource" controls></video>
              <p class="delete-btn" @click="handleDeleteResource('VIDEO')">
                <i class="el-icon-delete"></i>
              </p>
            </div>
            <!-- 音频显示 -->
            <div v-else-if="editObject.subjectContent.type === 'AUDIO'" class="question-audio-view">
              <hi-audio-player :src="editObject.subjectContent.resource" :autoplay="false"></hi-audio-player>
              <div class="delete-btn" @click="handleDeleteResource('AUDIO')">
                <i class="el-icon-delete"></i>
              </div>
            </div>
          </div>
          <el-input
            v-model.trim="editObject.subjectContent.content"
            class="question-input"
            resize="none"
            type="textarea"
            :rows="4"
            :maxlength="500"
            show-word-limit
            placeholder="请输入题目内容"
          ></el-input>
        </div>
      </el-form-item>
      <!-- 媒体上传按钮 -->
      <div class="media-upload-buttons">
        <hi-upload-img
          class="upload-btn"
          :action="$hi.url.upload"
          :show-file-list="false"
          :before-upload="handleBeforeUploadResourceImg"
          :on-success="onUploadResourceImg"
          :on-error="handleUploadResourceError"
          :disabled="isAnyUploading && !uploadStates.resourceImg"
        >
          <el-button size="small" icon="el-icon-picture" :loading="uploadStates.resourceImg" :disabled="isAnyUploading && !uploadStates.resourceImg">
            {{ uploadStates.resourceImg ? '上传中...' : '添加图片' }}
          </el-button>
        </hi-upload-img>
        <hi-upload-media
          class="upload-btn"
          :action="$hi.url.uploadvideo"
          accept="video/mp4"
          :show-file-list="false"
          :before-upload="handleBeforeUploadResourceVideo"
          :on-success="onUploadResourceVideo"
          :on-error="handleUploadResourceError"
          :maxSize="5"
          :disabled="isAnyUploading && !uploadStates.resourceVideo"
        >
          <el-button
            size="small"
            icon="el-icon-video-camera"
            :loading="uploadStates.resourceVideo"
            :disabled="isAnyUploading && !uploadStates.resourceVideo"
          >
            {{ uploadStates.resourceVideo ? '上传中...' : '添加视频' }}
          </el-button>
        </hi-upload-media>
        <hi-upload-audio
          class="upload-btn"
          :action="$hi.url.uploadaudio"
          :show-file-list="false"
          :before-upload="handleBeforeUploadResourceAudio"
          :on-success="onUploadResourceAudio"
          :on-error="handleUploadResourceError"
          :disabled="isAnyUploading && !uploadStates.resourceAudio"
        >
          <el-button
            size="small"
            icon="el-icon-microphone"
            :loading="uploadStates.resourceAudio"
            :disabled="isAnyUploading && !uploadStates.resourceAudio"
          >
            {{ uploadStates.resourceAudio ? '上传中...' : '添加音频' }}
          </el-button>
        </hi-upload-audio>
        <span class="upload-hint">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <p>视频：mp4格式，建议大小不超过20M</p>
              <p>音频：mp3格式，建议大小不超过5M</p>
              <p>图片：jpg、png格式，大小不超过5M</p>
            </div>
            <i class="el-icon-question"></i>
            <span>各种尺寸规范</span>
          </el-tooltip>
        </span>
      </div>
    </el-form>

    <!-- 选项管理 -->
    <div class="options-section">
      <div class="options-section-title">
        <div class="flex flex-a-c flex-j-c">
          <h4>选项设置</h4>
          <span style="color: #909399; font-size: 12px; margin-left: 10px">每个选项至少设置一张图片或文字内容</span>
        </div>
        <el-button :disabled="editObject.options.length >= 6" type="primary" @click="handleAddOption"> 添加选项 </el-button>
      </div>
      <div class="option-header">
        <div class="option-col-index">选项</div>
        <div class="option-col-img">图片</div>
        <div class="option-col-content">内容</div>
        <div class="option-col-answer">答案</div>
        <div class="option-col-action">操作</div>
      </div>
      <div class="option-list">
        <div class="option-item" v-for="(option, index) in editObject.options" :key="index">
          <div class="option-col-index">
            <div class="option-index-view">
              {{ String.fromCharCode(65 + index) }}
            </div>
          </div>
          <div class="option-col-img">
            <div class="option-img-view">
              <hi-upload-img
                class="upload-change"
                :action="$hi.url.upload"
                :show-file-list="false"
                :show-loading="true"
                :before-upload="() => handleBeforeUploadOptionImg(index)"
                :on-success="(res) => onUploadOptionImgSuccess(index, res)"
                :on-error="() => handleUploadOptionImgError(index)"
                :disabled="isAnyUploading && !uploadStates.optionImgs[index]"
              >
                <i v-if="!option.img" class="el-icon-plus avatar-uploader-icon"></i>
                <img v-else :src="option.img" alt="选项图片" />
                <div v-if="option.img" class="delete-btn-option" @click.stop="handleDeleteOptionImg(index)">
                  <i class="el-icon-delete"></i>
                </div>
              </hi-upload-img>
            </div>
          </div>
          <div class="option-col-content">
            <el-input v-model.trim="option.title" :maxlength="500" placeholder="请输入选项内容" show-word-limit></el-input>
          </div>
          <div class="option-col-answer">
            <el-checkbox v-model="option.rightAnswer" true-label="Y" false-label="N" @change="handleChangeRightAnswer(index)"></el-checkbox>
          </div>
          <div class="option-col-action">
            <el-button
              v-if="editObject.options.length > 2"
              type="text"
              icon="el-icon-close"
              style="color: #f56c6c"
              @click="handleDeleteOption(index)"
            ></el-button>
            <el-button v-else type="text" disabled icon="el-icon-close"></el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="other-settings">
      <h4>其他设置</h4>
      <el-form ref="questionForm" :model="editObject" :rules="rules" label-width="80px">
        <el-form-item label="题目注释" prop="remark">
          <el-input
            class="question-input"
            v-model="editObject.remark"
            type="textarea"
            :rows="3"
            :maxlength="500"
            show-word-limit
            placeholder="用于答题结束后对该问题进行解释"
          ></el-input>
        </el-form-item>

        <!-- 用户题库 -->
        <template v-if="model === 'user'">
          <el-form-item label="题目分组" prop="groupId">
            <el-select v-model="editObject.groupId" placeholder="请选择分组" class="w-200">
              <el-option v-for="item in subjectGroupList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>

          <!-- 题目难度 -->
          <el-form-item label="题目难度" prop="difficultyId">
            <el-select v-model="editObject.difficultyId" placeholder="请选择难度" class="w-200" @change="handleDifficultyChange">
              <el-option v-for="item in subjectDifficultyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>

          <!-- 答题得分 -->
          <el-form-item label="答对得分" prop="score">
            {{ scoreObj.rightScore }}
            <span style="margin-left: 8px; color: #909399">分</span>
          </el-form-item>

          <!-- 答错扣分 -->
          <el-form-item label="答错扣分" prop="wrongScore">
            {{ scoreObj.wrongScore }}
            <span style="margin-left: 8px; color: #909399">分</span>
          </el-form-item>

          <!-- 答题时间 -->
          <el-form-item label="答题时间" prop="timeLimit">
            {{ scoreObj.answerTime }}
            <span style="margin-left: 8px; color: #909399">秒</span>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="答对得分" prop="rightScore">
            <el-input-number
              class="w-100"
              v-model.number="editObject.rightScore"
              :min="0"
              :max="99999"
              controls-position="right"
              placeholder="请输入答对得分"
            ></el-input-number>
            <span style="margin-left: 8px; color: #909399">分</span>
          </el-form-item>
          <el-form-item label="答错扣分" prop="wrongScore">
            <el-input-number
              class="w-100"
              v-model.number="editObject.wrongScore"
              :min="0"
              :max="99999"
              controls-position="right"
              placeholder="请输入答错扣分"
            ></el-input-number>
            <span style="margin-left: 8px; color: #909399">分</span>
          </el-form-item>
          <el-form-item label="答题时间" prop="answerTime">
            <el-input-number
              class="w-100"
              v-model.number="editObject.answerTime"
              :min="0"
              :max="99999"
              controls-position="right"
              placeholder="请输入答题时间"
            ></el-input-number>
            <span style="margin-left: 8px; color: #909399">秒</span>
          </el-form-item>
          <p class="text-red">注：轮次内编辑调整题目，不会影响题库内添加的该题目</p>
        </template>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        {{ editObject.id || editObject.uuid ? '更新' : '保存' }}
      </el-button>
      <el-button v-if="!editObject.id && !editObject.uuid" type="success" @click="handleSave('continue')" :loading="saving">
        保存并继续添加
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { Hi } from '@/libs/common'
import { mapActions, mapGetters, mapState } from 'vuex'

// 用户题库
const defaultData = {
  type: 'RADIO', // 类型
  subjectContent: {
    type: 'TEXT',
    content: '',
    resource: '', // 资源
  },
  options: [
    { img: '', rightAnswer: 'N', title: '' },
    { img: '', rightAnswer: 'N', title: '' },
  ], // 选项
  answer: '', // 答案
  remark: '', // 注释
  difficultyId: null, // 题目难度
  groupId: null, // 题目分组
}

export default {
  name: 'QuestionForm',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    question: {
      type: Object,
      default: () => ({}),
    },
    model: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      editObject: null,
      scoreObj: {
        rightScore: 10, // 答对得分
        wrongScore: 10, // 答错扣分
        answerTime: 0, // 答题时间
      },
      // 上传状态管理
      uploadStates: {
        resourceImg: false, // 题目图片上传状态
        resourceVideo: false, // 题目视频上传状态
        resourceAudio: false, // 题目音频上传状态
        optionImgs: {}, // 选项图片上传状态 {index: boolean}
      },
    }
  },
  computed: {
    ...mapState('questionBank', ['saving', 'subjectDifficultyList', 'subjectGroupList']),
    ...mapGetters({
      wallFlagObj: 'wall/getWallFlagObj',
      defaultGroup: 'questionBank/defaultGroup',
    }),
    title() {
      if (this.editObject?.id || this.editObject?.uuid) {
        return '编辑题目'
      }
      return '新增题目'
    },
    wall() {
      return this.wallFlagObj[this.$route.query.wallFlag]
    },
    rules() {
      return {}
    },
    // 检查是否有任何上传正在进行
    isAnyUploading() {
      return (
        this.uploadStates.resourceImg ||
        this.uploadStates.resourceVideo ||
        this.uploadStates.resourceAudio ||
        Object.values(this.uploadStates.optionImgs).some((state) => state)
      )
    },
  },
  watch: {
    question: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.initForm()
        }
      },
      deep: true,
      immediate: true,
    },
    'editObject.options': {
      handler(newVal) {
        const correctAnswers = []
        newVal.forEach((item, index) => {
          if (item.rightAnswer === 'Y') {
            correctAnswers.push(String.fromCharCode(65 + index))
          }
        })
        this.editObject.answer = correctAnswers.join(',')
      },
      deep: true,
    },
  },
  methods: {
    ...mapActions('questionBank', ['saveQuestion', 'fetchGroups']),
    // 重置上传状态
    resetUploadStates() {
      this.uploadStates = {
        resourceImg: false,
        resourceVideo: false,
        resourceAudio: false,
        optionImgs: {},
      }
    },

    // 初始化表单
    initForm() {
      // 重置上传状态
      this.resetUploadStates()

      if (this.question && (this.question.id || this.question.uuid)) {
        this.editObject = Hi.Object.copy(this.question)
      } else {
        if (this.model === 'user') {
          this.editObject = Hi.Object.copy(defaultData)
        } else {
          const data = Hi.Object.copy(defaultData)
          delete data.difficultyId
          delete data.groupId
          data.rightScore = 0
          data.wrongScore = 0
          data.answerTime = 0
          this.editObject = data
        }
      }

      if (!this.editObject.id && !this.editObject.uuid) {
        if (this.subjectDifficultyList.length > 0) {
          this.editObject.difficultyId = this.subjectDifficultyList[0].id
        } else {
          this.editObject.difficultyId = ''
        }
        if (this.editObject.groupId === 0) {
          this.editObject.groupId = null
        }
      }
      this.handleDifficultyChange(this.editObject.difficultyId)
    },
    // 上传前处理 - 题目资源
    handleBeforeUploadResourceImg() {
      this.uploadStates.resourceImg = true
    },
    handleBeforeUploadResourceVideo() {
      this.uploadStates.resourceVideo = true
    },
    handleBeforeUploadResourceAudio() {
      this.uploadStates.resourceAudio = true
    },

    // 上传前处理 - 选项图片
    handleBeforeUploadOptionImg(index) {
      this.$set(this.uploadStates.optionImgs, index, true)
    },

    // 上传错误处理 - 题目资源
    handleUploadResourceError() {
      this.uploadStates.resourceImg = false
      this.uploadStates.resourceVideo = false
      this.uploadStates.resourceAudio = false
    },

    // 上传错误处理 - 选项图片
    handleUploadOptionImgError(index) {
      this.$set(this.uploadStates.optionImgs, index, false)
    },

    // 上传资源成功
    onUploadOptionImgSuccess(index, res) {
      this.$set(this.uploadStates.optionImgs, index, false)
      this.$set(this.editObject.options, index, {
        ...this.editObject.options[index],
        img: Hi.String.dealUrl(res.data.url),
      })
    },

    // 上传资源成功
    onUploadResourceImg(res) {
      this.uploadStates.resourceImg = false
      this.$set(this.editObject.subjectContent, 'type', 'IMAGE')
      this.$set(this.editObject.subjectContent, 'resource', Hi.String.dealUrl(res.data.url))
    },
    onUploadResourceVideo(res) {
      this.uploadStates.resourceVideo = false
      this.$set(this.editObject.subjectContent, 'type', 'VIDEO')
      this.$set(this.editObject.subjectContent, 'resource', Hi.String.dealUrl(res.data.url))
    },
    onUploadResourceAudio(res) {
      this.uploadStates.resourceAudio = false
      this.$set(this.editObject.subjectContent, 'type', 'AUDIO')
      this.$set(this.editObject.subjectContent, 'resource', Hi.String.dealUrl(res.data.url))
    },

    // 删除图片
    handleDeleteResource() {
      this.$set(this.editObject.subjectContent, 'type', 'TEXT')
      this.$set(this.editObject.subjectContent, 'resource', '')
    },

    // 添加选项
    handleAddOption() {
      if (this.editObject.options.length < 6) {
        this.$set(this.editObject.options, this.editObject.options.length, {
          id: '',
          title: '',
          rightAnswer: 'N',
        })
      }
    },

    // 删除选项
    handleDeleteOption(index) {
      if (this.editObject.options.length > 2) {
        this.editObject.options.splice(index, 1)
      }
    },

    // 删除选项图片
    handleDeleteOptionImg(index) {
      this.$set(this.editObject.options, index, {
        ...this.editObject.options[index],
        img: '',
      })
    },

    // 难度改变
    handleDifficultyChange(val) {
      const difficulty = this.subjectDifficultyList.find((item) => item.id === val)
      if (difficulty) {
        this.scoreObj.rightScore = difficulty.rightScore
        this.scoreObj.wrongScore = difficulty.wrongScore
        this.scoreObj.answerTime = difficulty.answerTime
      }
    },

    // 类型改变
    handleChangeType(val) {
      if (this.editObject.type === 'RADIO') {
        this.editObject.options.forEach((item) => {
          item.rightAnswer = 'N'
        })
      }
    },

    // 选项答案改变
    handleChangeRightAnswer(index) {
      if (this.editObject.type === 'RADIO') {
        this.editObject.options.forEach((item, i) => {
          if (i !== index) {
            item.rightAnswer = 'N'
          }
        })
      }
    },

    // 验证表单
    validateForm() {
      return new Promise((resolve, reject) => {
        // 基础表单验证
        this.$refs.questionForm.validate((valid) => {
          if (!valid) {
            reject(new Error('请完善表单信息'))
            return
          }

          // 题目内容验证
          if (!this.editObject.subjectContent.content) {
            reject(new Error('请输入题目内容'))
            return
          }

          // 选项验证
          const hasValidOptions = this.editObject.options.every((option) => {
            return (option.title && option.title.trim()) || option.img
          })

          if (!hasValidOptions) {
            reject(new Error('每个选项至少设置一张图片或文字内容'))
            return
          }

          // 正确答案验证
          const hasCorrectAnswer = this.editObject.options.some((option) => option.rightAnswer === 'Y')
          if (!hasCorrectAnswer) {
            reject(new Error('至少要选择一个正确答案'))
            return
          }

          // if (this.model === 'user' && !this.editObject.groupId) {
          //   reject(new Error('请选择题目分组'))
          //   return
          // }
          if (this.model === 'user' && !this.editObject.difficultyId) {
            reject(new Error('请选择题目难度'))
            return
          }

          resolve(true)
        })
      })
    },

    // 保存题目
    async handleSave(type) {
      try {
        await this.validateForm()
        const isContinue = type === 'continue'

        if (!this.editObject.groupId) {
          delete this.editObject.groupId
        }

        this.$emit('success', this.editObject)

        if (!isContinue) {
          this.handleClose()
        } else {
          this.initForm()
        }
      } catch (error) {
        console.error('保存题目失败:', error)
        this.$notify.error({
          title: '错误',
          message: error.message,
        })
      }
    },

    // 打开弹窗
    handleOpen() {
      this.initForm()
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('close')
      this.initForm()
    },
  },
  created() {
    this.initForm()
  },
}
</script>
<style lang="scss" scoped>
.question-content {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  transition: all 0.3s ease;
}

.question-media {
  flex-shrink: 0;
  width: 140px;
}

.question-img-view,
.question-video-view {
  position: relative;
  width: 140px;
  height: 100px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;

  img,
  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.question-audio-view {
  position: relative;
  width: 140px;
  height: 100px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 20px;
  height: 20px;
  background: #f56c6c;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  margin: 0;
  transition: all 0.2s ease;

  &:hover {
    background: #f78989;
  }

  i {
    font-size: 10px;
  }
}

.question-input {
  flex: 1;
  border-radius: 8px;

  ::v-deep .el-textarea__inner {
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;
    font-size: 14px;
    line-height: 1.6;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

.media-upload-buttons {
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  transition: all 0.3s ease;
  margin-left: 80px;
}

.upload-btn {
  margin-right: 0;

  ::v-deep .el-button {
    border-radius: 6px;
    font-size: 13px;
    padding: 8px 16px;
    transition: all 0.3s ease;
  }
}

.upload-hint {
  color: #909399;
  font-size: 12px;
  margin-left: auto;
  font-style: italic;
}

.options-section {
  margin-top: 20px;
  .options-section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    margin-bottom: 10px;

    h4 {
      color: #303133;
      font-size: 18px;
      font-weight: 600;
      position: relative;
      padding-left: 12px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        border-radius: 2px;
        background-color: #409eff;
      }
    }
  }
}
.other-settings {
  margin-top: 10px;
  h4 {
    color: #303133;
    font-size: 18px;
    font-weight: 600;
    position: relative;
    padding-left: 12px;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 20px;
      border-radius: 2px;
      background-color: #409eff;
    }
  }
}

/* 选项表格样式优化 */
.option-header {
  display: flex;
  border-bottom: 1px solid #ebeef5;
  border-top: 1px solid #ebeef5;
  font-weight: 600;

  > div {
    padding: 16px 12px;
    border-right: 1px solid #ebeef5;
    font-size: 14px;

    &:last-child {
      border-right: none;
    }
  }
}

.option-col-index {
  width: 50px;
  text-align: center;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  .option-index-view {
    width: 35px;
    height: 35px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
  }
}

.option-col-img {
  width: 50px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  .option-img-view {
    width: 35px;
    height: 35px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .delete-btn-option {
      position: absolute;
      top: 0;
      right: 0;
      width: 35px;
      height: 35px;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      opacity: 0;
      transition: all 0.3s ease;
      &:hover {
        opacity: 1;
      }
    }
  }
}

.option-col-content {
  flex: 1;
}

.option-col-answer {
  width: 50px;
  text-align: center;
}

.option-col-action {
  width: 50px;
  text-align: center;
}

.option-list {
  border-top: none;
  overflow: hidden;
}

.option-item {
  display: flex;
  border-bottom: 1px solid #f0f2f5;
  align-items: center;
  background: #ffffff;

  &:last-child {
    border-bottom: none;
  }

  > div {
    padding: 14px 12px;

    &:last-child {
      border-right: none;
    }
  }

  .option-col-index {
    font-weight: 600;
    font-size: 16px;
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 24px;
  border-top: 1px solid #f0f2f5;
}
</style>
