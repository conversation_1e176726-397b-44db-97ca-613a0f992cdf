import api from './api'
export default {
  // 粉丝查询接口
  onwalllist: postData => api.fetchBaseData('/pro/hxc/prowxuser/onwalllist.htm', postData),
  // 获取粉丝信息用户模糊查询
  listTwo: postData => api.fetchBaseData('/pro/hxc/prowxuser/listTwo.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prowxuser/list.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/prowxuser/page.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/prowxuser/read.htm', postData),
  searchname: postData => api.fetchBaseData('/pro/hxc/prowxuser/searchname.htm', postData),
  search: postData => api.fetchBaseData('/pro/hxc/prowxuser/search.htm', postData),
}
