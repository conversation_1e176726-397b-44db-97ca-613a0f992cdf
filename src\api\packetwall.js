import api from './api'
export default {
  // 盲盒抽奖、红包墙
  read: (v) => api.fetchBaseData('/pro/hxc/propacketwall/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/propacketwall/update.htm', v),
  add: (v) => api.fetchBaseData('/pro/hxc/propacketwall/add.htm', v),
  page: (v) => api.fetchBaseData('/pro/hxc/propacketwall/page.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/propacketwall/list.htm', v),
  delete: (postData) => api.fetchBaseData('/pro/hxc/propacketwall/delete.htm', postData),
  finish: (postData) => api.fetchBaseData('/pro/hxc/propacketwall/finish.htm', postData),
  copy: (postData) => api.fetchBaseData('/pro/hxc/propacketwall/copy.htm', postData),
}
