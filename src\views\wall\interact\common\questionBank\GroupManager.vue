<template>
  <el-dialog
    :title="`${type === 'difficulty' ? '难度' : '分组'}管理`"
    :visible="visible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
    append-to-body
  >
    <div class="group-manager">
      <div>
        <el-button type="primary" @click="handleAddData">
          {{ dialogTitle }}
        </el-button>
      </div>
      <!-- 分组列表 -->
      <div class="mrg-t-20">
        <el-table :data="tableData" min-height="300px">
          <el-table-column label="名称" prop="name" />
          <el-table-column label="题目数量" prop="subjectCount" />
          <el-table-column v-if="type === 'difficulty'" label="分值" prop="rightScore" />
          <el-table-column label="操作" width="150">
            <template v-slot="{ row }">
              <el-button type="text" @click="handleEditData(row)">编辑</el-button>
              <el-button class="text-red" type="text" @click="handleDeleteData(row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible="showDialog"
      width="500px"
      :close-on-click-modal="false"
      @close="handleEditClose"
      append-to-body
      destroy-on-close
    >
      <el-form :model="editData" :rules="formRules" ref="addForm" label-width="120px">
        <el-form-item label="名称" prop="name">
          <el-input class="w-200" v-model="editData.name" placeholder="请输入名称" :maxlength="20" show-word-limit></el-input>
        </el-form-item>
        <template v-if="type === 'difficulty'">
          <el-form-item label="答对得分" prop="rightScore">
            <el-input-number
              class="w-100"
              v-model="editData.rightScore"
              placeholder="请输入答对得分"
              :max="9999"
              controls-position="right"
            ></el-input-number>
            <span class="text-gray">该难度内题目默认答对得分数</span>
          </el-form-item>
          <el-form-item label="答错扣分" prop="wrongScore">
            <el-input-number
              class="w-100"
              v-model="editData.wrongScore"
              placeholder="请输入答错得分"
              :max="9999"
              controls-position="right"
            ></el-input-number>
            <span class="text-gray">该难度内题目默认答错扣分数</span>
          </el-form-item>
          <el-form-item label="答题时间" prop="answerTime">
            <el-input-number
              class="w-100"
              v-model="editData.answerTime"
              placeholder="请输入答题时间"
              :max="9999"
              controls-position="right"
            ></el-input-number>
            <span>秒</span>
            <span class="text-gray">该难度内题目默认答题时间</span>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="saveData" :loading="saving"> 确定 </el-button>
      </div>
    </el-dialog>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { Hi } from '@/libs/common'
import { mapState, mapActions, mapGetters } from 'vuex'

const defaultData = {
  name: '',
  rightScore: 10, // 答对得分
  wrongScore: 0, // 答错得分
  answerTime: 10, // 答题时间
}

export default {
  name: 'GroupManager',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'difficulty',
      validator: (value) => ['difficulty', 'group'].includes(value),
    },
  },
  data() {
    return {
      showDialog: false,
      editData: Hi.Object.copy(defaultData),
    }
  },
  computed: {
    ...mapState('questionBank', ['loading', 'saving', 'subjectDifficultyList', 'subjectGroupList']),
    ...mapGetters({
      wallFlagObj: 'wall/getWallFlagObj',
    }),
    wall() {
      return this.wallFlagObj[this.$route.query.wallFlag]
    },
    tableData() {
      return this.type === 'difficulty' ? this.subjectDifficultyList : this.subjectGroupList
    },
    // 对话框标题
    dialogTitle() {
      return this.type === 'difficulty' ? '添加难度' : '添加分组'
    },
    // 表单验证规则
    formRules() {
      const baseRules = {
        name: [
          { required: true, message: `请输入${this.type === 'difficulty' ? '难度' : '分组'}名称`, trigger: 'blur' },
          { min: 1, max: 20, message: '名称长度在 1 到 20 个字符', trigger: 'blur' },
        ],
      }

      if (this.type === 'difficulty') {
        return {
          ...baseRules,
          rightScore: [{ required: true, message: '请输入答对得分', trigger: 'blur' }],
          wrongScore: [{ required: true, message: '请输入答错得分', trigger: 'blur' }],
          answerTime: [{ required: true, message: '请输入答题时间', trigger: 'blur' }],
        }
      }

      return baseRules
    },
  },
  methods: {
    ...mapActions('questionBank', ['saveSubjectDifficulty', 'deleteSubjectDifficulty', 'saveSubjectGroup', 'deleteSubjectGroup']),

    // 添加数据
    handleAddData() {
      this.editData = Hi.Object.copy(defaultData)
      this.showDialog = true
    },

    // 编辑数据
    handleEditData(row) {
      this.editData = Hi.Object.copy(row)
      this.showDialog = true
    },

    // 删除数据
    async handleDeleteData(id) {
      try {
        const itemType = this.type === 'difficulty' ? '难度' : '分组'
        await this.$confirm(`确定删除该${itemType}吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        const deleteAction = this.type === 'difficulty' ? this.deleteSubjectDifficulty : this.deleteSubjectGroup

        await deleteAction(id)
      } catch (error) {
        // 用户取消删除，不需要处理
      }
    },

    // 保存数据
    async saveData() {
      try {
        await this.$refs.addForm.validate()

        const saveAction = this.type === 'difficulty' ? this.saveSubjectDifficulty : this.saveSubjectGroup

        await saveAction(this.editData)
        this.showDialog = false
      } catch (error) {
        // 表单验证失败或保存失败，不关闭对话框
        console.error('保存失败:', error)
      }
    },

    handleEditClose() {
      this.showDialog = false
      this.$refs.addForm.resetFields()
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('close')
    },
  },
}
</script>

<style scoped>
.group-manager {
  padding: 20px;
}

.text-gray {
  color: #999;
  font-size: 12px;
  margin-left: 10px;
}

.mrg-t-20 {
  margin-top: 20px;
}
</style>
