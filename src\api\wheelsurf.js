import api from './api'
export default {
  // 游戏抽奖
  read: (v) => api.fetchBaseData('/pro/hxc/prowheelsurf/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/prowheelsurf/update.htm', v),
  add: (v) => api.fetchBaseData('/pro/hxc/prowheelsurf/add.htm', v),
  page: (v) => api.fetchBaseData('/pro/hxc/prowheelsurf/page.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/prowheelsurf/list.htm', v),
  delete: (postData) => api.fetchBaseData('/pro/hxc/prowheelsurf/delete.htm', postData),
  finish: (postData) => api.fetchBaseData('/pro/hxc/prowheelsurf/finish.htm', postData),
  listcount: (postData) => api.fetchBaseData('/pro/hxc/prowheelsurf/listcount.htm', postData),
}
