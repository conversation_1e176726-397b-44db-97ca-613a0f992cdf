import api from './api';
export default {
  add: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryimport/add.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryimport/update.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryimport/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryimport/list.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryimport/delete.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryimport/batch.htm', postData),
  cleardata: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryimport/cleardata.htm', postData),
};
