import api from './api'
export default {
  add: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3/add.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3/read.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3/page.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3/update.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3/list.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3/delete.htm', postData),
  reset: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3/reset.htm', postData),
  copy: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3/copy.htm', postData),
  sort: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3/sort.htm', postData),
}
