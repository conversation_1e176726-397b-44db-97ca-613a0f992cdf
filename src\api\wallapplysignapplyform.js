import api from './api'
export default {
  list: postData => api.fetchBaseData('/pro/hxc/prowallapplysignapplyform/list.htm', postData),
  insert: postData => api.fetchBaseData('/pro/hxc/prowallapplysignapplyform/insert.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prowallapplysignapplyform/update.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/prowallapplysignapplyform/delete.htm', postData),
}
