<template>
  <div class="listlottery-box" v-loading="loading" element-loading-text="拼命加载中">
    <template v-if="isResultControl">
      <hi-result-control
        :config="config"
        :wall="wall"
        @toList=";(loading = true), (isResultControl = false)"
        @updateConfig="readConfig()"
      ></hi-result-control>
    </template>
    <template v-else-if="isRoster">
      <hi-listlottery-manage
        :config="config"
        @toList=";(loading = true), (isRoster = false)"
        @fetchwall="getWall"
        @loaded="loaded()"
      ></hi-listlottery-manage>
    </template>
    <template v-else>
      <template v-if="!isEdit">
        <hi-listlottery-list
          :config="config"
          @loaded="loaded()"
          @edit="(v) => setEdit(v)"
          @add="addAct"
          @refreshConfig="readConfig()"
          @to-managelist="isRoster = true"
          @toResult="isResultControl = true"
        ></hi-listlottery-list>
      </template>
      <template v-else>
        <hi-listlottery-edit
          @loaded="loaded()"
          :listlotteryType="listlotteryType"
          :themeType="editObj.themeType"
          :editId="editObj.id"
          @list="
            (v) => {
              setList(), readConfig()
            }
          "
          @update="readConfig()"
          @toRoster="isRoster = true"
        ></hi-listlottery-edit>
      </template>
    </template>
    <!-- 升级，发布 -->
    <template v-if="isPublish">
      <hi-wall-publish v-if="wall.wallVersion === 'test'" :wallFlag="wall.wallFlag" @close="setIsPublish(false)" @pay="payfn"></hi-wall-publish>
      <hi-wall-upgrade v-else :wallFlag="wall.wallFlag" @close="setIsPublish(false)" @pay="payfn"></hi-wall-upgrade>
    </template>
  </div>
</template>
<script>
import { wallSetMixin } from '@/libs/mixins'
import HiListlotteryList from './list.vue'
import HiListlotteryEdit from './edit/index.vue'
import HiListlotteryManage from './listmanage/index.vue'
import HiResultControl from './resultControl.vue'
import { mapState, mapActions, mapMutations } from 'vuex'
import api from '@/api'
export default {
  name: 'WallDiglett',
  mixins: [wallSetMixin],
  components: {
    HiListlotteryList,
    HiListlotteryEdit,
    HiListlotteryManage,
    HiResultControl,
  },
  data() {
    return {
      isRoster: false,
      isResultControl: false,
      listlotteryType: 'LIST_LOTTERY',
    }
  },
  computed: {
    ...mapState('ListlotteryEdit', ['config', 'isPublish']),
    ...mapState('wall', ['wall']),
  },
  methods: {
    ...mapActions({ fetchWall: 'wall/fetchWall' }),
    ...mapActions('ListlotteryEdit', ['fetchConfig']),
    ...mapMutations('ListlotteryEdit', ['setIsPublish']),
    addAct() {
      this.editObj = {}
      this.loading = true
    },
    async readConfig() {
      try {
        await this.fetchConfig()
      } catch (err) {
        console.log(err)
        this.$notify.error({ title: '错误', message: err.msg })
      }
    },
    async payfn(pay) {
      try {
        await this.$hi.pay(pay)
        await this.fetchWall({
          wallFlag: this.wall.wallFlag,
          force: true,
        })
      } catch (err) {
        console.error(err)
      }
    },
    async getWall() {
      await this.fetchWall({ wallFlag: this.wall.wallFlag, force: true })
      this.wall = this.wallFlagObj[this.wall.wallFlag]
    },
  },
  created() {
    this.isRoster = this.$route.query.open === 'roster'
  },
  async mounted() {
    await this.readConfig()
  },
}
</script>
<style scoped lang="stylus">
.listlottery-box
  min-width 1100px
</style>
