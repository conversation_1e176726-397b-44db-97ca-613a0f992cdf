import api from './api'
export default {
  list: postData => api.fetchBaseData('/pro/hxc/propiclotteryrecord/list.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/propiclotteryrecord/page.htm', postData),
  clearData: postData => api.fetchBaseData('/pro/hxc/propiclotteryrecord/clearData.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/propiclotteryrecord/update.htm', postData),
}
