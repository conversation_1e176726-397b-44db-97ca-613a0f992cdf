<template>
  <div class="iframe-container" v-loading="isLoading" :element-loading-text="loadingText">
    <!-- iframe 容器 -->
    <div v-show="currentState === 'success'" class="migration-iframe-wrapper">
      <iframe
        ref="migrationIframe"
        :src="url"
        frameborder="0"
        @error="handleIframeError"
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-top-navigation"
      ></iframe>
    </div>

    <!-- 错误状态 -->
    <div v-if="currentState === 'error'" class="migration-error migration-state-error">
      <div class="error-content">
        <i class="el-icon-warning"></i>
        <h3>加载失败</h3>
        <p>{{ errorMessage }}</p>
        <div class="error-details" v-if="errorDetails">
          <el-collapse>
            <el-collapse-item title="错误详情" name="1">
              <pre>{{ errorDetails }}</pre>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div class="error-actions">
          <el-button @click="initMigration"> 重试 </el-button>
          <!-- <el-button type="primary" @click="fallbackToOriginal">使用原版本</el-button> -->
        </div>
      </div>
    </div>

    <slot v-if="currentState === 'fallback'"></slot>
  </div>
</template>

<script>
import { env } from '@/libs/common'

export default {
  name: 'HiIframeContainer',
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    loadTimeout: {
      type: Number,
      default: 15000, // 10秒超时
    },
    iframeUrl: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      currentState: 'unknown', // unknown, loading, success, error, fallback
      errorMessage: '',
      errorDetails: '',
      messageListener: null,
      loadingText: '正在初始化...',
      retryCount: 0,
      loadTimer: null,
      url: '',
    }
  },
  computed: {
    _moduleName() {
      return this.$route.name.split('-')[2]
    },
    isLoading() {
      return this.currentState === 'loading'
    },
  },
  methods: {
    // 获取新系统URL
    getNewSystemUrl() {
      if (this.iframeUrl) {
        return this.iframeUrl
      }

      const moduleName = this._moduleName

      const domain = env.env === 'dev' ? env.proxy + '/' : env.wwwdomain

      // 构建基础URL
      const baseUrl = `${domain}/next/admin/wall/interact/${moduleName}`

      // 默认参数设置
      const params = {
        hideHeader: 'Y',
        hideAside: 'Y',
        ...this.$route.query, // 直接合并路由查询参数
      }

      return `${baseUrl}?${new URLSearchParams(params).toString()}`
    },

    // 初始化迁移
    initMigration() {
      this.resetState()
      this.startMigrationProcess()
    },

    // 重置状态
    resetState() {
      this.currentState = 'unknown'
      this.errorMessage = ''
      this.errorDetails = ''
      this.loadingText = '正在初始化...'
      this.clearTimers()
    },

    // 清理定时器
    clearTimers() {
      if (this.loadTimer) {
        clearTimeout(this.loadTimer)
        this.loadTimer = null
      }
    },

    // 开始迁移流程
    startMigrationProcess() {
      this.currentState = 'loading'
      this.errorMessage = ''
      this.errorDetails = ''

      // 构建新系统URL
      this.url = this.getNewSystemUrl()

      // 设置消息监听
      this.setupMessageListener()

      // 设置加载超时
      this.setLoadTimeout()
    },

    // 设置加载超时
    setLoadTimeout() {
      this.loadTimer = setTimeout(() => {
        if (this.currentState === 'loading') {
          this.handleIframeError('加载超时，请检查网络连接')
        }
      }, this.loadTimeout)
    },

    // 监听iframe消息
    setupMessageListener() {
      this.messageListener = (event) => {
        const { type, data } = event.data || {}

        if (!type) return

        switch (type) {
          case 'iframe:ready':
            this.handleIframeReady(data)
            break
          case 'iframe:error':
            this.handleIframeError(data)
            break
          default:
            this.$emit('iframe:message', event.data)
        }
      }

      window.addEventListener('message', this.messageListener)
    },

    // 处理iframe准备就绪
    handleIframeReady(data) {
      this.clearTimers()
      this.loadingText = '加载完成'
      this.currentState = 'success'
      this.$emit('iframe:ready', data)
    },

    // 处理iframe错误
    handleIframeError(error) {
      this.clearTimers()
      this.currentState = 'error'

      if (typeof error === 'string') {
        this.errorMessage = error
      } else if (error && error.message) {
        this.errorMessage = error.message
        this.errorDetails = error.stack || JSON.stringify(error, null, 2)
      } else {
        this.errorMessage = '新版本加载失败，请稍后重试'
        this.errorDetails = JSON.stringify(error, null, 2)
      }

      this.$emit('iframe:error', error)
    },

    // 降级到原版本
    fallbackToOriginal() {
      this.currentState = 'fallback'
      this.cleanup()
      this.$emit('fallback')
    },

    // 发送消息到iframe
    sendMessageToIframe(message) {
      if (!this.$refs.migrationIframe || !this.$refs.migrationIframe.contentWindow) {
        console.warn('iframe未准备就绪，无法发送消息')
        return false
      }

      try {
        this.$refs.migrationIframe.contentWindow.postMessage(message, '*')
        return true
      } catch (error) {
        console.error('发送消息到iframe失败:', error)
        return false
      }
    },

    // 清理资源
    cleanup() {
      this.clearTimers()
      if (this.messageListener) {
        window.removeEventListener('message', this.messageListener)
        this.messageListener = null
      }
    },
  },
  mounted() {
    this.initMigration()
  },
  beforeDestroy() {
    this.cleanup()
  },
}
</script>

<style scoped lang="scss">
.iframe-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

// 错误状态样式
.migration-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;

  .error-content {
    text-align: center;
    padding: 40px;

    .el-icon-warning {
      font-size: 48px;
      color: #f56c6c;
      margin-bottom: 20px;
    }

    h3 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 18px;
    }

    p {
      margin: 10px 0 30px 0;
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
    }

    .error-actions {
      .el-button {
        margin: 0 5px;
      }
    }
  }
}

// iframe 包装器样式
.migration-iframe-wrapper {
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;

  iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
    display: block; // 关键：将iframe设置为block元素，消除基线对齐问题
  }
}
</style>
