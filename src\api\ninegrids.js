import api from './api'
export default {
  // 游戏抽奖
  read: (v) => api.fetchBaseData('/pro/hxc/proninegrids/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/proninegrids/update.htm', v),
  add: (v) => api.fetchBaseData('/pro/hxc/proninegrids/add.htm', v),
  page: (v) => api.fetchBaseData('/pro/hxc/proninegrids/page.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/proninegrids/list.htm', v),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proninegrids/delete.htm', postData),
  finish: (postData) => api.fetchBaseData('/pro/hxc/proninegrids/finish.htm', postData),
  listcount: (postData) => api.fetchBaseData('/pro/hxc/proninegrids/listcount.htm', postData),
}
