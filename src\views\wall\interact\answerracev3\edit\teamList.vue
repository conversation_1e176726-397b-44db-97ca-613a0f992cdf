<template>
  <div>
    <div class="team-list-btn">
      <el-button type="primary" @click="handleAddTeam" :disabled="disabled">添加团队</el-button>
    </div>
    <el-table :data="teamList" border>
      <el-table-column label="队伍头像" prop="teamHeadImg">
        <template v-slot="{ row }">
          <img class="avatar" :src="row.teamHeadImg || defaultTeamPicture" />
        </template>
      </el-table-column>
      <el-table-column label="队伍名称" prop="teamName">
        <template v-slot="{ row }">
          <span>{{ row.teamName || '未命名队伍' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template v-slot="{ row, $index }">
          <el-button type="text" @click="handleQrcode(row)">二维码</el-button>
          <el-button type="text" @click="handleEditTeam(row, $index)" :disabled="disabled">编辑</el-button>
          <el-button class="text-red" type="text" @click="handleDeleteTeam(row, $index)" :disabled="disabled">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑团队弹窗 -->
    <el-dialog :title="editingTeam.id ? '编辑团队' : '添加团队'" :visible.sync="teamDialog" :close-on-click-modal="false" width="500px">
      <el-form :model="editingTeam" :rules="teamRules" ref="teamForm" label-width="100px">
        <el-form-item label="队伍名称" prop="teamName">
          <el-input v-model.trim="editingTeam.teamName" placeholder="请输入队伍名称" :maxlength="20" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="队伍头像" prop="teamHeadImg">
          <div class="flex">
            <hi-upload-img
              :action="$hi.url.upload"
              :show-file-list="false"
              :on-success="uploadSuccess"
              :on-progress="() => (uploadLoading = true)"
              :on-error="() => (uploadLoading = false)"
            >
              <el-button type="primary">上传替换</el-button>
            </hi-upload-img>
            <el-button style="margin-left: 15px" @click="editingTeam.teamHeadImg = defaultTeamPicture" plain> 恢复默认 </el-button>
          </div>
          <div class="flex flex-a-fe mrg-t-10">
            <div class="pic-box" v-loading="uploadLoading">
              <img class="w-full h-full" v-hiimg="editingTeam.teamHeadImg" alt="" src="" />
            </div>
            <div class="mrg-l-10">
              <hi-size-specification
                placement="right-start"
                :note="{
                  推荐尺寸: '128*128(1:1)',
                  大小: '小于2M',
                  格式: 'jpg/png/bmp',
                }"
              >
              </hi-size-specification>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="teamDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSaveTeam">确定</el-button>
      </span>
    </el-dialog>

    <!-- 二维码弹窗 -->
    <el-dialog title="团队参与二维码" :visible.sync="qrcodeDialog" width="400px" center>
      <div class="qrcode-container">
        <img :src="getQrcode(currentTeam.id) | qrcode" alt="" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api'
import { env, Hi } from '@/libs/common'

export default {
  name: 'HiTeamList',
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    wall: {
      type: Object,
      required: true,
    },
    actConfig: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      teamDialog: false,
      qrcodeDialog: false,
      uploadLoading: false,
      editingTeam: {},
      editingIndex: null,
      currentTeam: {},
      defaultTeamPicture: 'https://res3.hixianchang.com/qn/up/9a55f123dfed9771a26665adc4850c4d.png',
      teamRules: {
        teamName: [
          { required: true, message: '请输入队伍名称', trigger: 'blur' },
          { min: 1, max: 20, message: '队伍名称长度为1-20个字符', trigger: 'blur' },
        ],
      },
    }
  },
  computed: {
    teamList: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('update:value', val)
      },
    },
  },
  watch: {
    'actConfig.id': {
      async handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          await this.saveTeamList()
        }
      },
    },
  },
  methods: {
    async fetchTeamList() {
      if (!this.actConfig.id) return
      const res = await api.answerracev3team.list({ where: { answerracev3Id: this.actConfig.id } })
      this.teamList.splice(0, this.teamList.length, ...res)
    },
    handleAddTeam() {
      this.editingTeam = {
        teamName: '',
        teamHeadImg: this.defaultTeamPicture,
        wallId: this.wall.id,
        answerracev3Id: this.actConfig.id,
      }
      this.editingIndex = null
      this.teamDialog = true
    },

    handleEditTeam(row, index) {
      this.editingTeam = { ...row }
      this.editingIndex = index
      this.teamDialog = true
    },

    async handleDeleteTeam(row, index) {
      try {
        await this.$confirm('确认删除该团队吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        if (row.id) {
          await api.answerracev3team.delete({ where: { wallId: this.wall.id, id: row.id } })
          await this.fetchTeamList()
          this.$notify.success({ title: '成功', message: '删除成功' })
        } else {
          this.teamList.splice(index, 1)
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$notify.error({
            title: '错误',
            message: error?.msg || '删除失败，请稍后重试',
          })
        }
      }
    },

    async handleSaveTeam() {
      await this.$refs.teamForm.validate()

      try {
        const teamData = {
          ...this.editingTeam,
          wallId: this.wall.id,
          answerracev3Id: this.actConfig.id,
        }

        const _add = async () => {
          if (this.actConfig.id) {
            await api.answerracev3team.add(teamData)
            await this.fetchTeamList()
          } else {
            this.teamList.push(teamData)
          }
        }

        const _update = async () => {
          if (teamData.id) {
            await api.answerracev3team.update({
              where: { id: teamData.id, answerracev3Id: this.actConfig.id, wallId: this.wall.id },
              update: {
                teamName: teamData.teamName,
                teamHeadImg: teamData.teamHeadImg,
              },
            })
            await this.fetchTeamList()
          } else {
            this.teamList.splice(this.editingIndex, 1, teamData)
          }
        }

        this.editingIndex === null ? _add() : _update()
        this.teamDialog = false
        this.$notify.success({
          title: '成功',
          message: this.editingIndex !== null ? '编辑成功' : '添加成功',
        })
      } catch (error) {
        this.$notify.error({
          title: '错误',
          message: error?.msg || '保存失败，请稍后重试',
        })
      } finally {
      }
    },

    handleQrcode(row) {
      this.currentTeam = { ...row }
      this.qrcodeDialog = true
    },

    getQrcode(id) {
      let domain = env.wwwdomain
      if (env.env === 'dev') {
        domain = 'http://www.dev.hixianchang.com/'
      }
      return `${domain}p/m/${this.wall.mobileFlag}.html?/#/common/transfer-route.html?mobileFlag=${this.wall.mobileFlag}&route=wall-answerracev3&teamId=${id}`
    },

    uploadSuccess(res) {
      this.editingTeam.teamHeadImg = Hi.String.dealUrl(res.data.url)
      this.uploadLoading = false
    },

    async saveTeamList() {
      try {
        for (const team of this.teamList) {
          await api.answerracev3team.add({
            ...team,
            wallId: this.wall.id,
            answerracev3Id: this.actConfig.id,
          })
        }
        await this.fetchTeamList()
        this.$notify.success({
          title: '成功',
          message: '团队列表保存成功',
        })
      } catch (error) {
        this.$notify.error({
          title: '错误',
          message: error?.msg || '保存失败，请稍后重试',
        })
      }
    },
  },
  mounted() {
    this.fetchTeamList()
  },
}
</script>

<style scoped lang="scss">
.team-list-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.pic-box {
  width: 80px;
  height: 80px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.flex-a-fe {
  align-items: flex-end;
}

.mrg-t-10 {
  margin-top: 10px;
}

.mrg-l-10 {
  margin-left: 10px;
}

.qrcode-container {
  text-align: center;
  padding: 20px;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.qrcode-container .team-info {
  margin-bottom: 20px;
}

.qrcode-container .team-info .team-avatar {
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
  border-radius: 50%;
  object-fit: cover;
}

.qrcode-container .team-info h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.qrcode-placeholder {
  padding: 40px 20px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  background-color: #fafafa;
}

.qrcode-placeholder p {
  margin: 10px 0;
  color: #666;
}

.qrcode-placeholder .hint {
  font-size: 12px;
  color: #999;
}
</style>
