import api from './api'
export default {
  update: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3team/update.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3team/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3team/add.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3team/delete.htm', postData),
}
