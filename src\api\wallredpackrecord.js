import api from './api'
export default {
  page: postData => api.fetchBaseData('/pro/hxc/prowallredpackrecord/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prowallredpackrecord/list.htm', postData),
  sumMoney: postData => api.fetchBaseData('/pro/hxc/prowallredpackrecord/sumMoney.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prowallredpackrecord/update.htm', postData),
}
