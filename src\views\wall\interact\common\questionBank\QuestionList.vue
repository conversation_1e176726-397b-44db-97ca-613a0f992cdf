<template>
  <div class="question-list">
    <div v-if="mode === 'manage'">
      <el-button type="primary" @click="handleAddQuestion">添加题目</el-button>
      <el-button type="primary" @click="handleImportQuestion">导入题目</el-button>
      <el-button :disabled="selectedQuestions.length === 0" type="primary" plain @click="handleSubjectSet('group')">设置分组</el-button>
      <el-button :disabled="selectedQuestions.length === 0" type="primary" plain @click="handleSubjectSet('difficulty')">设置难度</el-button>
      <el-button :disabled="selectedQuestions.length === 0" type="danger" @click="handleBatchDelete">批量删除</el-button>
    </div>
    <div class="question-table">
      <el-table
        ref="questionTableRef"
        :data="questionList"
        v-loading="loading"
        @selection-change="onSelectionChange"
        max-height="450px"
        empty-text="暂无题目数据"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" width="80" type="index" />
        <el-table-column label="分组">
          <template v-slot="{ row }">
            <span>{{ getGroupName(row.groupId) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="题目" min-width="200">
          <template v-slot="{ row }">
            <div class="question-content">
              <el-popover v-if="row.subjectContent.resource" placement="top-start" trigger="hover">
                <div class="title-img-big">
                  <img v-lazy="row.subjectContent.resource" v-if="row.subjectContent.type === 'IMAGE'" alt="题目图片" />
                  <video v-if="row.subjectContent.type === 'VIDEO'" :src="row.subjectContent.resource" alt="题目视频" controls />
                  <hi-audio-player v-if="row.subjectContent.type === 'AUDIO'" :src="row.subjectContent.resource" alt="题目音频" />
                </div>
                <div slot="reference" class="title-img">
                  <img v-lazy="row.subjectContent.resource" v-if="row.subjectContent.type === 'IMAGE'" alt="题目图片" />
                  <video v-if="row.subjectContent.type === 'VIDEO'" :src="row.subjectContent.resource" />
                  <div v-if="row.subjectContent.type === 'AUDIO'" class="audio-icon">
                    <img src="@/assets/wall/interact/teamanswer/sound.png" alt="" />
                  </div>
                </div>
              </el-popover>
              <el-popover v-else placement="top-start" trigger="hover">
                <div class="popover-content">{{ row.subjectContent.content }}</div>
                <div slot="reference" class="title-text">{{ row.subjectContent.content }}</div>
              </el-popover>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="题目类型">
          <template v-slot="{ row }">
            <span>{{ row.type === 'RADIO' ? '单选题' : '多选题' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="答案">
          <template v-slot="{ row }">
            <el-popover placement="top-start" width="300" trigger="hover">
              <div class="option-box flex flex-w-w">
                <div class="option-txt-pic" v-for="(item, i) in row.options" :key="i">
                  <p class="opt-index">{{ String.fromCharCode(65 + i) }}：</p>
                  <div>
                    <p v-if="item.title">{{ item.title }}</p>
                    <p v-if="item.img" class="pic"><img :src="item.img" @click="previewImage(item.img)" /></p>
                  </div>
                </div>
              </div>
              <span class="correct-ans" slot="reference">{{ getCorrectAnswers(row.options) }}</span>
            </el-popover>
          </template>
        </el-table-column>

        <el-table-column label="注释" width="200">
          <template v-slot="{ row }">
            <el-tooltip placement="top">
              <div slot="content" class="tooltip-content">{{ row.remark || '-' }}</div>
              <div class="remark-text">{{ row.remark || '-' }}</div>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="难度">
          <template v-slot="{ row }">
            <span>{{ getDifficultyData(row.difficultyId).name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="答题时间">
          <template v-slot="{ row }">
            <div class="time-score">
              <span>{{ getDifficultyData(row.difficultyId).answerTime }}秒</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="分值">
          <template v-slot="{ row }">
            <span>{{ getDifficultyData(row.difficultyId).rightScore }}分</span>
          </template>
        </el-table-column>

        <el-table-column v-if="mode === 'manage'" label="操作" width="200" fixed="right">
          <template v-slot="{ row }">
            <el-button type="text" @click="handleEditQuestion(row)">编辑</el-button>
            <el-button class="text-red" type="text" @click="handleDeleteQuestion(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 题目表单弹窗 -->
    <question-form
      :visible="showQuestionForm"
      model="user"
      :question="currentQuestion"
      @close="showQuestionForm = false"
      @success="onQuestionSaved"
    />

    <!-- 导入题目弹窗 -->
    <question-export :visible="showQuestionExport" @close="showQuestionExport = false" />

    <!-- 导入题目弹窗 -->
    <el-dialog
      :title="dialogType === 'group' ? '设置分组' : '设置难度'"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      @close="awaitDialog.j()"
    >
      <div class="group-list">
        <el-table ref="groupTable" :data="dialogData" style="width: 100%">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column label="名称" prop="name" />
          <el-table-column align="center" label="" width="100">
            <template v-slot="{ row }">
              <el-radio v-model="selectedId" :label="row.id" @change="handleGroupSelection(row)">&nbsp;</el-radio>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="awaitDialog.j && awaitDialog.j()">取 消</el-button>
        <el-button type="primary" @click="awaitDialog.r && awaitDialog.r()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api'
import { previewImage } from '@/libs/common'
import { mapActions, mapGetters, mapState } from 'vuex'
import QuestionExport from './QuestionExport.vue'
import QuestionForm from './QuestionForm.vue'

export default {
  name: 'QuestionList',
  components: {
    QuestionForm,
    QuestionExport,
  },
  props: {
    filterConditions: {
      type: Object,
      default: () => ({}),
    },
    mode: {
      type: String,
      default: 'manage', // manage: 管理模式, select: 选择模式
      validator: (value) => ['manage', 'select'].includes(value),
    },
  },
  data() {
    return {
      showQuestionExport: false, // 导入题目弹窗
      selectedQuestions: [], // 选中的题目
      currentQuestion: {}, // 当前题目
      showQuestionForm: false, // 题目表单
      dialogVisible: false, // 分组对话框
      awaitDialog: null, // 分组对话框
      selectedId: null, // 选中的分组ID
      dialogType: 'group', // 对话框类型
    }
  },
  computed: {
    ...mapState('questionBank', ['loading', 'deleting', 'questionList', 'subjectDifficultyList', 'subjectGroupList']),
    ...mapGetters({
      wallFlagObj: 'wall/getWallFlagObj',
    }),
    wall() {
      return this.wallFlagObj[this.$route.query.wallFlag]
    },
    // 分组映射
    groupMap() {
      const map = {}
      this.subjectGroupList.forEach((group) => {
        map[group.id] = group.name
      })
      return map
    },
    // 难度映射
    difficultyMap() {
      const map = {}
      this.subjectDifficultyList.forEach((difficulty) => {
        map[difficulty.id] = difficulty
      })
      return map
    },
    // 对话框数据
    dialogData() {
      return this.dialogType === 'group' ? this.subjectGroupList : this.subjectDifficultyList
    },
  },
  watch: {
    filterConditions: {
      handler(v) {
        this.fetchQuestions(v)
      },
      deep: true,
    },
  },
  methods: {
    previewImage,
    ...mapActions('questionBank', ['initQuestionBank', 'saveQuestion', 'deleteQuestion', 'deleteQuestions', 'fetchQuestions']),
    // 获取分组名称
    getGroupName(groupId) {
      return this.groupMap[groupId] || '-'
    },

    // 获取难度名称
    getDifficultyData(difficultyId) {
      return this.difficultyMap[difficultyId] || { name: '-', rightScore: '-', answerTime: '-' }
    },

    // 获取正确答案
    getCorrectAnswers(options) {
      if (!options || !Array.isArray(options)) {
        return '-'
      }

      const correctAnswers = []
      options.forEach((option, index) => {
        if (option.rightAnswer === 'Y') {
          correctAnswers.push(String.fromCharCode(65 + index))
        }
      })

      return correctAnswers.length > 0 ? correctAnswers.join('/') : '-'
    },

    // 题目保存成功回调
    async onQuestionSaved(question) {
      try {
        await this.saveQuestion(question)
        await this.fetchQuestions(this.filterConditions)
      } catch (error) {
        console.error('保存题目失败:', error)
      }
    },

    // 选择变化
    onSelectionChange(selection) {
      this.selectedQuestions = selection || []
    },

    // 删除题目
    async handleDeleteQuestion(question) {
      try {
        await this.$confirm('确定要删除题目吗？', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        await this.deleteQuestions([question.id])
      } catch (error) {
        // 用户取消删除，不需要处理
      }
    },

    handleImportQuestion() {
      console.log('导入题目')
      this.showQuestionExport = true
    },
    // 批量删除
    async handleBatchDelete() {
      if (this.selectedQuestions.length === 0) {
        this.$notify.warning({
          title: '提示',
          message: '请选择要删除的题目',
        })
        return
      }

      try {
        await this.$confirm(`确定要删除选中的 ${this.selectedQuestions.length} 个题目吗？`, '确认批量删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        const ids = this.selectedQuestions.map((q) => q.id)
        await this.deleteQuestions(ids)
        this.selectedQuestions = []
        this.$refs.questionTableRef.clearSelection()
      } catch (error) {
        // 用户取消删除，不需要处理
      }
    },

    // 处理分组选择
    handleGroupSelection(currentRow) {
      this.selectedId = currentRow.id
    },

    // 设置分组
    async handleSubjectSet(type) {
      if (this.selectedQuestions.length === 0) {
        this.$notify.warning({
          title: '提示',
          message: '请选择要设置的题目',
        })
        return
      }

      // 重置选中的分组
      this.selectedId = null
      this.dialogVisible = true
      this.dialogType = type

      try {
        await new Promise((resolve, reject) => {
          this.awaitDialog = { r: resolve, j: reject }
        })

        // 确定按钮被点击，处理分组设置
        if (!this.selectedId) {
          this.$notify.warning({
            title: '提示',
            message: `请选择一个${type === 'group' ? '分组' : '难度'}`,
          })
          return
        }

        const update = type === 'group' ? { groupId: this.selectedId } : { difficultyId: this.selectedId }

        await api.subject.update({
          where: {
            idList: this.selectedQuestions.map((q) => q.id),
          },
          update,
        })

        const targetName = type === 'group' ? this.getGroupName(this.selectedId) : this.getDifficultyData(this.selectedId).name

        this.$notify.success({
          title: '成功',
          message: `已将 ${this.selectedQuestions.length} 个题目设置为${type === 'group' ? '分组' : '难度'}：${targetName}`,
        })

        await this.initQuestionBank()
        this.selectedQuestions = []
        this.$refs.questionTableRef.clearSelection()
      } catch (error) {
        // 用户取消或出错，不需要处理
      } finally {
        this.dialogVisible = false
      }
    },

    // 编辑题目
    handleEditQuestion(question) {
      this.currentQuestion = { ...question }
      this.showQuestionForm = true
    },

    handleAddQuestion() {
      this.currentQuestion = {}
      this.showQuestionForm = true
    },
  },
}
</script>

<style scoped lang="scss">
.question-table {
  margin-top: 10px;
}

.question-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-img {
  width: 40px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  margin: 5px;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .audio-icon {
    width: 30px;
    margin-top: 5px;
  }
}

.title-img-big {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  img,
  video,
  audio {
    max-width: 100%;
    max-height: 150px;
    border-radius: 4px;
    object-fit: contain;
  }

  audio {
    width: 100%;
    height: 40px;
  }
}
.title-text {
  flex: 1;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

.time-score {
  text-align: center;
}

.correct-ans {
  color: #409eff;
  font-weight: bold;
  cursor: pointer;
}

.option-box {
  width: 100%;
  max-height: 300px;
  overflow: auto;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

.option-txt-pic {
  width: 110px;
  margin: 10px 15px;
  display: flex;
}

.option-txt-pic .pic {
  width: 80px;
  height: 80px;
}

.option-txt-pic .pic img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
}

.opt-index {
  width: 20px;
  text-align: center;
  font-weight: 600;
}

.remark-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100px;
}
.tooltip-content {
  max-width: 400px;
  word-break: break-all;
}
.popover-content {
  max-width: 400px;
  word-break: break-all;
}
</style>
