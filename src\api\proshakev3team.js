import api from './api'
export default {
  read: (postData) => api.fetchBaseData('/pro/hxc/proshakev3team/read.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proshakev3team/update.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proshakev3team/list.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/proshakev3team/batch.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proshakev3team/add.htm', postData),
}
