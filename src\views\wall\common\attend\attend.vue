<template>
    <div class="box">
      <div class="flex top" v-if="option.canSetType">
        <p>当前参与方式：</p>
        <p class="blond">&nbsp;&nbsp;{{ contentTypeName }}</p>
      </div>
      <div v-if="isMerchants">
        <h3>{{ merchantPartnerMsg }}</h3>
      </div>
      <template v-else>
        <el-tabs v-model="actTab" type="card">
          <el-tab-pane v-for="item in attendList" :key="item.id" :name="item.id">
            <div slot="label" class="atten-title">
              <icon :name="item.icon"></icon>
              {{ item.name }}
              <span class="is-main" v-if="item.id === vuxWall.wxauthType && option.canSetType">主</span>
            </div>
            <div class="flex flex-a-c">
              <template v-if="option.canSetType">
                <el-button type="primary" v-if="item.id === vuxWall.wxauthType" disabled>当前主参与方式</el-button>
                <el-button type="primary" v-else @click="updateAttendType(item.id)">设为主参与方式</el-button>
              </template>
              <div class="flex mrg-l-20">
                <p class="blond">介绍：&nbsp;</p>
                <p class="info">{{ infoText }}</p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div class="box-content" v-loading="!isShow">
          <keep-alive>
            <component
              v-if="isShow"
              :is="getActiveAttend(actTab || 'common')"
              :showType="showType"
              :wall="vuxWall"
              :actType="actType"
              :route="route"
              :actId="option.actId"
            ></component>
          </keep-alive>
        </div>
      </template>
    </div>
  </template>
  <script>
  import api from '@/api'
  import HiCommonType from './commontype.vue'
  import HiCustomType from './customtype.vue'
  import HiWebType from './h5type.vue'
  import HiSelfType from './selftype.vue'
  import HiWxappType from './wxapptype.vue'

  import { mapActions, mapGetters } from 'vuex'
  const attendData = [
    {
      id: 'common',
      name: '通用公众号',
      icon: 'attend_eneral_fficial',
      component: 'HiCommonType',
    },
    {
      id: 'self',
      name: '自有公众号',
      icon: 'attend_private_fficial',
      component: 'HiSelfType',
    },
    {
      id: 'H5',
      name: 'H5网页',
      icon: 'attend_h5',
      component: 'HiWebType',
    },
    {
      id: 'wxapp',
      name: '微信小程序',
      icon: 'attend_mini_program',
      component: 'HiWxappType',
    },
    {
      id: 'custom',
      name: '自定义参与方式',
      icon: 'attend_custom',
      component: 'HiCustomType',
    },
  ]
  export default {
    name: 'WallAttendtype',
    props: {
      //展示方式dialog为partway组件内用,page为页面使用
      showType: {
        type: String,
        default: 'dialog',
      },
      actType: {
        type: String,
      },
      wall: {
        type: Object,
        default() {
          return {}
        },
      },
      route: {
        default: '',
      },
      //是否需要更新wall数据
      isNeedWallUpdate: {
        type: Boolean,
        default: false,
      },
      //更多配置内容,用于特定互动例如在线抽奖
      option: {
        type: Object,
        default() {
          return {
            canSetType: true, //是否关闭设置方式
            hideTypes: [], //需要隐藏的方式
            atcId: '',
            actType: '',
          }
        },
      },
    },
    components: {
      HiCommonType,
      HiSelfType,
      HiWebType,
      HiWxappType,
      HiCustomType,
    },
    data() {
      return {
        isShow: false,
        activeAttend: 'HiCommonType',
        actTab: '',
      }
    },
    computed: {
      ...mapGetters({
        wallFlagObj: 'wall/getWallFlagObj',
        isOem: 'user/isOem',
        isMerchants: 'user/isMerchants',
        merchantPartnerMsg: 'user/getMerchantPartnerMsg',
      }),
      infoText() {
        var textObj = {
          common: '微信扫码参与，方便参与者快速找到互动参与入口',
          self: '可绑定自己的公众号进行活动，为公众号吸粉，方便参与者快速找到互动参与入口',
          H5: '微信快速扫描或点击链接参与，便于主办方在不同参与渠道内灵活调用',
          wxapp: '微信小程序参与，方便参与者快速找到互动入口，主办方公众号内灵活调用',
          custom: '主办方使用自有渠道设计手机参与入口，可按需求灵活搭配互动独立二维码及链接使用',
        }
        if (this.actTab) {
          return textObj[this.actTab]
        } else {
          return '微信扫码参与，方便参与者快速找到互动参与入口'
        }
      },
      contentTypeName() {
        let name = '通用公众号'
        if (this.vuxWall.wxauthType) {
          this.attendList.forEach((item) => {
            if (item.id === this.vuxWall.wxauthType) {
              name = item.name
            }
          })
        }
        return name
      },
      wallFlag() {
        return this.wall.wallFlag || this.$route.query.wallFlag
      },
      vuxWall() {
        return this.wallFlagObj[this.wallFlag] || {}
      },
      attendList() {
        let data = attendData
        if (['wall-wheelsurf', 'wall-ninegrids', 'wall-mysterybox', 'wall-packetwall'].includes(this.route)) {
          data = data.filter((item) => item.id !== 'custom')
        }

        if (this.isOem) {
          data = data.filter((item) => item.id === 'H5' || item.id === 'custom')
        }
        return data.filter((item) => {
          return !this.option.hideTypes.includes(item.id)
        })
      },
    },
    methods: {
      ...mapActions({
        fetchWall: 'wall/fetchWall',
        fetchAllConfig: 'wall/fetchAllConfig',
      }),
      async updateAttendType(type) {
        try {
          await api.wall.update({
            where: { id: this.vuxWall.id },
            update: { wxauthType: this.actTab },
          })
          await this.fetchWall({
            wallFlag: this.wallFlag,
            force: true,
          })
          this.$notify.success({ title: '成功', message: '活动主参与方式设置完成' })
        } catch (err) {
          console.log(err)
          this.$notify.error(err.msg || '提交失败！')
        }
      },
      getActiveAttend(id) {
        let component = 'common'
        if (id) {
          this.attendList.forEach((item) => {
            if (item.id === id) {
              component = item.component
            }
          })
        }
        return component
      },
    },
    async mounted() {
      await this.fetchAllConfig(this.vuxWall.id)
      //如果没有传入确定设置值，则根据主参与方式配置来，用于在线抽奖等参与方式需要控制的互动
      this.actTab = this.option.actType || this.vuxWall.wxauthType
      this.isShow = true
    },
  }
  </script>
  <style scoped lang="scss">
  h3 {
    text-align: center;
  }
  .box {
    .top {
      font-size: 14px;
      margin: 10px 0;

      .blond {
        font-weight: 600;
      }
    }

    .info {
      color: #999;
    }

    .mrg-l-20 {
      margin-left: 20px;
    }

    .atten-title {
      position: relative;
      margin: 0 5px;

      .is-main {
        width: 20px;
        height: 20px;
        background: rgb(250, 99, 99);
        border-radius: 50%;
        line-height: 20px;
        text-align: center;
        color: #fff;
        position: absolute;
        right: -20px;
        top: 0px;
        font-size: 12px;
      }
    }
    .box-content {
      min-height: 300px;
    }
  }
  </style>
