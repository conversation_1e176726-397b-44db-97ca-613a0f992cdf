import api from './api'
export default {
  page: postData => api.fetchBaseData('/pro/hxc/proropepack/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proropepack/list.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/proropepack/delete.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/proropepack/add.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/proropepack/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proropepack/update.htm', postData),
  closed: postData => api.fetchBaseData('/pro/hxc/proropepack/closed.htm', postData),
}