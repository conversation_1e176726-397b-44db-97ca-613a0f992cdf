<template>
  <hi-wall-set-right>
    <div slot="content" class="applySign-box">
      <el-button class="back-button" @click="$emit('toList')">&lt; 返回轮次列表</el-button>
      <hi-question-bank-manager mode="manage" />
    </div>
  </hi-wall-set-right>
</template>

<script>
import HiQuestionBankManager from '../common/questionBank/QuestionBankManager.vue'
export default {
  name: 'AnswerraceManage',
  inject: ['wall'],
  components: {
    HiQuestionBankManager,
  },
  props: {
    config: {
      type: Object,
      default: () => ({}),
    },
  },
}
</script>

<style scoped>
.back-button {
  margin-bottom: 20px;
}
</style>
