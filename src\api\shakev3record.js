import api from './api'
export default {
  page: (postData) => api.fetchBaseData('/pro/hxc/proshakev3record/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proshakev3record/list.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proshakev3record/update.htm', postData),
  teamscore: (postData) => api.fetchBaseData('/pro/hxc/proshakev3record/teamscore.htm', postData),
}
