import api from './api';
export default {
  list: (postData) => api.fetchBaseData('/pro/hxc/propiclotterygroup/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/propiclotterygroup/add.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/propiclotterygroup/update.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/propiclotterygroup/delete.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/propiclotterygroup/batch.htm', postData),
};
