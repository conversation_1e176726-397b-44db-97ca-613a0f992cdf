import api from './api'
export default {
  // 盲盒抽奖、红包墙，配置
  read: (v) => api.fetchBaseData('/pro/hxc/promysteryboxconfig/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/promysteryboxconfig/update.htm', v),
  add: (v) => api.fetchBaseData('/pro/hxc/promysteryboxconfig/add.htm', v),
  page: (v) => api.fetchBaseData('/pro/hxc/promysteryboxconfig/page.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/promysteryboxconfig/list.htm', v),
  delete: (postData) => api.fetchBaseData('/pro/hxc/promysteryboxconfig/delete.htm', postData),
  finish: (postData) => api.fetchBaseData('/pro/hxc/promysteryboxconfig/finish.htm', postData),
}
