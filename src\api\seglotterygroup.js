import api from './api'
export default {
  list: (postData) => api.fetchBaseData('/pro/hxc/proseglotterygroup/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proseglotterygroup/add.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proseglotterygroup/update.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proseglotterygroup/delete.htm', postData),
}
