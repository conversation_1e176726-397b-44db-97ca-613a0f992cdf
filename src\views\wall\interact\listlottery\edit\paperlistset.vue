<template>
  <div class="listlottery-paperlist-box">
    <!-- {{ dataList.map((item) => item.showSwitch) }} -->
    <!-- {{ importconfigList.map((item) => item.showSwitch) }} -->
    <table class="table-option mar-t-15" border="1" v-if="importconfigList.length > 0">
      <tr class="head">
        <th class="before-name">大屏幕展示位置</th>
        <th class="order">排序</th>
        <th>名称</th>
        <th class="no-repeat">不可重复</th>
        <th class="t-oprate">屏幕展示</th>
      </tr>
      <hi-draggable class="drag-ul" v-model="dataList" tag="tbody" :sortable="{ draggable: 'tr', handle: '.move' }" @end="endDrag">
        <tr class="tr-box" v-for="(item, index) in dataList" :key="item.dn">
          <td class="before-name">
            <p>{{ explain[index].title }}</p>
            <p>{{ explain[index].sub }}</p>
          </td>
          <td class="order">
            <img class="move" src="~@/assets/wall/interact/drag.png" />
          </td>
          <td class="table-name">{{ getName(item) }}</td>
          <td class="no-repeat">
            <p>{{ item.dn === 'd1' ? '是' : '否' }}</p>
            <p v-if="item.dn === 'd1'">唯一标识</p>
          </td>
          <td class="t-oprate">
            <hi-switch
              v-model="item.showSwitch"
              active-value="Y"
              inactive-value="N"
              active-text="开启"
              inactive-text="关闭"
              @change="changeSwitch"
              :disabled="isDisabled(item)"
            >
            </hi-switch>
          </td>
        </tr>
      </hi-draggable>
    </table>
    <p class="f999 info">可拖动调整信息展示在大屏幕的位置，不同主题可展示字段数量不同</p>
  </div>
</template>
<script>
import { Hi } from '@/libs/common'
import { mapMutations, mapState } from 'vuex'
import HiDraggable from 'vuedraggable'
export default {
  name: 'Theme',
  props: {
    disabled: { type: Boolean, default: false },
    actConfig: {
      type: Object,
      default: () => {},
    },
    themeType: {
      type: String,
      default: '',
    },
  },
  components: { HiDraggable },
  data() {
    return {
      dataList: [],
      explain: [
        {
          title: '第一列',
          sub: '头像区域展示项',
        },
        {
          title: '第二列',
          sub: '名称区域展示项',
        },
        {
          title: '第三列',
          sub: ' ',
        },
      ],
    }
  },
  computed: {
    ...mapState('ListlotteryEdit', {
      importconfigList: (state) => state.importconfigList,
      config: (state) => state.config,
    }),
  },
  watch: {
    importconfigList: {
      deep: true,
      immediate: true,
      handler(v) {
        this.dataList = Hi.Object.copy(v)
      },
    },
    themeType: {
      immediate: true,
      handler(v) {
        this.setImportconfigList(
          this.dataList.map((item) => {
            if (v !== 'MINGDANYAOHAO' && (item.dn === 'd3' || item.dn === 'd2')) {
              item.showSwitch = 'Y'
            }
            return item
          })
        )
      },
    },
  },
  methods: {
    ...mapMutations('ListlotteryEdit', ['setImportconfigList']),
    getName(item) {
      try {
        let obj = {
          d1: 'd1Name',
          d2: 'd2Name',
          d3: 'd3Name',
        }
        return this.config[obj[item.dn]]
      } catch (error) {}
    },
    changeSwitch() {
      this.setImportconfigList(Hi.Object.copy(this.dataList))
    },
    //拖拽结束
    async endDrag() {
      if (this.disabled) return
      try {
        let arr = []
        this.dataList.forEach((item, index) => {
          item.sort = index + 1
        })
        this.setImportconfigList(Hi.Object.copy(this.dataList))
      } catch (err) {
        console.log(err)
      }
    },
    isDisabled(item) {
      if (this.themeType !== 'MINGDANYAOHAO' && (item.dn === 'd3' || item.dn === 'd2')) {
        return true
      }
      return this.disabled || item.dn === 'd1'
    },
  },
  async created() {},
}
</script>
<style scoped lang="stylus">
.table-option
  width 500px
  border 1px solid #dfe6ec
  border-bottom 0
  border-bottom 1px solid #dfe6ec
  border-collapse collapse
  .head
    background #eef1f6
    th
      padding 10px 5px
  td
  th
    text-align left
    box-sizing border-box
    padding 0px 8px
    position relative
    text-align center
  tr
    margin 5px 0
  .t-oprate
  >>> ::-webkit-scrollbar
    width 4px
  .drag-ul
    overflow-y auto
  .order
    width 60px
    align-items center
    text-align center
    .move
      cursor move
      margin 10px
      padding 10px
  .before-name
    width 120px
    text-align center
  .no-repeat
    width 80px
.info
  width 500px
  text-align center
  line-height 3
</style>
