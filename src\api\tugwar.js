import api from './api'

export default {
  add: postData => api.fetchBaseData('/pro/hxc/protugwar/add.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/protugwar/delete.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/protugwar/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/protugwar/update.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/protugwar/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/protugwar/list.htm', postData),
}
