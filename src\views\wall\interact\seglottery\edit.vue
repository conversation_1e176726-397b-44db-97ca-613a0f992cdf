<template>
  <hi-wall-set-right class="wall-piclottery-edit-box" v-if="loadPage">
    <div>
      <el-form :model="nowActConfig" :rules="rules" ref="form" label-width="150px">
        <hi-collapse title="基础设置">
          <el-form-item label="奖项名称：" prop="title">
            <el-input class="w348" v-model.trim="nowActConfig.title" placeholder="请输入奖项名称" maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="名单设置：">
            <el-button type="text" @click="go">去设置>>> </el-button>
          </el-form-item>
        </hi-collapse>
        <hi-collapse title="奖品设置">
          <hi-prize
            singlePrize
            :actConfig="nowActConfig"
            :disabled="isEnd"
            @create="(cb) => cb(save)"
            @awardListChange="onAwardListChange"
          ></hi-prize>
          <el-form-item v-if="_countMax" class="mrg-t-20" label="单次抽出：">
            <el-input-number
              controls-position="right"
              class="w100 mr-10"
              v-model="nowActConfig.onceNum"
              :min="1"
              :precision="0"
              :max="Number(_countMax)"
              placeholder=""
            ></el-input-number>
            <span>人</span>
            <span class="mrg-l-10 red">调整抽出数量后，请点击大屏幕“名单数量”处刷新按钮，刷新后生效</span>
          </el-form-item>
        </hi-collapse>
        <hi-collapse title="分段规则" showDescText="设置分段规则需提前导入数据">
          <el-form label-width="150px" :disabled="isEnd">
            <el-form-item label="滚动数据：" prop="rollContent">
              <el-select
                class="w-300"
                v-model="selectedStages"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="请选择滚动数据"
                @change="handleStageChange"
              >
                <el-option v-for="item in stageList" :key="item.key" :label="item.name" :value="item.key"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="滚动顺序：">
              <el-radio-group v-model="nowActConfig.rollOrder">
                <el-radio label="ASC">从前往后</el-radio>
                <el-radio label="DESC">从后往前</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="抽取方式：">
              <el-radio-group v-model="nowActConfig.extractType">
                <el-radio label="SINGLE">
                  一次抽取一个分段
                  <el-tooltip effect="dark" content="大屏幕点击一次开始/停止，抽出一个分段，多个分段时需多次点击开始/停止" placement="top-start">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                </el-radio>
                <el-radio label="MULTI"
                  >一次抽取多个分段
                  <el-tooltip effect="dark" content="大屏幕点击一次开始/停止，所有分段依次抽出" placement="top-start">
                    <i class="el-icon-question"></i> </el-tooltip
                ></el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </hi-collapse>
      </el-form>
      <hi-collapse title="高级设置">
        <div class="relative pd-10">
          <el-form label-width="110px">
            <el-form-item label="允许重复中奖" prop="repeatwinSwitch" :disabled="false">
              <div class="flex flex-a-c" style="height: 32px">
                <hi-switch active-value="Y" inactive-value="N" active-text="开启" inactive-text="关闭" v-model="nowActConfig.repeatwinSwitch">
                </hi-switch>
                <el-tooltip effect="dark" content="开启后，其他轮次中过奖的人有机会再中，但本轮次中仅可被抽中一次" placement="top-start">
                  <img class="doubt" src="~@/assets/wall/interact/doubt.png" />
                </el-tooltip>
              </div>
            </el-form-item>
          </el-form>
          <el-form label-width="110px" :disabled="isSend">
            <el-form-item label="参与条件限制">
              <div class="import-limit">
                <el-radio-group v-model="nowActConfig.limitType">
                  <el-radio label="nolimit" border>不限制</el-radio>
                  <el-radio label="group" border>按分组限制</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
            <el-form-item v-if="nowActConfig.limitType === 'group'" label="选择分组字段">
              <el-transfer
                class="mrg-t-20"
                v-if="nowActConfig.limitContent"
                v-model="nowActConfig.limitContent"
                filterable
                :data="groupList"
                :titles="['不可参与', '可参与']"
                :props="{ key: 'key', label: 'label' }"
              ></el-transfer>
            </el-form-item>
          </el-form>
          <hi-unlock placement="middle" vatKey="seglotteryAdvancedLimit" :actInfo="config" @readConfig="$emit('updateConfig')"></hi-unlock>
        </div>
      </hi-collapse>
    </div>
    <div slot="control" class="control">
      <el-button plain @click="backList" :disabled="false">返回</el-button>
      <el-button type="primary" :disabled="saveDisabled" @click="save('NORMAL')">保存</el-button>
    </div>
  </hi-wall-set-right>
</template>
<script>
import api from '@/api'
import { Hi } from '@/libs/common'
import HiPrize from '@/views/wall/interact/common/prize/index.vue'

const defaultAct = {
  title: '排座抽奖', //奖项名称
  onceNum: 1, //一次抽出几个
  rollOrder: 'ASC', //抽奖顺序
  rollContent: '',
  extractType: 'SINGLE', //抽奖类型
  repeatwinSwitch: 'N', //允许重复中奖
  limitType: 'nolimit', //限制类型
  limitContent: [],
}
export default {
  name: 'wall-seglottery-edit',
  inject: ['wall'],
  props: {
    editId: {
      type: Number,
      default: 0,
    },
    themeId: {
      type: [String, Number],
      default: '',
    },
    config: {
      default: () => ({}),
    },
  },
  components: {
    HiPrize,
  },
  data() {
    return {
      loadPage: false,
      groupList: [], //所有分组
      selectedGroup: [], //选中的分组
      groupObj: {}, //分组数据对象
      actConfig: {}, //活动配置
      nowActConfig: {}, //当前活动配置
      awardList: [],
      sendInfo: [],
      stageList: [], //滚动数据
      selectedStages: '', //选中的滚动数据
      rules: {
        title: [{ required: true, message: '奖项名称不能为空', trigger: 'blur' }],
        onceNum: [
          { required: true, message: '单次抽奖人数不能为空', trigger: 'blur' },
          { required: true, message: '单次抽奖人数不能为空', trigger: 'change' },
        ],
        rollContent: [{ required: true, message: '滚动数据不能为空', trigger: 'change' }],
      },
    }
  },
  computed: {
    disabled() {
      return this.nowActConfig.id && this.nowActConfig.displayState === 'DOWN_WALL'
    },
    hasChanges() {
      const d1 = Hi.Object.copy(this.actConfig)
      const d2 = Hi.Object.copy(this.nowActConfig)
      if (d1.limitContent) {
        d1.limitContent = JSON.stringify(d1.limitContent)
      }
      if (d2.limitContent) {
        d2.limitContent = JSON.stringify(d2.limitContent)
      }
      return this.nowActConfig && Object.keys(this.nowActConfig).length > 0 && !!Hi.Object.difference(d1, d2, { deep: true })
    },
    defaultChange() {
      return Hi.Object.difference(defaultAct, this.nowActConfig, {
        ignoreKey: ['updateDate'],
      })
    },
    saveDisabled() {
      return this.nowActConfig.id && !this.hasChanges
    },
    isEnd() {
      return this.nowActConfig.id && this.nowActConfig.count > 0
    },
    _countMax() {
      return this.awardList.length ? this.awardList.reduce((total, award) => total + award.count, 0) : Infinity
    },
    isSend() {
      let num = 0
      this.sendInfo.forEach((item) => {
        num += item.sendCnt
      })
      return !!num
    },
  },
  watch: {
    actConfig: {
      handler(v) {
        if (v.id) {
          Object.keys(v).forEach((i) => {
            this.$set(this.nowActConfig, i, Hi.Object.copy(v[i]))
          })
        } else {
          this.nowActConfig = Hi.Object.copy(defaultAct)
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    async fetchStageList() {
      try {
        const data = await api.seglotterystage.list({ where: { wallId: this.wall.id } })
        this.stageList = data
      } catch (err) {
        console.error('获取抽奖阶段列表失败:', err)
        this.stageList = []
      }
    },
    handleStageChange(value) {
      let _rollContent = value
      if (typeof _rollContent === 'string') {
        _rollContent = JSON.parse(_rollContent || '[]')
      }
      this.nowActConfig.rollContent = JSON.stringify(_rollContent)
    },
    // 新窗口打开名单管理
    async oepnRoster() {
      const routeUrl = this.$router.resolve({
        name: 'wall-interact-seglottery',
        query: { wallFlag: this.$route.query.wallFlag, open: 'roster' },
      })
      window.open(routeUrl.href, '_blank')
    },
    async _insertLottery() {
      const submitData = {
        ...this.nowActConfig,
        limitContent: JSON.stringify(this.nowActConfig.limitContent || []),
      }
      const data = await api.seglottery.add({
        wallId: this.wall.id,
        themeId: this.themeId,
        ...submitData,
      })
      this.$notify.success({ title: '成功', message: '活动创建成功' })
      return data
    },
    async _updateLottery() {
      const d1 = Hi.Object.copy(this.actConfig)
      const d2 = Hi.Object.copy(this.nowActConfig)
      if (d1.limitContent) {
        d1.limitContent = JSON.stringify(d1.limitContent)
      }
      if (d2.limitContent) {
        d2.limitContent = JSON.stringify(d2.limitContent)
      }
      const update = Hi.Object.difference(d1, d2, { deep: true })
      if (update) {
        await api.seglottery.update({
          where: { id: this.nowActConfig.id, wallId: this.config.wallId },
          update: update,
        })
        this.$notify.success({ title: '成功', message: '活动保存成功' })
      }
    },
    async save() {
      try {
        await this.$refs.form.validate()
        if (this.selectedStages.length === 0) {
          this.$notify.error({ title: '错误', message: '请选择滚动数据' })
          return false
        }
        if (this.nowActConfig.id) {
          await this._updateLottery()
          await this.fetchpiclottery()
        } else {
          const id = await this._insertLottery()
          await this.fetchpiclottery(id)
        }
      } catch (err) {
        console.log(err)
        if (err === false) return false
        if (err !== 'cancel') {
          this.$notify.error({ title: '错误', message: err?.msg || '提交数据出错' })
        }
      }
    },
    //返回列表
    async backList() {
      const deeps = this.actConfig.id ? this.hasChanges : this.defaultChange
      if (deeps) {
        this.$confirm('是否保存您所做的更改？', '提示', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            await this.save('NORMAL')
            this.$emit('list')
          })
          .catch((err) => {
            if (err === 'cancel') {
              this.$emit('list')
            }
          })
      } else {
        this.$emit('list')
      }
    },
    async onAwardListChange({ awardList }) {
      this.awardList = Hi.Object.copy(awardList)
      try {
        if (awardList && awardList.length > 0) {
          this.sendInfo = await api.awards.sendInfo({
            where: {
              wallId: this.wall.id,
              idList: awardList.map((item) => item.id),
            },
          })
        } else {
          this.sendInfo = []
        }
      } catch (err) {
        console.error('获取奖品发送信息失败:', err)
        this.sendInfo = []
      }
    },
    go() {
      window.open('/pro/admin/wall/interact/seglottery.html?wallFlag=' + this.wall.wallFlag + '&open=roster', '_blank')
    },
    //初始化数据
    async fetchpiclottery(id) {
      try {
        let editId = id || this.editId || this.nowActConfig.id
        if (editId) {
          const data = await api.seglottery.read({
            where: { id: editId, wallId: this.wall.id },
          })
          const parseContent = (content, defaultValue) => {
            if (typeof content === 'string') {
              try {
                return JSON.parse(content)
              } catch (e) {
                return defaultValue
              }
            }
            return content
          }

          data.limitContent = parseContent(data.limitContent, [])
          this.selectedStages = parseContent(data.rollContent, [])
          this.actConfig = data
        } else {
          this.selectedStages = this.stageList.map((item) => item.key)
          this.nowActConfig.rollContent = JSON.stringify(this.selectedStages)
        }
      } catch (err) {
        console.error(err)
      }
    },
    async fetchImportList() {
      try {
        const response = await api.seglotterygroup.list({
          where: { wallId: this.wall.id },
        })
        this.groupList = response.map((item) => ({
          key: item.id,
          label: item.name,
        }))
      } catch (err) {
        console.error('获取导入列表失败:', err)
        this.groupList = []
      }
    },
  },
  async mounted() {
    await this.fetchImportList()
    await this.fetchStageList()
    await this.fetchpiclottery()
    if (this.$route.query.open === 'roster') {
      // open = roster 需要直接打开名单管理，随即删掉这个url参数
      this.isRoster = true
      await this.$router.replace({
        name: 'wall-interact-seglottery',
        query: { wallFlag: this.$route.query.wallFlag },
      })
    }
    this.$emit('loaded')
    this.loadPage = true
  },
}
</script>
<style scoped lang="scss">
::v-deep .el-checkbox {
  display: block !important;
}
.red {
  color: #f56c6c;
  font-size: 12px;
  font-weight: 600;
}
.mr-10 {
  margin-right: 10px;
}

.w348 {
  width: 348px;
}

.w100 {
  width: 100px;
}

.doubt {
  margin-left: 10px;
}

.mrg-t-20 {
  margin-top: 20px;
}

.ask {
  position: relative;
  top: 5px;
}
</style>
