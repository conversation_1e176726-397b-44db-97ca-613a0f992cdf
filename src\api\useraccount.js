import api from './api'
export default {
  // 红包账户
  read: postData => api.fetchBaseData('/pro/hxc/prouseraccount/read.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/prouseraccounttrade/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prouseraccounttrade/list.htm', postData),
  // 兑换嗨豆
  convertPoints: postData => api.fetchBaseData('/pro/hxc/prouseraccount/convertPoints.htm', postData),
  //快直播剩余流量
  fastLiveGetCurrentFlux: postData => api.fetchBaseData('/pro/hxc/prouseraccount/fastLiveGetCurrentFlux.htm', postData),
}
