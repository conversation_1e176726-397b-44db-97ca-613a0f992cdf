import api from './api'
export default {
  add: postData => api.fetchBaseData('/pro/hxc/provoicepackawards/add.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/provoicepackawards/update.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/provoicepackawards/list.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/provoicepackawards/delete.htm', postData),
}
