import api from './api'
export default {
  list: (postData) => api.fetchBaseData('/pro/hxc/proshakev3awards/list.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/proshakev3awards/batch.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proshakev3awards/add.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proshakev3awards/delete.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proshakev3awards/update.htm', postData),
}
