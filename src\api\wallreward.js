import api from './api'
export default {
  page: postData => api.fetchBaseData('/pro/hxc/prowallreward/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prowallreward/list.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/prowallreward/delete.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/prowallreward/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prowallreward/update.htm', postData),
  insert: postData => api.fetchBaseData('/pro/hxc/prowallreward/insert.htm', postData),
}
