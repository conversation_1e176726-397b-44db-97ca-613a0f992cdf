import api from './api';

export default {
  insert: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerteam/insert.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerteam/delete.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerteam/read.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerteam/update.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerteam/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerteam/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerteam/add.htm', postData),
  onlineCnt: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerteam/onlineCnt.htm', postData),
};
