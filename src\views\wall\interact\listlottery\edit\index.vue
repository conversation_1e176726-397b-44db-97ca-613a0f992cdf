<template>
  <hi-wall-set-right class="wall-listlottery-edit-box" v-if="loadPage">
    <div>
      <el-form :model="actConfig" :rules="rules" ref="form" label-width="150px">
        <hi-collapse title="基础设置">
          <el-form-item label="奖项名称：" prop="title">
            <el-input class="w348" v-model.trim="actConfig.title" placeholder="请输入奖项名称" maxlength="20"></el-input>
            <div class="mrg-l-5" style="display: inline-block">
              <el-checkbox v-model="actConfig.hideTitleSwitch" :true-label="'Y'" :false-label="'N'">大屏幕不展示标题</el-checkbox>
            </div>
          </el-form-item>
        </hi-collapse>
        <hi-collapse title="奖品设置">
          <el-form-item label="抽奖模式：">
            <el-radio-group :value="actConfig.singlePrizeSwitch" :disabled="limitDisable" @input="radioChange">
              <el-radio label="Y">单轮单奖品</el-radio>
              <el-radio label="N">单轮多奖品</el-radio>
            </el-radio-group>
            <div class="sub" style="margin-top: -10px">
              <div>一轮仅可抽取一种奖品</div>
              <div>一轮可一次性抽取多种奖品</div>
            </div>
          </el-form-item>
          <el-form-item v-if="actConfig.singlePrizeSwitch === 'N'" label="单次抽出数量：">
            <div class="sub">点击"抽奖按钮"一次，抽出的中奖人数量</div>
            <el-radio-group v-model="actConfig.extractType" :disabled="limitDisable">
              <div class="flex flex-d-c">
                <el-radio label="TOTAL" class="mrg-t-10"
                  >统一设置总数量，随机分配奖品
                  <el-tooltip effect="dark" content="实时显示签名数量" placement="top-start">
                    <template slot="content">
                      <p>奖品随机分配给中奖人，每种奖品中奖人数量不同</p>
                      <p>仅保证奖品数之和等于设置值</p>
                      <p>适用于： 相同价值奖项一次性抽出，随机分配给中奖人。</p>
                    </template>
                    <img class="doubt mrg-r-10" src="~@/assets/wall/interact/ask.png" />
                  </el-tooltip>
                </el-radio>
                <el-radio label="SINGLE" class="mrg-t-20"
                  >按奖品分别设置数量，固定分配奖品
                  <el-tooltip effect="dark" content="实时显示签名数量" placement="top-start">
                    <template slot="content">
                      <p>每种奖品抽出的中奖人数量固定</p>
                      <p>适用于：不同级别奖项一次性抽出</p>
                    </template>
                    <img class="doubt mrg-r-10" src="~@/assets/wall/interact/ask.png" />
                  </el-tooltip>
                </el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <hi-prize
            :actConfig="actConfig"
            :disabled="limitDisable"
            @create="(cb) => cb(save, 'NORMAL')"
            @awardListChange="awardListChange"
          ></hi-prize>
          <el-form-item
            v-if="actConfig.singlePrizeSwitch === 'Y' || (actConfig.extractType === 'TOTAL' && awardList.length)"
            class="mrg-t-20"
            label="单次抽出："
          >
            <el-input-number
              controls-position="right"
              class="w100 mr-10"
              v-model="actConfig.onceNum"
              :min="1"
              :precision="0"
              :max="Number(_countMax)"
              placeholder=""
            ></el-input-number>
            <span>人</span>
          </el-form-item>
          <el-form-item label="名单设置：">
            <el-button type="text" @click="go">去设置>>> </el-button>
          </el-form-item>
        </hi-collapse>
      </el-form>
      <hi-collapse title="高级设置">
        <div class="relative pd-10">
          <el-form label-width="110px">
            <el-form-item label="允许重复中奖" prop="repeatwinSwitch" v-if="actType !== 'NUMBER_LOTTERY'">
              <div class="flex flex-a-c" style="height: 32px">
                <hi-switch active-value="Y" inactive-value="N" active-text="开启" inactive-text="关闭" v-model="actConfig.repeatwinSwitch">
                </hi-switch>
                <el-tooltip effect="dark" content="开启后，其他轮次中过奖的人有机会再中，但本轮次中仅可被抽中一次" placement="top-start">
                  <img class="doubt" src="~@/assets/wall/interact/doubt.png" />
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="参与条件限制">
              <hi-import-limit
                :groupList="groupList"
                :wall="wall"
                :actConfig="actConfig"
                :config="config"
                :ableGroup="listlotteryawardgroup"
                :disabled="disabled"
                @groupChange="changeLimitGroup"
                @updateConfig="fetchConfig"
              ></hi-import-limit>
            </el-form-item>
            <el-form-item label="隐藏参与人数">
              <div class="flex flex-a-c">
                <hi-switch active-value="Y" inactive-value="N" active-text="开启" inactive-text="关闭" v-model="actConfig.hiddenJoinNumSwitch">
                </hi-switch>
                <span class="mrg-l-10">开启后大屏幕将不展示参与人数</span>
              </div>
            </el-form-item>
            <el-form-item label="大屏幕展示">
              <Hi-paperlistset :actConfig="actConfig" :themeType="themeType" :disabled="disabled"></Hi-paperlistset>
            </el-form-item>
            <el-form-item label="数据隐私保护">
              <hi-privacy :disabled="disabled"></hi-privacy>
            </el-form-item>
          </el-form>
          <hi-unlock
            placement="middle"
            vatKey="listlotteryAdvancedLimit"
            :actInfo="config"
            @readConfig="
              () => {
                fetchConfig(), fetchAllData()
              }
            "
          ></hi-unlock>
        </div>
      </hi-collapse>
    </div>
    <div slot="control" class="control">
      <el-button plain @click="backList" :disabled="false">返回</el-button>
      <el-button type="primary" :disabled="btnDisabled || disabled" @click="save('NORMAL')">保存</el-button>
    </div>
  </hi-wall-set-right>
</template>
<script>
import api from '@/api'
import { Hi, timer } from '@/libs/common'
import HiPrize from '@/views/wall/interact/common/prize/index.vue'
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex'
import HiImportLimit from './importLimit.vue'
import HiPaperlistset from './paperlistset.vue'
import HiPrivacy from './privacy.vue'
import router from '@/router'

export default {
  name: 'wall-listlottery-edit',
  props: {
    editId: {
      type: Number,
      default: 0,
    },
    themeType: {
      type: String,
      default: '',
    },
    listlotteryType: {
      type: String,
      default: '',
    },
  },
  components: {
    HiPaperlistset,
    HiImportLimit,
    HiPrivacy,
    HiPrize,
  },
  data() {
    return {
      then: null,
      loadPage: false,
      prizeNameFocusFlag: false, // 奖品名称选中标记
      groupList: [], //所有分组
      groupObj: {}, //f分组数据对象
      awardList: [],
      rules: {
        title: [{ required: true, message: '奖项名称不能为空', trigger: 'blur' }],
        prizeName: [
          {
            required: true,
            message: '奖品名称不能为空',
            trigger: 'blur',
            whitespace: true,
          },
        ],
        prizeNum: [
          { required: true, message: '奖品数量不能为空', trigger: 'blur' },
          { required: true, message: '奖品数量不能为空', trigger: 'change' },
        ],
        onceNum: [
          { required: true, message: '单次抽奖人数不能为空', trigger: 'blur' },
          { required: true, message: '单次抽奖人数不能为空', trigger: 'change' },
        ],
      },
    }
  },
  computed: {
    ...mapState('wall', ['wall']),
    ...mapState('ListlotteryEdit', [
      'actConfig',
      'config',
      'originActConfig',
      'listlotteryawardgroup',
      'importconfigList',
      'orignImportconfigList',
      'listlotteryawardgroupObj',
    ]),
    ...mapGetters({
      isUpdate: 'ListlotteryEdit/isUpdate',
      defaultChange: 'ListlotteryEdit/defaultChange',
    }),
    actType() {
      if (!this.actConfig.id) {
        return this.listlotteryType
      } else {
        return this.actConfig.listlotteryType
      }
    },
    limitDisable() {
      return (this.actConfig.id && this.actConfig.displayState === 'DOWN_WALL') || (this.actConfig.id && this.actConfig.count > 0)
    },
    prizeNamePlaceholder() {
      return this.prizeNameFocusFlag ? '' : '如：\n苹果手机\n价值5000元'
    },
    disabled() {
      return this.actConfig.id && this.actConfig.displayState === 'DOWN_WALL'
    },
    btnDisabled() {
      return !this.isUpdate
    },
    _countMax() {
      return this.awardList.length ? this.awardList.reduce((total, award) => total + award.count, 0) : Infinity
    },
  },
  methods: {
    ...mapMutations('ListlotteryEdit', ['setListlotteryawardgroup']),
    ...mapActions('ListlotteryEdit', ['fetchAllData', 'saveAllData', 'fetchConfig']),
    radioChange(e) {
      if (e === 'Y' && this.actConfig.id && this.awardList.length > 1) {
        this.$notify.error({ title: '错误', message: '已设置多奖品' })
        return
      }
      if (e === 'Y') {
        this.actConfig.extractType = 'TOTAL'
      }
      this.actConfig.singlePrizeSwitch = e
    },
    // 新窗口打开名单管理
    async oepnRoster() {
      const routeUrl = this.$router.resolve({
        name: 'wall-interact-listlottery',
        query: { wallFlag: this.$route.query.wallFlag, open: 'roster' },
      })
      window.open(routeUrl.href, '_blank')
    },
    async save(displayState) {
      if (this.checkReplaceStr()) {
        this.$notify.error({ title: '错误', message: '请设置替换字符' })
        return false
      }
      await this.$refs.form.validate()
      try {
        this.actConfig.displayState = displayState
        this.actConfig.themeType = this.themeType
        await this.saveAllData(displayState)
        this.$notify.success({ title: '成功', message: '保存成功' })
      } catch (err) {
        console.log(err)
        if (err !== 'cancel') {
          this.$notify.error({ title: '错误', message: err.msg || '提交数据出错' })
          return false
        }
      }
    },
    //检测替换符是否填写完整
    checkReplaceStr() {
      let replaceStrNoOk = false
      this.importconfigList.forEach((item) => {
        if (item.privacySwitch === 'Y' && !item.replaceStr) {
          replaceStrNoOk = true
        }
      })
      return replaceStrNoOk
    },
    uploadSuccess(file, fileList) {
      file = file.data
      this.actConfig.prizeImg = Hi.String.dealUrl(file.url)
    },
    // all分组列表
    async fetchGroupList() {
      try {
        let list = await api.listlotterygroup.list({
          where: { wallId: this.wall.id },
        })
        list.forEach((item) => {
          this.groupObj[item.id] = item
        })
        this.groupList = list
      } catch (err) {
        console.log(err)
        this.$notify.error({ title: '错误', message: err.msg })
      }
    },
    changeLimitGroup(data) {
      let arr = []
      data.forEach((item) => {
        if (this.groupObj[item]) {
          let obj = {
            groupId: this.groupObj[item].id,
            wallId: this.wall.id,
          }
          this.actConfig.id && (obj.listlotteryId = this.actConfig.id)
          //如果原本就存在的数据，使用元数据
          if (this.listlotteryawardgroupObj[item]) {
            obj = this.listlotteryawardgroupObj[item]
          }
          arr.push(obj)
        }
      })
      this.setListlotteryawardgroup(arr)
    },
    //返回列表
    async backList() {
      if (this.isUpdate && this.defaultChange) {
        this.$confirm('是否保存您所做的更改？', '提示', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            await this.save('NORMAL')
            this.$emit('list')
          })
          .catch((err) => {
            if (err === 'cancel') {
              this.$emit('list')
            }
          })
      } else {
        this.$emit('list')
      }
    },
    awardListChange({ awardList }) {
      this.awardList = Hi.Object.copy(awardList)
    },
    go() {
      window.open('/pro/admin/wall/interact/listlottery.html?wallFlag=' + this.wall.wallFlag + '&open=roster', '_blank')
    },
  },
  async created() {
    //新增的时候，需要传入listlotteryType，来自于新增的选择，编辑时会去read的数据里看
    await this.fetchAllData({
      id: this.editId,
      type: this.listlotteryType,
    })
    await this.fetchGroupList()
    await timer(300)
    this.$emit('loaded')
    this.loadPage = true
  },
}
</script>
<style scoped lang="stylus">
.mr-10 {
  margin-right: 10px;
}

.ml-10 {
  margin-left: 10px;
}

.w348 {
  width: 348px;
}

.w100 {
  width: 100px;
}

.margin1020 {
  margin: 0 15px 0 10px;
}

.btn-to-edit {
  padding-top: 6px;
  padding-bottom: 6px;
}

.control {
  display: flex;
  align-items: center;
  justify-content: center;
}

.componentbox {
  font-size: 14px;
  margin-top: 15px;
  padding-bottom: 17px;
}

.award-pic {
  width: 86px;
  height: 86px;
  border: 1px solid #bfcbd9;

  .img {
    display: inline-block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}

.inline {
  display: inline-block;
}

>>> .el-upload--picture-card {
  width: 86px;
  height: 86px;
  line-height: 86px;
  border: 0;
  position: relative;
}

>>> .el-upload--picture-card p {
  width: 86px;
  height: 86px;
  line-height: 86px;
  text-align: center;
  position: absolute;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.4);
  color: #fff;
  visibility: hidden;
}

>>> .el-upload--picture-card:hover p {
  visibility: visible;
}

.sub{
  display: flex;
  font-size: 12px;
  color: #999;
  div:first-child{
    width : 140px;
  }
}
.doubt{
  margin-left: 10px;
}
</style>
