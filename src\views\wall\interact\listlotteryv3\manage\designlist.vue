<template>
  <div class="design-list-box">
    <el-dialog
      title="名单设计"
      :visible.sync="localVisible"
      width="750px"
      :close-on-click-modal="false"
      destroy-on-close
      @open="handleOpen"
      @close="handleClose"
    >
      <div class="flex flex-a-c flex-j-sb mrg-b-10">
        <div class="design-tip">根据活动需要设计可展示的信息内容，最多可添加三项信息</div>
        <div>
          <el-button plain :disabled="nowImportFormList.length >= 3" type="primary" icon="el-icon-plus" @click="handleAddInfo">添加信息</el-button>
        </div>
      </div>
      <el-table class="w-full" ref="tableRef" :data="nowImportFormList" border row-key="key">
        <el-table-column label="排序" width="60" align="center">
          <template v-slot="{}">
            <div class="drag-handle">
              <img class="move" src="~@/assets/wall/interact/drag.png" />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="信息类型" width="150">
          <template v-slot="{ row }">
            <el-select class="w-full" v-model="row.type" placeholder="请选择">
              <el-option
                v-for="option in typeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
                v-if="!(row.key === 'd1' && option.value === 'group')"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="信息名称">
          <template v-slot="{ row }">
            <el-input v-model="row.name" placeholder="请输入"></el-input>
          </template>
        </el-table-column>

        <el-table-column width="120" align="center">
          <template v-slot:header>
            隐私保护
            <el-tooltip placement="top" popper-class="ask-popper">
              <div slot="content">
                <span style="font-weight: bold; color: red">开启隐私保护后，会自动隐藏对应列的数据信息。</span>如：<br />
                手机号：隐藏中间4位<br />
                身份证号：隐藏年月日<br />
                姓名：保留第一位和最后一位其它隐藏（姓名为2个字时，只保留第一个字）<br />
                文本：保留第一个和最后一个字：只有2个字时，只保留第一个字<br />
                数字：保留第一个和最后一个数字：只有2个数字时，只保留第一个数字<br />
                分组：保留第一个和最后一个字；只有1个字时，只保留第一个字
              </div>
              <i class="el-icon-info" style="margin-left: 4px; color: #409eff; cursor: pointer" />
            </el-tooltip>
          </template>
          <template v-slot="{ row }">
            <hi-switch v-model="row.privacySwitch" :active-value="'Y'" :inactive-value="'N'"></hi-switch>
          </template>
        </el-table-column>

        <el-table-column label="是否展示" width="100" align="center">
          <template v-slot="{ row }">
            <hi-switch v-model="row.showSwitch" :active-value="'Y'" :inactive-value="'N'"></hi-switch>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" align="center">
          <template v-slot="{ row, $index }">
            <span v-if="row.key === 'd1'" class="disabled-text">(唯一标识不可删除)</span>
            <template v-else>
              <el-button v-if="row.type === 'group'" type="text" @click="handleEditGroup(row, $index)">编辑分组</el-button>
              <el-button type="text" class="delete-btn" @click="handleDelete($index)">删除</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 编辑分组对话框 -->
      <el-dialog
        title="编辑分组"
        :visible.sync="groupDialogVisible"
        width="550px"
        :close-on-click-modal="false"
        append-to-body
        @close="handleCloseGroupDialog"
      >
        <div class="design-tip">批量导入名单时，未创建的分组会自动创建</div>
        <div class="design-group-table">
          <el-table class="w-full" :data="groupList" border>
            <el-table-column label="分组名称">
              <template v-slot="{ row }">
                <el-input v-model="row.name" placeholder="请输入分组名称" size="small"></el-input>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="100" align="center">
              <template v-slot="{ row, $index }">
                <el-button type="text" icon="el-icon-delete" class="delete-btn" @click="handleDeleteGroup(row, $index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="add-group-container">
          <div class="add-button-container">
            <div class="custom-button" :class="{ disabled: groupList.length >= 100 }" @click="handleAddGroup()">
              <i class="el-icon-plus"></i> 添加分组
            </div>
          </div>
          <div class="group-limit-tip" v-if="groupList.length >= 100"><i class="el-icon-warning-outline"></i> 最多只能添加100个分组</div>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="handleCloseGroupDialog">取消</el-button>
          <el-button type="primary" @click="handleSaveGroup">确定</el-button>
        </div>
      </el-dialog>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :disabled="saveDisabled" @click="handleSave">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api'
import { Hi } from '@/libs/common'
import Sortable from 'sortablejs'

export default {
  name: 'DesignList',
  props: {
    wall: {
      type: Object,
      default: () => ({}),
    },
    config: {
      type: Object,
      default: () => ({}),
    },
    importFormList: {
      type: Array,
      default: () => [],
    },
    importList: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      localVisible: false,
      currentEditIndex: -1,
      groupDialogVisible: false,
      showDesignInfo: false,
      sortableInstance: null, // 排序实例
      typeOptions: [
        { label: '姓名', value: 'name' },
        { label: '手机号', value: 'phone' },
        { label: '身份证号', value: 'idcard' },
        { label: '文本', value: 'txt' },
        { label: '数字', value: 'num' },
        { label: '分组', value: 'group' },
      ],
      groupList: [],
      nowImportFormList: [],
    }
  },
  computed: {
    saveDisabled() {
      return !Hi.Object.batchSave(this.importFormList, this.nowImportFormList)
    },
  },
  watch: {
    visible(val) {
      this.localVisible = val
    },
  },
  methods: {
    async handleDragEnd(evt) {
      const { oldIndex, newIndex } = evt
      if (oldIndex === newIndex) return
      const targetRow = this.nowImportFormList.splice(oldIndex, 1)[0]
      this.nowImportFormList.splice(newIndex, 0, targetRow)
      this.nowImportFormList.forEach((item, index) => {
        item.sort = index
      })
    },
    // 初始化分组排序功能
    initGroupSortable() {
      this.$nextTick(() => {
        if (this.$refs.tableRef) {
          const el = this.$refs.tableRef.$el.querySelector('.el-table__body-wrapper > table > tbody')
          if (el) {
            new Sortable(el, {
              handle: '.drag-handle',
              ghostClass: 'sortable-ghost',
              onEnd: this.handleDragEnd,
            })
          }
        }
      })
    },
    handleOpen() {
      this.initGroupSortable()
      this.nowImportFormList = Hi.Object.copy(this.importFormList)
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    async handleSave() {
      // 至少保留一个展示
      const showCount = this.nowImportFormList.filter((item) => item.showSwitch === 'Y').length
      if (showCount === 0) {
        this.$notify.warning({ title: '警告', message: '至少保留一个展示项' })
        return
      }

      const _update = Hi.Object.batchSave(this.importFormList, this.nowImportFormList, {
        ignoreKey: ['privacySwitch', 'showSwitch', 'sort'],
      })
      // 检查是否有名单数据需要清除
      if (this.importList.length > 0 && _update) {
        try {
          // 弹出确认框询问是否清除数据
          const confirmResult = await this.$confirm('修改名单设计将清除现有名单数据，是否继续？', '提示', {
            confirmButtonText: '继续',
            cancelButtonText: '取消',
            type: 'warning',
          }).catch(() => 'cancel')

          // 用户取消操作
          if (confirmResult === 'cancel') {
            return
          }

          // 清除名单数据
          await api.listlotteryv3import.cleardata({
            where: { wallId: this.wall.id },
          })

          this.$notify.success({
            title: '成功',
            message: '名单数据已清除',
          })
        } catch (error) {
          this.$notify.error({
            title: '错误',
            message: '清除名单数据失败',
          })
          return
        }
      }

      const batch = Hi.Object.batchSave(this.importFormList, this.nowImportFormList)
      if (batch) {
        batch.forEach((item) => {
          switch (item.type) {
            case 'add':
              item.data.forEach((dataItem) => {
                // @ts-ignore
                dataItem.wallId = this.wall.id
              })
              break
            case 'del':
              item.data = {
                // @ts-ignore
                wallId: this.wall.id,
                idList: item.data,
              }
              break
            case 'update':
              // @ts-ignore
              item.data.where.wallId = this.wall.id
              break
          }
        })
        try {
          await api.listlotteryv3importform.batch(batch)
          this.$notify.success({ title: '成功', message: '名单设计保存成功' })
          this.$emit('update:visible', false)
          this.$emit('refresh')
        } catch (error) {
          this.$notify.error({ title: '错误', message: error.msg || '保存失败，请重试' })
        }
      } else {
        this.$emit('update:visible', false)
      }
    },
    handleDelete(index) {
      this.nowImportFormList.splice(index, 1)
    },
    handleEditGroup(row, index) {
      this.currentEditIndex = index
      const data = JSON.parse(row.value || '[]')
      this.groupList = data.map((item) => ({ name: item }))
      this.groupDialogVisible = true

      // 等待对话框打开后初始化排序功能
      this.$nextTick(() => {
        this.initGroupSortable()
      })
    },
    handleAddGroup() {
      if (this.groupList.length >= 100) {
        this.$notify.warning({ title: '警告', message: '最多只能添加100个分组' })
        return
      }
      this.groupList.push({ name: '' })
    },
    handleCloseGroupDialog() {
      this.groupDialogVisible = false
    },
    handleDeleteGroup(row, index) {
      this.groupList.splice(index, 1)
    },
    handleSaveGroup() {
      // 校验
      if (this.groupList.length === 0) {
        this.$notify.warning({ title: '警告', message: '请至少添加一个分组' })
        return
      }
      if (this.groupList.some((item) => !item.name)) {
        this.$notify.warning({ title: '警告', message: '请输入分组名称' })
        return
      }
      const group = this.groupList.map((item) => item.name)
      this.nowImportFormList[this.currentEditIndex].value = JSON.stringify(group)
      this.groupDialogVisible = false
    },
    handleAddInfo() {
      if (this.nowImportFormList.length >= 3) {
        this.$notify.warning({ title: '警告', message: '最多只能添加3个信息类型' })
        return
      }

      // 查找未使用的类型
      const usedTypes = this.nowImportFormList.map((item) => item.type)
      const availableType = this.typeOptions.find((option) => !usedTypes.includes(option.value))
      // 查询未使用的key
      const keys = ['d1', 'd2', 'd3']
      const usedKeys = this.nowImportFormList.map((item) => item.key)
      const availableKey = keys.find((key) => !usedKeys.includes(key))

      if (availableType) {
        this.nowImportFormList.push({
          key: availableKey,
          name: availableType.label,
          privacySwitch: 'N',
          showSwitch: 'N',
          sort: this.nowImportFormList.length,
          type: availableType.value,
        })
      } else {
        this.$notify.error({ title: '警告', message: '已经没有可添加的信息类型了' })
      }
    },
  },
}
</script>

<style scoped lang="scss">
.design-tip {
  color: #999;
  font-size: 14px;
}

.design-list-box {
  .disabled-text {
    color: #999;
    font-size: 12px;
  }

  .delete-btn {
    color: #f56c6c;
  }

  .add-info {
    color: #409eff;
    cursor: pointer;
    padding: 15px 10px;
    margin-top: 10px;
    border-top: 1px solid #dfe6ec;

    i {
      margin-right: 5px;
    }
  }
}

.design-group-table {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 15px;
}

.add-group-container {
  margin-top: 15px;
  display: flex;
  align-items: center;
  width: 100%;
}

.add-button-container {
  flex: 1;
}

.custom-button {
  width: 100%;
  height: 36px;
  line-height: 36px;
  text-align: center;
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: #fff;
    color: #409eff;
  }

  &.disabled {
    color: #c0c4cc;
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    cursor: not-allowed;
  }

  i {
    margin-right: 5px;
  }
}

.group-limit-tip {
  margin-left: 10px;
  color: #e6a23c;
  font-size: 12px;

  i {
    margin-right: 4px;
  }
}

.drag-handle {
  cursor: move;
  color: #909399;

  i {
    font-size: 18px;
  }
}

.sortable-ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
</style>
