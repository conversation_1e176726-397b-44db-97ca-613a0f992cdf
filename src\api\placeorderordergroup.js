import api from './api'
export default {
  //订货会-订单分组
  add: postData => api.fetchBaseData('/pro/hxc/proplaceordergroup/add.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proplaceordergroup/update.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/proplaceordergroup/page.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/proplaceorderordergroup/batch.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proplaceorderordergroup/list.htm', postData),
}

