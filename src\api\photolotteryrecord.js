import api from './api'
export default {
  list: postData => api.fetchBaseData('/pro/hxc/prophotolotteryrecord/list.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/prophotolotteryrecord/page.htm', postData),
  clearData: postData => api.fetchBaseData('/pro/hxc/prophotolotteryrecord/clearData.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prophotolotteryrecord/update.htm', postData),
}
