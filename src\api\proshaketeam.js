import api from './api'
export default {
  read: postData => api.fetchBaseData('/pro/hxc/proshaketeam/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proshaketeam/update.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proshaketeam/list.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/proshaketeam/batch.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/proshaketeam/add.htm', postData),
}
