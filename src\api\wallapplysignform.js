import api from './api'
export default {
  list: postData => api.fetchBaseData('/pro/hxc/prowallapplysignform/list.htm', postData),
  insert: postData => api.fetchBaseData('/pro/hxc/prowallapplysignform/insert.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prowallapplysignform/update.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/prowallapplysignform/batch.htm', postData),
}
