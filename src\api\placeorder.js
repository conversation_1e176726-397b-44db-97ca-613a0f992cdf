import api from './api'
export default {
  //订货会
  read: postData => api.fetchBaseData('/pro/hxc/proplaceorder/read.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/proplaceorder/add.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proplaceorder/update.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proplaceorder/list.htm', postData),
  sort: postData => api.fetchBaseData('/pro/hxc/proplaceorder/sort.htm', postData),
}
