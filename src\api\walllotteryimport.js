import api from './api';

export default {
  add: (postData) => api.fetchBaseData('/pro/hxc/prowalllotteryimport/add.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/prowalllotteryimport/batch.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/prowalllotteryimport/delete.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/prowalllotteryimport/list.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/prowalllotteryimport/page.htm', postData),
  countList: (postData) => api.fetchBaseData('/pro/hxc/prowalllotteryimport/countList.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/prowalllotteryimport/read.htm', postData),
  clearData: (postData) => api.fetchBaseData('/pro/hxc/prowalllotteryimport/cleardata.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/prowalllotteryimport/update.htm', postData),
};
