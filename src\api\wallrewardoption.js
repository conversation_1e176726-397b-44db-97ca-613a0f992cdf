import api from './api'
export default {
  infoPage: postData => api.fetchBaseData('/pro/hxc/prowallrewardoption/infoPage.htm', postData),
  infoList: postData => api.fetchBaseData('/pro/hxc/prowallrewardoption/infoList.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prowallrewardoption/list.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/prowallrewardoption/batch.htm', postData),
}
