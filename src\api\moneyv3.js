import api from './api'
export default {
  page: postData => api.fetchBaseData('/pro/hxc/promoneyv3/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/promoneyv3/list.htm', postData),
  insert: postData => api.fetchBaseData('/pro/hxc/promoneyv3/insert.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/promoneyv3/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/promoneyv3/update.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/promoneyv3/delete.htm', postData),
}
