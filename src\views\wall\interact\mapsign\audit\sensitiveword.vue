<template>
  <div class="sensitive-box">
    <div class="content">
      <div class="add-sensitive">
        <el-button type="primary" icon="el-icon-arrow-left" @click="$emit('back')">返回</el-button>
        <div class="flex">
          <div class="search">
            <el-input placeholder="搜索敏感词" v-model="sensitiveKey">
              <el-button type="primary" plain slot="append" icon="el-icon-search" @click="seekSensitive"></el-button>
            </el-input>
          </div>
          <el-button type="primary" icon="plus" @click="openAddSensitive">添加敏感词</el-button>
        </div>
      </div>
      <p class="senstive-top">
        <el-button type="danger" @click="batchDel" :disabled="selectAll || delIdList.length === 0">删除</el-button>
      </p>
      <ul>
        <li class="tit">
          <p class="choose">
            <el-checkbox v-model="delAll" :disabled="selectAll" @change="checkAllChange($event)"> </el-checkbox>
            敏感词
          </p>
          <p class="operate">
            <span>操作</span>
          </p>
        </li>
        <template v-if="sensitiveListObj.dataList && sensitiveListObj.dataList.length > 0">
          <li v-for="(item, index) of sensitiveListObj.dataList" :key="index">
            <p>
              <el-checkbox v-model="item.checkItem" @change="changeItem($event, item.id)"></el-checkbox>
              {{ item.word }}
            </p>
            <p class="operate del">
              <span @click="deleteSensitive(item.id)">删除</span>
            </p>
          </li>
        </template>
        <li v-else class="flex flex-a-c flex-j-c">暂无敏感词</li>
      </ul>
      <div class="page">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="sensitiveListObj.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="sensitiveListObj.total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog title="添加敏感词" :visible.sync="dialogVisible" width="404px">
      <p class="sensitive-hint">每行一个敏感词，或者以逗号隔开</p>
      <div class="sensitive_add">
        <p>敏感词</p>
        <div>
          <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="sensitiveTextarea"> </el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="cancleSensitive">取 消</el-button>
        <el-button type="primary" :loading="saveLoad" @click="confirmSensitive">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import api from '@/api/wallwordfilter'
import { Hi } from '@/libs/common'
export default {
  data() {
    return {
      sensitiveKey: '', // 敏感词搜索关键词
      delAll: false, // 删除全部
      sensitiveTextarea: '',
      sessitiveAddShow: false, // 添加敏感词
      sensitivePage: {
        pageIndex: 1,
        pageSize: 10,
      },
      sensitiveListObj: {},
      words: [],
      currentPage: 1,
      delIdList: [],
      selectAll: false,
      selectCheckBox: false,
      dialogVisible: false, // 添加敏感词弹窗
      saveLoad: false,
    }
  },
  watch: {
    sensitiveKey: function (val) {
      if (!val) {
        this.getSensitivePage.call(this)
      }
    },
    delIdList: function (val) {
      val.length === this.sensitiveListObj.dataList.length ? (this.delAll = true) : (this.delAll = false)
    },
  },
  async created() {
    await this.getSensitivePage.call(this)
  },
  methods: {
    // 敏感词分页
    async getSensitivePage() {
      // wordfliter  新增
      let sensitiveList = await api.page(this.sensitivePage).catch((err) => this.$message.error(err.msg))
      this.sensitiveListObj = sensitiveList
      let len = this.sensitiveListObj.dataList.length
      if (len > 0) {
        // 添加一个属性  记录 checkbox 状态
        for (var i = 0; i < len; i++) {
          this.$set(this.sensitiveListObj.dataList[i], 'checkItem', false)
        }
        this.selectAll = false
      } else {
        this.selectAll = true
      }
    },
    // 添加敏感词窗口打开事件
    openAddSensitive() {
      this.dialogVisible = true
      this.sensitiveTextarea = ''
    },
    // 敏感词添加
    async addSensitive() {
      // wordfilter 新增
      try {
        await api.addBatch({
          words: this.words,
        })
        this.$notify.success({ title: '成功', message: '敏感词添加成功' })
      } catch (e) {
        console.log(e)
        this.$notify.error({
          title: '错误',
          message: e ? e.msg || '敏感词添加失败，请稍后重试！' : '敏感词添加失败，请稍后重试！',
        })
      }
    },
    // 敏感词 确认添加 导入
    async confirmSensitive() {
      let data = this.sensitiveTextarea.replace(/，|\n/gi, ',')
      let wordArr = data.split(',')
      let temp = {}
      wordArr.forEach(function (i, c) {
        var word = i.slice(0, 10)
        if (word) {
          wordArr[c] = word
          temp[word] = ''
        }
      })
      // 数据排重
      this.words = Object.keys(temp)
      if (!this.words.length) {
        this.$notify.error({ title: '错误', message: '敏感词不能为空' })
        return
      }
      this.saveLoad = true
      try {
        await this.addSensitive.call(this)
        this.dialogVisible = false
        await this.getSensitivePage.call(this)
        this.delAll = false
      } catch (e) {
        console.log(e)
      }
      this.saveLoad = false
    },
    // 敏感词 添加取消
    cancleSensitive() {
      this.sensitiveTextarea = ''
      this.dialogVisible = false
    },
    // 敏感词 全选
    checkAllChange(e) {
      let len = this.sensitiveListObj.dataList.length
      if (e) {
        this.delIdList = []
        for (var i = 0; i < len; i++) {
          this.$set(this.sensitiveListObj.dataList[i], 'checkItem', true)
          this.delIdList.push(this.sensitiveListObj.dataList[i].id)
        }
      } else {
        for (var i = 0; i < len; i++) {
          this.$set(this.sensitiveListObj.dataList[i], 'checkItem', false)
        }
        this.delIdList = []
      }
    },
    // 敏感词删除 单条
    async deleteSensitive(id) {
      // wordfilter 新增
      try {
        await api.delete({ where: { id: id } })
        this.$notify.success({ title: '成功', message: '敏感词删除成功' })
      } catch (e) {
        console.log(e)
        this.$notify.error({
          title: '错误',
          message: e ? e.msg || '删除失败，请稍后重试！' : '删除失败，请稍后重试！',
        })
      }
      await this.getSensitivePage.call(this)
      this.delIdList = []
    },
    // 敏感词批量删除
    async batchDel() {
      // wordfilter 新增
      let len = this.delIdList.length
      if (len > 0) {
        try {
          await api.delete({ where: { idList: this.delIdList } })
          this.$notify.success({ title: '成功', message: '敏感词删除成功' })
        } catch (e) {
          console.log(e)
          this.$notify.error({
            title: '错误',
            message: e ? e.msg || '删除失败，请稍后重试！' : '删除失败，请稍后重试！',
          })
        }
        await this.getSensitivePage.call(this)
        this.delIdList = []
      }
    },
    // 敏感词搜索
    async seekSensitive() {
      // wordfliter  新增
      if (!this.sensitiveKey) {
        await this.getSensitivePage.call(this)
        return
      }
      let filterSensitive = await api
        .page({
          where: {
            likeWord: this.sensitiveKey,
          },
        })
        .catch((err) => this.$message.error(err.msg))
      this.sensitiveListObj = filterSensitive
    },
    // 敏感词单条 选择
    changeItem(e, id) {
      let index = 0
      if (e) {
        this.delIdList.push(id)
      } else {
        let len = this.delIdList.length
        for (var i = 0; i < len; i++) {
          if (id === this.delIdList[i]) {
            index = i
          }
        }
        this.delIdList.splice(index, 1)
      }
      if (this.delIdList.length === this.sensitiveListObj.dataList.length) {
        this.delAll = true
      } else {
        this.delAll = false
      }
    },
    // 页容量变化
    async handleSizeChange(val) {
      this.sensitivePage.pageSize = val
      await this.getSensitivePage.call(this)
      this.$parent.$parent.$refs.auditContent.scrollTop = 0
    },
    // 页码变化
    async handleCurrentChange(val) {
      this.sensitivePage.pageIndex = val
      await this.getSensitivePage.call(this)
      this.$parent.$parent.$refs.auditContent.scrollTop = 0
    },
  },
}
</script>

<style scoped lang="stylus">
.sensitive-box
  padding-top 13px
.add-sensitive
  height 30px
  display flex
  justify-content space-between
  margin 30px 0
  position relative
  margin 18px 0
.content
  padding 0 25px
.add-sensitive .search
  width 146px
  margin-right 12px
.senstive-top
  height 42px
  padding-left 54px
  display flex
  align-items center
  border 1px solid #dfe6ec
  border-bottom 0
  box-sizing border-box
.sensitive-box ul
  border 1px solid #dfe6ec
.sensitive-box ul li
  height 40px
  display flex
  align-items center
  border-top 1px solid #dfe6ec
  box-sizing border-box
.sensitive-box ul li.tit
  border 0
  font-weight 700
  background-color #eef1f6
li p
  line-height 42px
.sensitive-box ul li .el-checkbox
  margin 0 20px 0 10px
.sensitive-box ul li .operate
  flex 1
  text-align right
.sensitive-box ul li .operate span
  margin-right 40px
  cursor pointer
.sensitive-box ul li .del span
  color #ff6161
.sensitive-box .page
  margin-top 10px
  display flex
  justify-content flex-end
.sensitive-hint
  height 30px
  line-height 30px
  text-align center
  background-color #ecf3ff
  margin-bottom 16px
.sensitive-hint span
  color #4886ff
  cursor pointer
  margin-left 10px
.sensitive_add
  display flex
.sensitive_add p
  width 100px
  height 30px
  line-height 30px
  text-align right
  margin-right 13px
.sensitive_add div
  flex 1
  padding-right 48px
.sensitive-box >>> .el-textarea__inner
  resize none
  height 120px
  margin-bottom 15px
.sensitive-box >>> .el-checkbox .el-checkbox__label
  margin-left 38px
.sensitive-box >>> .el-dialog .el-dialog__header
  padding 0 20px
.sensitive-box >>> .el-dialog .el-dialog__body
  padding 0
.add-sensitive .search >>> .el-input-group__append
  text-align center
.add-sensitive .search .el-button
.add-sensitive .search >>> .el-button.el-button--default.el-button--small:hover
  border-color transparent
  color #bfcbd9
  padding 0 7px
</style>
