import api from './api';

export default {
  add: (postData) => api.fetchBaseData('/pro/hxc/prowalllotterytheme/add.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/prowalllotterytheme/batch.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/prowalllotterytheme/delete.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/prowalllotterytheme/list.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/prowalllotterytheme/read.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/prowalllotterytheme/update.htm', postData),
};
