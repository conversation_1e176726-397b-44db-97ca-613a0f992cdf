<template>
  <div class="question-bank-manager">
    <!-- 筛选面板 -->
    <div class="filter-section">
      <div>
        <!-- 题目难度筛选 -->
        <div class="filter-group">
          <span class="filter-label">题目难度：</span>
          <el-tag
            v-for="difficulty in _subjectDifficultyList"
            :key="difficulty.id"
            :type="currentFilters.difficultyId === difficulty.id ? 'primary' : 'info'"
            :effect="currentFilters.difficultyId === difficulty.id ? 'dark' : 'plain'"
            class="filter-tag"
            @click="handleDifficultyFilter(difficulty.id)"
          >
            {{ difficulty.name }}
          </el-tag>
          <el-button v-if="mode === 'manage'" type="primary" size="mini" plain @click="showDifficultyManager"> 管理难度 </el-button>
        </div>

        <!-- 题目分组筛选 -->
        <div class="filter-group">
          <span class="filter-label">题目分组：</span>
          <el-tag
            v-for="group in _subjectGroupList"
            :key="group.id"
            :type="currentFilters.groupId === group.id ? 'primary' : 'info'"
            :effect="currentFilters.groupId === group.id ? 'dark' : 'plain'"
            class="filter-tag"
            @click="handleGroupFilter(group.id)"
          >
            {{ group.name }}
          </el-tag>

          <el-button v-if="mode === 'manage'" type="primary" size="mini" plain @click="showGroupManager"> 管理分组 </el-button>
        </div>

        <!-- 题目类型筛选 -->
        <div class="filter-group">
          <span class="filter-label">题目类型：</span>
          <el-tag
            v-for="type in typeList"
            :key="type.key"
            :type="currentFilters.type === type.key ? 'primary' : 'info'"
            :effect="currentFilters.type === type.key ? 'dark' : 'plain'"
            class="filter-tag"
            @click="handleTypeFilter(type.key)"
          >
            {{ type.name }}
          </el-tag>
        </div>
      </div>
      <el-button v-if="mode === 'select'" type="primary" @click="handleManageQuestion">管理题库</el-button>
    </div>

    <div class="question-list-container">
      <hi-question-list ref="questionList" :filter-conditions="currentFilters" :mode="mode" />
    </div>

    <!-- 分组管理弹窗 -->
    <hi-group-manager :visible="managerVisible" :type="managerType" @close="managerVisible = false" />
  </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex'
import HiQuestionList from './QuestionList.vue'
import HiGroupManager from './GroupManager.vue'

export default {
  name: 'QuestionBankManager',
  components: {
    HiQuestionList,
    HiGroupManager,
  },
  props: {
    // 管理模式
    mode: {
      type: String,
      default: 'manage', // manage: 管理模式, select: 选择模式
      validator: (value) => ['manage', 'select'].includes(value),
    },
  },
  data() {
    return {
      typeList: [
        {
          key: '',
          name: '全部',
        },
        {
          key: 'RADIO',
          name: '单选',
        },
        {
          key: 'CHECKBOX',
          name: '多选',
        },
      ],
      currentFilters: {
        difficultyId: '', // 难度筛选
        groupId: '', // 分组筛选
        type: '', // 类型筛选
      },
      // 分组管理
      managerType: 'group', // group: 分组管理, difficulty: 难度管理
      managerVisible: false,
    }
  },
  computed: {
    ...mapGetters({
      wallFlagObj: 'wall/getWallFlagObj',
    }),
    ...mapState('questionBank', ['subjectDifficultyList', 'subjectGroupList']),
    wall() {
      return this.wallFlagObj[this.$route.query.wallFlag]
    },
    _subjectDifficultyList() {
      return [{ id: '', name: '全部' }, ...this.subjectDifficultyList]
    },
    _subjectGroupList() {
      return [{ id: '', name: '全部' }, ...this.subjectGroupList]
    },
    selectedQuestions() {
      return this.$refs.questionList.selectedQuestions
    },
  },

  methods: {
    ...mapActions('questionBank', ['initQuestionBank']),

    // 显示难度管理
    showDifficultyManager() {
      this.managerType = 'difficulty'
      this.managerVisible = true
    },

    // 显示分组管理
    showGroupManager() {
      this.managerType = 'group'
      this.managerVisible = true
    },

    handleDifficultyFilter(id) {
      this.currentFilters.difficultyId = id
    },

    handleGroupFilter(id) {
      this.currentFilters.groupId = id
    },

    handleTypeFilter(key) {
      this.currentFilters.type = key
    },

    // 初始化管理器
    async initManager() {
      try {
        if (!this.wall) {
          console.error('Wall 对象未找到，wallFlag:', this.$route.query.wallFlag)
          this.$notify.error({
            title: '错误',
            message: '未找到对应的活动信息',
          })
          return
        }
        this.currentFilters = {
          difficultyId: '',
          groupId: '',
          type: '',
        }
        await this.initQuestionBank(this.wall.id)
      } catch (error) {
        this.$notify.error({
          title: '错误',
          message: '初始化题库管理器失败',
        })
      }
    },

    handleManageQuestion() {
      window.open(this.$router.resolve({ name: 'wall-interact-answerracev3', query: { ...this.$route.query, mode: 'manage' } }).href, '_blank')
    },
  },
  mounted() {
    this.initManager()
  },
}
</script>

<style scoped>
.question-bank-manager {
  padding: 20px;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.filter-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-label {
  font-weight: 600;
  color: #303133;
  min-width: 80px;
  font-size: 14px;
}

.filter-tag {
  cursor: pointer;
}
</style>
