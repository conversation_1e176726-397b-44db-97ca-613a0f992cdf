import api from './api';
export default {
  read: (v) => api.fetchBaseData('/pro/hxc/prosignbookrandom/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/prosignbookrandom/update.htm', v),
  add: (v) => api.fetchBaseData('/pro/hxc/prosignbookrandom/add.htm', v),
  page: (v) => api.fetchBaseData('/pro/hxc/prosignbookrandom/page.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/prosignbookrandom/list.htm', v),
  delete: (postData) => api.fetchBaseData('/pro/hxc/prosignbookrandom/delete.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/prosignbookrandom/batch.htm', postData),
};
