<template>
  <div class="wall-data-answer-box">
    <div class="padding-con">
      <el-tabs v-model="tabState" type="card" @tab-click="handleTabClick">
        <el-tab-pane label="排名数据" name="RANK"> </el-tab-pane>
        <el-tab-pane label="答题数据" name="ANSWER"> </el-tab-pane>
      </el-tabs>
      <el-row>
        <label>活动标题</label>
        <el-select class="select-width" v-model="activityId" placeholder="请选择活动" @change="handleActivityChange">
          <el-option v-for="item in activityOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>

        <template v-if="isAnswer">
          <label class="mar-l-15">选择题目</label>
          <el-select class="select-width" v-model="subjectId" placeholder="请选择题目" @change="handleSubjectChange">
            <el-option v-for="item in subjects" :key="item.id" :label="item.subjectContent.content" :value="item.id"></el-option>
          </el-select>
        </template>

        <template v-if="isRank">
          <label class="mar-l-15"> {{ isTeam ? '参赛队伍' : '参赛人' }} </label>
          <el-input class="search-btn" :placeholder="isTeam ? '队伍名称' : '昵称或姓名'" v-model.trim="nickName" @keyup.enter="handleNickNameSearch">
          </el-input>
        </template>

        <el-button class="mar-l-15" v-if="isRank" type="primary" plain :loading="loading" @click="handleNickNameSearch">查询</el-button>
        <el-button class="mar-l-15" type="primary" plain @click="exportTable" :loading="exportLoad" :disabled="!canExport">数据导出</el-button>
      </el-row>

      <div class="table" v-loading="loading" :key="tableKey">
        <el-table :data="rankingData" tooltip-effect="light" highlight-current-row ref="table">
          <!-- 团队 -->
          <el-table-column v-if="isTeam" prop="teamName" label="队伍名称" :formatter="formatTeamName" />
          <!-- 个人 -->
          <el-table-column v-else prop="wxUserId" label="参赛人" :formatter="(row) => getNickName(row.wxUserId)">
            <template v-slot="{ row }">
              <hi-wxuser-name :user-id="row.wxUserId"></hi-wxuser-name>
            </template>
          </el-table-column>

          <template v-if="isTeam">
            <el-table-column v-if="isRank" prop="ranking" label="团队排名" :formatter="formatTeamRanking" />
            <el-table-column prop="score" label="分数" />
            <el-table-column prop="accuracy" label="正确率" :formatter="(row) => (row.accuracy * 100).toFixed(1) + '%'" />
            <el-table-column v-if="isAnswer" label="操作" width="100" column-key="teamId">
              <template v-slot="{ row }">
                <el-button type="text" @click="handleTeamDetail(row)">查看</el-button>
              </template>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column v-if="isAnswer" prop="subjectContent" label="是否正确" :formatter="formatSubjectCorrect" />
            <el-table-column v-if="isAnswer" prop="subjectId" label="回答内容" :formatter="formatSubjectContent" />
            <el-table-column v-if="isRank" prop="rightCount" label="答对题数" />
            <el-table-column v-if="isRank" prop="ranking" label="个人排名" :formatter="formatRankingWithIndex" />
            <el-table-column prop="totalScore" label="分数" />
            <el-table-column prop="totalTime" label="用时" :formatter="formatCostTime" />
            <el-table-column v-if="isAnswer" prop="createDate" label="提交时间" width="180" :formatter="formatDate" />
          </template>
        </el-table>
      </div>
    </div>

    <el-dialog :visible.sync="teamDetailVisible" title="队伍详情" width="800px" :close-on-click-modal="false">
      <div class="mrg-b-10">题目名称: {{ teamDetailName }}</div>
      <el-table :data="teamDetailData" tooltip-effect="light" highlight-current-row ref="table">
        <el-table-column prop="wxUserId" label="参赛人" :formatter="(row) => getNickName(row.wxUserId)">
          <template v-slot="{ row }">
            <hi-wxuser-name :user-id="row.wxUserId"></hi-wxuser-name>
          </template>
        </el-table-column>
        <el-table-column prop="subjectId" label="回答" :formatter="formatSubjectContent" />
        <el-table-column prop="answerResult" label="是否正确" :formatter="formatSubjectCorrect" />
        <el-table-column prop="score" label="得分" />
        <el-table-column prop="useTime" label="用时" :formatter="formatUseTime" />
        <el-table-column prop="createDate" label="提交时间" width="180" />
      </el-table>
    </el-dialog>
  </div>
</template>
<script>
import api from '@/api'
import HiWxuserName from '../common/wxuserName.vue'
import { exportMixin } from '@/libs/mixins'
import { relationUserMixin } from '@/views/wall/common/mixin'
import { prizeStateMixin, relationClaim } from '../common/mixin'
export default {
  name: 'WallDataAnswerracev3',
  mixins: [relationUserMixin, prizeStateMixin, exportMixin, relationClaim],
  inject: ['wall', 'signConfig', 'signForm'],
  components: { HiWxuserName },
  data() {
    return {
      loading: false,
      exportLoad: false,
      // 活动下拉选项列表
      activityOptions: [],
      // 筛选条件
      nickName: '', // 参赛人昵称或姓名
      activityId: '', // 活动ID
      // 活动列表对象，用于快速查找活动信息
      activitysObj: {},
      // 用时数据对象，key为用户ID，value为用时毫秒数
      costTime: {},
      // 当前标签页状态
      tabState: 'RANK',
      subjectId: '',
      subjects: [],
      rankingData: [],
      recordMap: {},
      // 数据加载状态
      dataLoaded: false,
      teamMap: {},

      teamDetailVisible: false,
      teamDetailData: [],
      tableKey: 0,
    }
  },
  computed: {
    // 判断当前活动是否为团队模式
    isTeam() {
      const activity = this.activitysObj[this.activityId]
      return activity && activity.joinType === 'TEAM'
    },
    isAnswer() {
      return this.tabState === 'ANSWER'
    },
    isRank() {
      return this.tabState === 'RANK'
    },
    // 是否有可用的活动
    hasActivities() {
      return this.activityOptions && this.activityOptions.length > 0
    },
    // 是否有可用的题目
    hasSubjects() {
      return this.subjects && this.subjects.length > 0
    },
    // 当前选中的活动信息
    currentActivity() {
      return this.activitysObj[this.activityId] || null
    },
    // 队伍详情题目名称
    teamDetailName() {
      return this.subjects.find((item) => item.id === this.subjectId)?.subjectContent.content || '-'
    },
    // 是否可以导出数据
    canExport() {
      if (!this.hasActivities || !this.activityId) {
        return false
      }

      // 排名数据页面：需要有排名数据
      if (this.isRank) {
        return this.dataLoaded && this.rankingData.length > 0
      }

      // 答题数据页面：需要有题目数据
      if (this.isAnswer) {
        return this.hasSubjects
      }

      return false
    },
  },
  watch: {
    tabState: {
      handler(newVal) {
        if (newVal === 'ANSWER') {
          this.rankingData = []
        }
      },
    },
  },
  methods: {
    // 提交时间
    formatDate(row) {
      const record = this.recordMap[row.wxUserId]
      return record?.createDate || '-'
    },

    // 队伍名称
    formatTeamName(row) {
      if (this.isTeam) {
        return this.teamMap[row.teamId]?.teamName || '-'
      }
      return '-'
    },

    // 个人排名
    formatRankingWithIndex(row) {
      return row.out ? '淘汰' : row.ranking
    },

    // 团队排名
    formatTeamRanking(row) {
      return row.ranking ? row.ranking : '淘汰'
    },

    // 回答内容
    formatSubjectContent(row) {
      try {
        const record = this.recordMap[row.wxUserId]
        if (!record || !record.option) return '-'

        const optionList = JSON.parse(record.option || '[]')
        if (optionList && optionList.length) {
          return optionList.map((item) => String.fromCharCode(65 + item.index)).join(',')
        }
        return '-'
      } catch (error) {
        console.error('格式化答题内容失败:', error)
        return '-'
      }
    },

    // 是否正确
    formatSubjectCorrect(row) {
      try {
        const record = this.recordMap[row.wxUserId]
        if (!record) return '-'
        return record.answerResult === 'Y' ? '正确' : '错误'
      } catch (error) {
        console.error('格式化答题结果失败:', error)
        return '-'
      }
    },

    // 用时
    formatUseTime(row) {
      const time = row.useTime
      if (!time || time <= 0) return '-'
      return (time / 1000).toFixed(2) + 's'
    },

    // 总用时
    formatCostTime(row) {
      try {
        const time = row.totalTime
        if (!time || time <= 0) return '-'
        return (time / 1000).toFixed(2) + 's'
      } catch (error) {
        console.error('格式化用时失败:', error)
        return '-'
      }
    },

    // 构建查询条件
    async buildQueryWhere() {
      try {
        const pageWhere = { wallId: this.wall.id }

        if (this.activityId) {
          pageWhere.answerracev3Id = this.activityId
        }

        if (this.subjectId) {
          pageWhere.subjectId = this.subjectId
        }

        // 如果输入了昵称，查询对应的用户ID列表
        if (this.nickName?.trim()) {
          const trimmedNickName = this.nickName.trim()

          if (this.isTeam) {
            // 团队模式：根据队伍名称搜索
            const teamList = await api.answerracev3team.list({
              where: {
                wallId: this.wall.id,
                answerracev3Id: this.activityId,
                likeTeamName: trimmedNickName,
              },
            })

            const teamIdList = teamList.map((item) => item.id)
            if (teamIdList.length) {
              pageWhere.teamIdList = teamIdList
            } else {
              this.$notify.warning({
                title: '提示',
                message: '未找到匹配的队伍，请检查队伍名称是否正确',
              })
              return null
            }
          } else {
            // 个人模式：根据用户昵称或姓名搜索
            const wxUserIdList = await this.fetchWxuserIdList(trimmedNickName)
            if (wxUserIdList?.length) {
              pageWhere.wxUserIdList = wxUserIdList
            } else {
              this.$notify.warning({
                title: '提示',
                message: '未找到匹配的用户，请检查昵称或姓名是否正确',
              })
              return null
            }
          }
        }

        return pageWhere
      } catch (error) {
        console.error('构建查询条件失败:', error)
        this.$notify.error({
          title: '错误',
          message: '查询条件构建失败，请重试',
        })
        return null
      }
    },

    // 处理数据列表的关联信息
    async processListData(dataList) {
      await this.relationUser(dataList)
      // 关联兑奖和邮寄方式信息
      await this.relationClaimType(dataList)
      await this.relationPostinfoObj(dataList)
      let columns = []
      //如果开启强制签到，导出签到信息
      await this.appendExportSignUser(columns)
      return columns
    },

    // 获取活动列表信息
    async fetchActivities() {
      const data = await api.answerracev3.list({
        where: { wallId: this.wall.id },
        sort: { id: 'asc' },
      })

      if (!data.length) return

      this.activityOptions = []
      data.forEach((item) => {
        this.$set(this.activitysObj, item.id, item)
        if (item.state !== 'DELETE') {
          this.activityOptions.push({
            value: item.id,
            label: item.title,
          })
        }
      })

      // 设置默认选中的活动ID
      this.activityId = parseInt(this.$route.query.id) || (this.activityOptions.length > 0 ? this.activityOptions[0].value : '')

      if (this.isTeam) {
        await this.fetchTeamList()
      }
    },

    //  题目列表
    async fetchSubjects() {
      const data = await api.answerracev3subject.list({
        where: {
          wallId: this.wall.id,
          answerracev3Id: this.activityId,
        },
        sort: { id: 'asc' },
      })
      this.subjects = data.map((item) => ({
        ...item,
        subjectContent: JSON.parse(item.subjectContent),
      }))
    },

    // 获取排名数据
    async fetchRanking() {
      if (!this.activityId) {
        this.rankingData = []
        return
      }

      try {
        this.loading = true
        const where = await this.buildQueryWhere()

        if (!where) {
          this.rankingData = []
          return
        }

        const data = await api.answerracev3record.ranking(where)

        let arrary = []
        if (this.isTeam) {
          arrary = data
        } else {
          const { dataList, outList } = data
          arrary = [...dataList, ...outList.map((item) => ({ ...item, out: true }))]
        }

        const wxUserIdList = arrary.filter((item) => item && item.wxUserId)
        if (wxUserIdList.length) {
          await this.processListData(wxUserIdList)
        }
        this.rankingData = arrary
        this.dataLoaded = true
      } catch (error) {
        console.error('获取排名数据失败:', error)
        this.$notify.error({
          title: '错误',
          message: error.message || '获取排名数据失败，请重试',
        })
        this.rankingData = []
      } finally {
        this.loading = false
      }
    },

    // 答题数据
    async fetchRecordList() {
      if (!this.activityId || !this.subjectId) {
        this.recordMap = {}
        return
      }

      try {
        const where = await this.buildQueryWhere()

        if (!where) {
          this.recordMap = {}
          return
        }

        const data = await api.answerracev3record.list({ where })

        if (!data || !Array.isArray(data)) {
          this.recordMap = {}
          return
        }

        // 清空之前的记录
        this.recordMap = {}

        data.forEach((item) => {
          if (item && item.wxUserId) {
            this.$set(this.recordMap, item.wxUserId, item)
          }
        })
      } catch (error) {
        console.error('获取答题记录失败:', error)
        this.$notify.error({
          title: '错误',
          message: error.message || '获取答题记录失败，请重试',
        })
        this.recordMap = {}
      }
    },

    async fetchTeamList() {
      const data = await api.answerracev3team.list({
        where: {
          wallId: this.wall.id,
          answerracev3Id: this.activityId,
        },
      })
      this.teamMap = data.reduce((acc, item) => {
        acc[item.id] = item
        return acc
      }, {})
    },

    // 活动切换
    async handleActivityChange() {
      try {
        // 重置相关数据
        this.subjectId = ''
        this.subjects = []
        this.recordMap = {}
        this.rankingData = []
        this.dataLoaded = false

        if (!this.activityId) {
          return
        }

        // 如果是答题数据，则获取题目
        if (this.isAnswer) {
          await this.fetchSubjects()
        } else {
          await this.fetchTeamList()
          await this.fetchRanking()
        }
      } catch (error) {
        console.error('活动切换失败:', error)
        this.$notify.error({
          title: '错误',
          message: '切换活动失败，请重试',
        })
      }
    },

    // 题目切换
    async handleSubjectChange() {
      try {
        this.recordMap = {}
        if (this.subjectId) {
          if (this.isTeam) {
            await this.fetchTeamList()
          }
          await Promise.all([this.fetchRanking(), this.fetchRecordList()])
        }
      } catch (error) {
        console.error('题目切换失败:', error)
        this.$notify.error({
          title: '错误',
          message: '切换题目失败，请重试',
        })
      }
    },

    // 昵称搜索
    async handleNickNameSearch() {
      try {
        await this.fetchRanking()
        // 如果当前在答题数据页且选择了题目，也需要重新获取答题记录
        if (this.isAnswer && this.subjectId) {
          await this.fetchRecordList()
        }
      } catch (error) {
        console.error('搜索失败:', error)
        this.$notify.error({
          title: '错误',
          message: '搜索失败，请重试',
        })
      }
    },

    // 标签页切换
    async handleTabClick(tab, event) {
      this.subjectId = ''
      this.tableKey++
      // 答题数据页签
      if (this.isAnswer) {
        // 获取答题记录
        await this.fetchRecordList()
        // 获取题目
        await this.fetchSubjects()
        return
      }
      // 排名数据页签
      await this.fetchRanking()
    },

    // 查看队伍
    async handleTeamDetail(row) {
      const regeditData = await api.answerracev3regedit.list({
        where: {
          wallId: this.wall.id,
          answerracev3Id: this.activityId,
          teamId: row.teamId,
        },
      })

      // 如果当前题目没有参赛人，则不显示队伍详情
      if (!regeditData.map((item) => item.wxUserId).length) {
        this.teamDetailData = []
        this.teamDetailVisible = true
        return
      }

      const data = await api.answerracev3record.list({
        where: {
          wallId: this.wall.id,
          answerracev3Id: this.activityId,
          subjectId: this.subjectId,
          wxUserIdList: regeditData.map((item) => item.wxUserId),
        },
      })

      const wxUserIdList = data.filter((item) => item && item.wxUserId)
      if (wxUserIdList.length) {
        await this.processListData(wxUserIdList)
      }

      this.teamDetailData = data
      this.teamDetailVisible = true
    },

    async exportData() {
      try {
        this.exportLoad = true

        if (!this.activityId) {
          this.$notify.warning({
            title: '提示',
            message: '请先选择要导出的活动',
          })
          return null
        }

        // 根据当前 tab 页面调用不同的导出方法
        if (this.isRank) {
          return await this.exportRankData()
        } else {
          return await this.exportAnswerData()
        }
      } catch (error) {
        console.error('导出数据失败:', error)
        this.$notify.error({
          title: '错误',
          message: error.message || error.msg || '导出失败，请重试',
        })
        return null
      } finally {
        this.exportLoad = false
      }
    },

    // 导出排名数据
    async exportRankData() {
      try {
        // 根据团队模式设置不同的导出文件名
        const fileName = this.isTeam ? '答题团队排名数据' : '答题排名数据'

        // 确保有数据可导出
        if (!this.rankingData || this.rankingData.length === 0) {
          this.$notify.warning({
            title: '提示',
            message: '没有可导出的排名数据',
          })
          return null
        }

        let columns = []

        if (!this.isTeam) {
          let wxidlist = this.rankingData.map((item) => item.wxUserId)
          if (wxidlist.length) {
            columns = await this.processListData(wxidlist)
          }
        }

        return {
          name: fileName,
          sheets: [
            {
              name: '排名数据',
              ref: this.$refs.table,
              columns,
            },
          ],
        }
      } catch (error) {
        console.error('导出排名数据失败:', error)
        this.$notify.error({
          title: '错误',
          message: error.message || '导出排名数据失败，请重试',
        })
        return null
      }
    },

    // 导出答题数据（所有题目的答题数据）
    async exportAnswerData() {
      if (this.isTeam) {
        return await this.exportTeamAnswerData()
      } else {
        return await this.exportPersonalAnswerData()
      }
    },

    // 导出团队答题数据
    async exportTeamAnswerData() {
      try {
        // 并行获取所有必要数据
        const [subjectList, regeditData, recordData] = await Promise.all([
          api.answerracev3subject.list({
            where: {
              wallId: this.wall.id,
              answerracev3Id: this.activityId,
            },
            sort: { id: 'asc' },
          }),
          api.answerracev3regedit.list({
            where: {
              wallId: this.wall.id,
              answerracev3Id: this.activityId,
            },
          }),
          api.answerracev3record.list({
            where: {
              wallId: this.wall.id,
              answerracev3Id: this.activityId,
            },
          }),
        ])

        // 数据验证
        if (!subjectList || subjectList.length === 0) {
          this.$notify.warning({
            title: '提示',
            message: '当前活动没有题目数据',
          })
          return null
        }

        if (!recordData || recordData.length === 0) {
          this.$notify.warning({
            title: '提示',
            message: '没有找到答题记录',
          })
          return null
        }

        // 获取团队信息
        await this.fetchTeamList()

        // 处理用户信息
        let userColumns = []
        const wxUserIdList = recordData.filter((item) => item && item.wxUserId).map((item) => item.wxUserId)
        if (wxUserIdList.length) {
          userColumns = await this.processListData(wxUserIdList)
        }

        // 构建团队成员映射
        const teamMemberMap = {}
        regeditData.forEach((reg) => {
          if (reg && reg.wxUserId && reg.teamId) {
            teamMemberMap[reg.wxUserId] = reg.teamId
          }
        })

        const sheets = []

        // 为每个题目创建一个sheet
        for (const subject of subjectList) {
          try {
            const subjectContent = this.parseSubjectContent(subject.subjectContent)
            const subjectName = subjectContent.content || `题目${subject.id}`

            // 过滤出该题目的记录并添加队伍信息
            const data = recordData
              .filter((item) => item && item.subjectId === subject.id)
              .map((item) => {
                const teamId = teamMemberMap[item.wxUserId]
                return {
                  ...item,
                  teamName: this.teamMap[teamId]?.teamName || '-',
                }
              })

            if (data.length === 0) {
              sheets.push({
                name: subjectName.substring(0, 30), // Excel表名长度限制
                columns: [
                  { label: '队伍名称', property: 'teamName' },
                  { label: '参赛人' },
                  { label: '回答' },
                  { label: '是否正确' },
                  { label: '得分' },
                  { label: '用时' },
                  { label: '提交时间' },
                ],
                data: [{ teamName: ' ' }],
              })
              continue
            }

            sheets.push({
              name: subjectName.substring(0, 30), // Excel表名长度限制
              columns: [
                { label: '队伍名称', property: 'teamName' },
                {
                  label: '参赛人',
                  property: 'wxUserId',
                  formatter: (row) => this.getNickName(row.wxUserId),
                },
                {
                  label: '回答',
                  property: 'option',
                  formatter: (row) => this.formatAnswerContent(row),
                },
                {
                  label: '是否正确',
                  property: 'answerResult',
                  formatter: (row) => (row.answerResult === 'Y' ? '正确' : '错误'),
                },
                { label: '得分', property: 'score' },
                {
                  label: '用时',
                  property: 'useTime',
                  formatter: (row) => this.formatUseTime(row),
                },
                { label: '提交时间', property: 'createDate' },
                ...userColumns,
              ],
              data,
            })
          } catch (subjectError) {
            console.error(`处理题目 ${subject.id} 失败:`, subjectError)
            // 继续处理其他题目，不中断整个导出流程
          }
        }

        if (sheets.length === 0) {
          this.$notify.warning({
            title: '提示',
            message: '没有找到可导出的答题数据',
          })
          return null
        }

        return { name: '答题竞赛团队答题数据', sheets }
      } catch (error) {
        console.error('导出团队答题数据失败:', error)
        this.$notify.error({
          title: '错误',
          message: error.message || '导出团队答题数据失败，请重试',
        })
        return null
      }
    },

    // 导出个人答题数据
    async exportPersonalAnswerData() {
      try {
        if (!this.hasSubjects) {
          this.$notify.warning({
            title: '提示',
            message: '当前活动没有题目数据',
          })
          return null
        }

        // 批量获取所有数据，避免循环中的重复API调用
        const baseWhere = {
          wallId: this.wall.id,
          answerracev3Id: this.activityId,
        }

        const [rankingResponse, allRecordData] = await Promise.all([
          api.answerracev3record.ranking(baseWhere),
          api.answerracev3record.list({ where: baseWhere }),
        ])

        // 处理排名数据结构 - 个人模式返回 {dataList, outList}，团队模式直接返回数组
        let allRankingData = []
        if (this.isTeam) {
          allRankingData = Array.isArray(rankingResponse) ? rankingResponse : []
        } else {
          if (rankingResponse && typeof rankingResponse === 'object') {
            const { dataList = [], outList = [] } = rankingResponse
            allRankingData = [...dataList, ...outList]
          }
        }

        if (!allRankingData || allRankingData.length === 0) {
          this.$notify.warning({
            title: '提示',
            message: '没有找到排名数据',
          })
          return null
        }

        if (!allRecordData || allRecordData.length === 0) {
          this.$notify.warning({
            title: '提示',
            message: '没有找到答题记录',
          })
          return null
        }

        // 构建记录映射，提高查找效率
        const recordMap = new Map()
        allRecordData.forEach((item) => {
          if (item && item.wxUserId && item.subjectId) {
            const key = `${item.wxUserId}_${item.subjectId}`
            recordMap.set(key, item)
          }
        })

        // 构建排名映射
        const rankingMap = new Map()
        allRankingData.forEach((item, index) => {
          if (item && item.wxUserId) {
            rankingMap.set(item.wxUserId, {
              ...item,
              calculatedRanking: item.ranking || index + 1,
            })
          }
        })

        const allAnswerData = []

        // 处理每个题目的数据
        for (const subject of this.subjects) {
          try {
            const subjectContent = this.parseSubjectContent(subject.subjectContent)

            // 获取该题目的所有答题记录
            const subjectRecords = allRecordData.filter((item) => item && item.subjectId === subject.id)

            subjectRecords.forEach((record) => {
              if (record && record.wxUserId) {
                const answerContent = this.formatAnswerContent(record)

                allAnswerData.push({
                  题目序号: subject.id,
                  题目内容: subjectContent.content || '-',
                  参赛人: record.wxUserId, // 这里会通过 formatter 显示用户名
                  回答内容: answerContent,
                  是否正确: record.answerResult === 'Y' ? '正确' : '错误',
                  分数: record.score || 0,
                  用时: this.formatCostTimeValue(record.useTime),
                  提交时间: record.createDate,
                  wxUserId: record.wxUserId, // 保留原始 ID 用于用户名显示
                })
              }
            })
          } catch (subjectError) {
            console.error(`处理题目 ${subject.id} 失败:`, subjectError)
            // 继续处理其他题目，不中断整个导出流程
          }
        }

        if (!allAnswerData.length) {
          this.$notify.warning({
            title: '提示',
            message: '没有找到答题数据',
          })
          return null
        }

        // 关联用户信息
        const wxUserIdList = [...new Set(allAnswerData.map((item) => item.wxUserId))]
        let columns = []

        columns.push(
          ...[
            { label: '题目序号', property: '题目序号' },
            { label: '题目内容', property: '题目内容' },
            {
              label: '参赛人',
              property: 'wxUserId',
              formatter: (row) => this.getNickName(row.wxUserId),
            },
            { label: '回答内容', property: '回答内容' },
            { label: '是否正确', property: '是否正确' },
            { label: '分数', property: '分数' },
            { label: '用时', property: '用时' },
            { label: '提交时间', property: '提交时间' },
          ]
        )

        const data = await this.processListData(wxUserIdList)

        columns.push(...data)

        return {
          name: '答题竞赛个人答题数据',
          sheets: [
            {
              name: '答题数据',
              data: allAnswerData,
              columns,
            },
          ],
        }
      } catch (error) {
        console.error('导出个人答题数据失败:', error)
        this.$notify.error({
          title: '错误',
          message: error.message || '导出个人答题数据失败，请重试',
        })
        return null
      }
    },

    // 安全解析题目内容
    parseSubjectContent(subjectContentStr) {
      try {
        if (!subjectContentStr) return { content: '-' }
        return typeof subjectContentStr === 'string' ? JSON.parse(subjectContentStr) : subjectContentStr
      } catch (error) {
        console.error('解析题目内容失败:', error)
        return { content: '-' }
      }
    },

    // 格式化答题内容（用于导出）
    formatAnswerContent(record) {
      try {
        if (!record || !record.option) return '-'

        const optionList = JSON.parse(record.option || '[]')
        if (optionList && Array.isArray(optionList) && optionList.length > 0) {
          return optionList
            .filter((item) => item && typeof item.index === 'number' && item.index >= 0)
            .map((item) => String.fromCharCode(65 + item.index))
            .join(',')
        }
        return '-'
      } catch (error) {
        console.error('格式化答题内容失败:', error)
        return '-'
      }
    },

    // 格式化用时数值（用于导出）
    formatCostTimeValue(time) {
      try {
        if (!time || time <= 0) return '-'
        const timeInSeconds = typeof time === 'string' ? parseFloat(time) : time
        if (isNaN(timeInSeconds)) return '-'
        return (timeInSeconds / 1000).toFixed(2) + 's'
      } catch (error) {
        console.error('格式化用时失败:', error)
        return '-'
      }
    },

    // 验证导出数据的完整性
    validateExportData(data, dataType = '数据') {
      if (!data || !Array.isArray(data) || data.length === 0) {
        this.$notify.warning({
          title: '提示',
          message: `没有可导出的${dataType}`,
        })
        return false
      }
      return true
    },
  },
  async mounted() {
    try {
      // 获取活动列表
      await this.fetchActivities()
      await this.fetchSubjects()
      await this.fetchRanking()
    } catch (error) {
      console.error('组件初始化错误:', error)
      this.$notify.error({
        title: '错误',
        message: '页面初始化失败，请刷新重试',
      })
    }
  },
}
</script>
<style scoped lang="scss">
.w-100 {
  width: 100px;
}
.color9 {
  color: #999;
}
.red {
  color: red;
}
.w-120 {
  width: 120px;
}

.empty-data {
  padding: 20px;
  color: #999;
  font-size: 14px;
  text-align: center;
}
.el-select {
  margin-bottom: 10px;
}
label {
  color: #a9a9a9;
  font-size: 14px;
  padding: 0 5px 0 0;
}
.table {
  padding: 20px 0;
}
.mar-l-15 {
  margin-left: 15px;
}
.select-width {
  width: 150px;
}
.search-btn {
  width: 140px;
}

.wall-data-answer-box :deep(.detail-dialog .el-dialog__body) {
  .dialog-footer {
    text-align: right;
    padding-right: 28px;
    margin-top: 20px;
  }
  .el-table {
    max-height: 430px;
    overflow-y: auto;
  }
}

:deep(.cell) {
  white-space: pre-line;
}
</style>
