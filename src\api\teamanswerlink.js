import api from './api';

export default {
  insert: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlink/insert.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlink/delete.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlink/read.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlink/update.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlink/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlink/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlink/add.htm', postData),
  sort: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlink/sort.htm', postData),
  updateBgimg: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlink/updateBgimg.htm', postData),
};
