import api from './api'
export default {
  add: postData => api.fetchBaseData('/pro/hxc/prowallredpackawards/add.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prowallredpackawards/update.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prowallredpackawards/list.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/prowallredpackawards/delete.htm', postData),
}
