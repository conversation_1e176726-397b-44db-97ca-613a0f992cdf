import api from './api'
export default {
  add: (v) => api.fetchBaseData('/pro/hxc/propiclotteryv3/add.htm', v),
  read: (v) => api.fetchBaseData('/pro/hxc/propiclotteryv3/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/propiclotteryv3/update.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/propiclotteryv3/list.htm', v),
  sort: (v) => api.fetchBaseData('/pro/hxc/propiclotteryv3/sort.htm', v),
  delete: (v) => api.fetchBaseData('/pro/hxc/propiclotteryv3/delete.htm', v),
}
