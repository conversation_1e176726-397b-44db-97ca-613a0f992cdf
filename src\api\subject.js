import api from './api'
export default {
  add: (v) => api.fetchBaseData('/pro/hxc/prosubject/add.htm', v),
  batchAdd: (v) => api.fetchBaseData('/pro/hxc/prosubject/batchAdd.htm', v),
  delete: (v) => api.fetchBaseData('/pro/hxc/prosubject/delete.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/prosubject/update.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/prosubject/list.htm', v),
  page: (v) => api.fetchBaseData('/pro/hxc/prosubject/page.htm', v),
}
