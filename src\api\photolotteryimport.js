import api from './api';
export default {
  add: (postData) => api.fetchBaseData('/pro/hxc/prophotolotteryimport/add.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/prophotolotteryimport/update.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/prophotolotteryimport/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/prophotolotteryimport/list.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/prophotolotteryimport/delete.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/prophotolotteryimport/batch.htm', postData),
  cleardata: (postData) => api.fetchBaseData('/pro/hxc/prophotolotteryimport/cleardata.htm', postData),
};
