import api from './api'
export default {
  read: postData => api.fetchBaseData('/pro/hxc/prosupperzzleteam/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prosupperzzleteam/update.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prosupperzzleteam/list.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/prosupperzzleteam/batch.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/prosupperzzleteam/add.htm', postData),
}
