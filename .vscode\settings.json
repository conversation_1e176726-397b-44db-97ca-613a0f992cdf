{"cSpell.words": ["accountdetail", "accountpay", "acount", "alipay", "answerrace", "attendtype", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "bgblue", "bggreen", "bgred", "<PERSON><PERSON>", "buycont", "cacle", "cancelfn", "cancle", "cardbox", "clearbutton", "closebox", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>unt", "<PERSON><PERSON><PERSON>", "DISCOUNTCARD", "Discout", "failfn", "FLUXCOUNT", "fnbox", "gao<PERSON>", "goldcoin", "goodstag", "headcountpackage", "hiben", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "iflex", "JSAPI", "libdomain", "marginb", "minicode", "miniprogram", "mousetreasure", "msgpack", "NOLIMIT", "ONECE", "<PERSON><PERSON><PERSON>", "pointspay", "popver", "programhref", "Qrcode", "READPACKCOUNT", "<PERSON><PERSON><PERSON>", "redaccount", "Redpack", "redpacket", "redpackpay", "redpackrecharge", "redpackrechargefee", "redpay", "<PERSON><PERSON>", "<PERSON><PERSON>", "showtag", "signatureconfig", "successfn", "sumbmit", "taocan", "Threed", "trem", "useraccount", "usercoupon", "userfunction", "vatfunction", "vatintroduce", "VOICEPACK", "vuex", "wallconfig", "walltime", "wallupgrade", "wallupgradepure", "webcount", "withdrawdetail", "wxpay", "wxuser", "<PERSON><PERSON><PERSON><PERSON>"], "compile-hero.disable-compile-files-on-did-save-code": true, "commentTranslate.multiLineMerge": false, "commentTranslate.source": "Google", "common-intellisense.ui": [], "commentTranslate.browse.mode": "inplace"}