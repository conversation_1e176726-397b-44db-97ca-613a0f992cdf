import api from './api'
export default {
  //报名签到数据
  list: postData => api.fetchBaseData('/pro/hxc/prowallapplysignapplydata/list.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prowallapplysignapplydata/update.htm', postData),
  audit: postData => api.fetchBaseData('/pro/hxc/prowallapplysignapplydata/audit.htm', postData),
  imports: postData => api.fetchBaseData('/pro/hxc/prowallapplysignapplydata/imports.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/prowallapplysignapplydata/page.htm', postData),
  count: postData => api.fetchBaseData('/pro/hxc/prowallapplysignapplydata/count.htm', postData),
  insert: postData => api.fetchBaseData('/pro/hxc/prowallapplysignapplydata/insert.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/prowallapplysignapplydata/delete.htm', postData),
  // 邀请函
}
