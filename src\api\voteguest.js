import api from './api'
export default {
  // 投票嘉宾
  add: postData => api.fetchBaseData('/pro/hxc/prowallvoteguest/add.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prowallvoteguest/update.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/prowallvoteguest/delete.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prowallvoteguest/list.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/prowallvoteguest/batch.htm', postData),
}