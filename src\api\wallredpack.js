import api from './api'
export default {
  list: postData => api.fetchBaseData('/pro/hxc/prowallredpack/list.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/prowallredpack/page.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/prowallredpack/add.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/prowallredpack/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prowallredpack/update.htm', postData),
  closed: postData => api.fetchBaseData('/pro/hxc/prowallredpack/closed.htm', postData),
}
