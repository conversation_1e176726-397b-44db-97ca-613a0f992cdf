import api from './api'
export default {
  //第三方账号的绑定
  list: postData => api.fetchBaseData('/pro/hxc/prouserextend/list.htm', postData),
  // 绑定
  oauthLink: postData => api.fetchBaseData('/pro/hxc/prouserextend/oauthLink.htm', postData),
  // 解绑
  unbound: postData => api.fetchBaseData('/pro/hxc/prouserextend/unbound.htm', postData),
  //授权回调
  notify: postData => api.fetchBaseData('/pro/hxc/prouserextend/notify.htm', postData),
}
