import api from './api'
export default {
  sendcountandmoney: postData => api.fetchBaseData('/pro/hxc/proropepackrecord/sendcountandmoney.htm', postData),
  sumMoney: postData => api.fetchBaseData('/pro/hxc/proropepackrecord/sumMoney.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/proropepackrecord/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proropepackrecord/list.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proropepackrecord/update.htm', postData),
}