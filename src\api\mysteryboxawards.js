import api from './api'
export default {
  // 盲盒抽奖、红包墙，奖品信息
  read: (v) => api.fetchBaseData('/pro/hxc/promysteryboxawards/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/promysteryboxawards/update.htm', v),
  add: (v) => api.fetchBaseData('/pro/hxc/promysteryboxawards/add.htm', v),
  page: (v) => api.fetchBaseData('/pro/hxc/promysteryboxawards/page.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/promysteryboxawards/list.htm', v),
  batch: (v) => api.fetchBaseData('/pro/hxc/promysteryboxawards/batch.htm', v),
}
