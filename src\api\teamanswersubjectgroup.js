import api from './api';

export default {
  insert: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubjectgroup/insert.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubjectgroup/delete.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubjectgroup/read.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubjectgroup/update.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubjectgroup/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubjectgroup/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubjectgroup/add.htm', postData),
};
