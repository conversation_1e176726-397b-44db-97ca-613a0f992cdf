<template>
  <div class="mysterybox-list-box" v-loading="loading">
    <template v-if="pageData.dataList.length">
      <el-table :data="pageData.dataList" border fit>
        <el-table-column class-name="type-column" prop="title" label="活动主题" width="140" align="center">
          <template v-slot="{ row }">
            <div class="type-img">
              <img :src="listImage(row)" />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="活动标题" align="center"></el-table-column>
        <el-table-column label="抽奖模式" :formatter="formatterGameMode" align="center"></el-table-column>
        <el-table-column label="奖品信息" :width="100">
          <template v-slot="{ row }">
            <el-popover placement="right" title="奖品信息" width="300" trigger="hover">
              <div class="font-12">
                <p v-for="(item, index) in rowAwardsRecord(row)" :key="index" class="mrg-b-10">
                  {{ item }}
                </p>
                <p v-if="!rowAwardsRecord(row).length">还没有添加奖品信息</p>
              </div>
              <el-button slot="reference" type="text">详情</el-button>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column :width="140" label="活动状态">
          <template v-slot="{ row }">
            <span :class="formatterSatte(row).color">{{ formatterSatte(row).state }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" :width="260" align="center">
          <template v-slot="{ row }">
            <div class="oprate">
              <el-button type="text" @click="openCustomIndex(row)">指定奖品编号 </el-button>
              <el-button type="text" @click="openAppend(row)">参与方式</el-button>
              <el-button type="text" @click="edit(row)">编辑</el-button>
              <el-dropdown :hide-on-click="false" class="mrg-l-10" @command="(v) => handleCommand(row, v)">
                <el-button type="text">更多</el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="del">删除</el-dropdown-item>
                  <el-dropdown-item command="end" :disabled="row.state === 'FINISH'">结束</el-dropdown-item>
                  <router-link
                    target="_blank"
                    :to="{
                      name: 'wall-data-winningrecordlist',
                      query: { wallFlag: wall.wallFlag, id: row.id, gameMode: row.gameMode, module: 'MYSTERYBOX' },
                    }"
                  >
                    <el-dropdown-item>中奖记录</el-dropdown-item>
                  </router-link>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <hi-page :pageData="pageData" @renderPage="renderPage"></hi-page>
    </template>
    <hi-nodata v-else>
      <el-button type="text" @click="$emit('toggleTab', 'theme')" :disabled="disabled">立即创建>></el-button>
    </hi-nodata>

    <!--  指定奖品编号  -->
    <wall-mysterybox-custom-index ref="customIndex" :actRowData="actRowData"></wall-mysterybox-custom-index>

    <el-dialog title="参与方式" :visible.sync="appendDialog" v-if="appendDialog" width="700px" :close-on-click-modal="false" top="5vh">
      <div class="flex flex-a-c">
        <p class="label-name">大屏幕：</p>
        <p>
          {{ formatterGameMode(actRowData) }}
        </p>
      </div>
      <div class="value flex flex-a-c">
        <div class="width-105">大屏幕地址</div>
        <el-input class="ipt-path mrg-l-10" :value="enterScreenPath" ref="wallhref" readonly></el-input>
        <el-button plain type="info" class="mrg-l-10" @click="copy('wallhref')">复制</el-button>
        <a class="el-button clearbutton" target="_blank" :href="enterScreenPath">
          <el-button type="primary" class="mrg-l-10">打开大屏幕</el-button>
        </a>
      </div>
      <el-divider></el-divider>
      <p class="label-name">手机参与方式</p>
      <p class="no-mobile" v-if="actRowData.gameMode === 'SCREEN'">您当前选择的游戏模式为大屏幕抽奖，此模式下无需手机参与，关注大屏幕即可</p>
      <hi-attend v-else :type="'dialog'" :wall="wall" route="wall-mysterybox" :option="option"></hi-attend>
    </el-dialog>

    <el-dialog title="复制活动" :visible.sync="copyDialog" v-if="copyDialog" width="500px" :close-on-click-modal="false">
      <div class="flex copy-dialog">
        <i class="el-icon-warning" style="color: #e6a23c; font-size: 34px"></i>
        <div class="mrg-l-15">
          <p class="">确认复制当前活动</p>
          <p v-if="copyDialogObj.hasKind || copyDialogObj.hasRedpack">
            当前活动已添加
            <span v-if="copyDialogObj.hasKind">实物奖品</span>
            <span class="fred" v-if="copyDialogObj.hasRedpack">现金红包奖品</span>
          </p>
          <template v-if="copyDialogObj.hasRedpack">
            <p>
              已添加红包总额：<span class="fred">{{ copyDialogObj.redpackAmount | fenToYuan }}</span> 元
            </p>
            <p>
              当前红包账户余额：<span class="fred">{{ accountData.redpackCount | fenToYuan }}</span> 元
            </p>
            <p class="f999">需支付完成后复制</p>
          </template>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="copyDialog = false">取 消</el-button>
        <el-button type="primary" @click="copyRowConfrim" :loading="copyRowConfrimLoading">确 认</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import api from '@/api'
import { Hi, env, fenToYuan } from '@/libs/common'
import { pageMixin } from '@/libs/mixins'
import HiAccount from '@/views/common/redaccount.vue'
import HiAttend from '@/views/wall/common/attend/index.vue'
import WallMysteryboxCustomIndex from '@/views/wall/interact/mysterybox/custom-index.vue'
import { mapActions, mapGetters, mapState } from 'vuex'

const stateObj = {
  NOT_STARTED: { state: '未开始', color: 'fyellow' },
  IN: { state: '进行中', color: 'fgreen' },
  FINISH: { state: '已结束', color: 'f666' },
  DATA_ING: { state: '数据处理中', color: 'f666' },
  PAUSE: { state: '进行中(暂停)', color: 'f666' },
}
export default {
  name: 'WallMysteryboxList',
  mixins: [pageMixin],
  components: { WallMysteryboxCustomIndex, HiAccount, HiAttend },
  props: {
    wall: {
      default: () => ({}),
    },
  },
  provide() {
    return {
      elForm: this,
    }
  },
  data() {
    return {
      Hi,
      isDel: false,
      loading: false,
      copyDialog: false,
      appendDialog: false,
      copyRowConfrimLoading: false,
      option: {
        canSetType: false,
        hideTypes: ['self', 'common'],
        atcId: '',
        actType: 'H5',
      },
      actRowData: {}, //当前所选行数据
      wallArawds: [], //当前活动的奖品数据
      wallRecord: [], //当前活动的领奖数据

      copyDialogObj: {
        moduleId: null,
        hasRedpack: false,
        hasKind: false,
        redpackAmount: 0,
      },
      listDetail: {},
    }
  },
  computed: {
    ...mapState('MysteryboxEdit', ['config']),
    ...mapGetters({
      accountData: 'useraccount/getAccount',
    }),
    disabled() {
      return this.config.openState === 'N'
    },
    enterScreenPath() {
      return `${env.wwwdomain}pro/pcwall/index.html#/wall.html?wallFlag=${this.wall.wallFlag}&activity=mysterybox&mysteryboxId=${this.actRowData.id}`
    },
  },
  methods: {
    ...mapActions({
      fetchBlance: 'useraccount/fetchBlance',
    }),
    formatterGameMode(row) {
      const mode = {
        SCREEN: '大屏幕抽取',
        MOBILE: '移动端抽取',
      }
      return mode[row.gameMode] || '-'
    },
    formatterTime(row) {
      return `${row.startTime}-${row.endTime}`
    },
    formatterScreenSatte(row) {
      if (row.screenstatusLimit === 'N') {
        return '关闭'
      } else {
        return row.gameMode === 'SAMETIME' ? '开启（同时参与）' : '开启（顺序参与）'
      }
    },
    formatterSatte(row) {
      return stateObj[row.activityState] || { color: '#333' }
    },
    // 行的奖品信息
    awardsRecord(row) {
      const EMPTY = {
        name: '谢谢参与',
        count: 0,
        record: 0,
      }
      const OTHER = {}
      this.wallArawds.forEach((item) => {
        if (item.moduleId !== row.id) return
        switch (item.type) {
          case 'REDPACK':
          case 'KIND':
            OTHER[item.id] = {
              ...item,
              record: 0,
            }
            break

          case 'EMPTY':
            EMPTY.count += item.count
            break
          default:
            EMPTY.count += item.count
            break
        }
      })
      this.wallRecord.forEach((item) => {
        if (item.moduleId !== row.id) return
        if (!item.state) {
          EMPTY.record += 1
        } else if (OTHER[item.awardsId]) {
          OTHER[item.awardsId].record += 1
        }
      })
      return [...Object.values(OTHER), EMPTY]
    },
    // 把行的奖品信息转换成适合列表中显示的奖品信息
    rowAwardsRecord(row) {
      const data = this.awardsRecord(row)?.filter((item) => item.status !== 'WAIT_PAY' && item.count > 0)
      return data.map((item) => {
        const { name, mode, count, redpackAmount } = item
        const sendCnt = this.listDetail[item.id]?.sendCnt || 0
        const remaining = count - sendCnt
        if (mode === 'RANDOM') {
          return `随机金额红包：共${fenToYuan(redpackAmount)}元， 共${count}个(剩余${remaining}个)`
        } else if (mode === 'FIXED') {
          return `固定金额红包：共${fenToYuan(redpackAmount * count)}元， 共${count}个(剩余${remaining}个)`
        } else {
          return `${name}：共${count}个(剩余${remaining}个)`
        }
      })
    },
    // 行的奖品信息转换成复制活动中适合的数据结构
    copyDialogAwardsRecord(row) {
      const data = this.awardsRecord(row)
      let hasKind = false
      let hasRedpack = false
      let redpackTotalPrice = 0
      data.forEach((item) => {
        const { name, type, count, record, redpackAmount } = item
        if (count < 1 || type === 'EMPTY') return
        if (type === 'RANDOM_REDPACK') {
          hasRedpack = true
          redpackTotalPrice += redpackAmount
        } else if (type === 'FIXED_REDPACK') {
          hasRedpack = true
          redpackTotalPrice += redpackAmount * count
        } else {
          hasKind = true
        }
      })
      this.copyDialogObj = { hasKind, hasRedpack, redpackAmount: redpackTotalPrice, moduleId: row.id }
    },
    listImage(row) {
      if (row.theme === 'MYSTERYBOX') {
        return require(`@/assets/wall/interact/mysterybox/mysterybox/list.png`)
      }
      if (row.theme === 'DUANWU') {
        return require(`@/assets/wall/interact/mysterybox/dragonBoatFestival/list.png`)
      }

      return require(`@/assets/wall/interact/mysterybox/${(row.theme || 'MYSTERYBOX').toLowerCase()}/list.png`)
    },
    async renderPage() {
      this.loading = true
      try {
        this.pageData = await api.mysterybox.page({
          pageSize: this.pageData.pageSize,
          pageIndex: this.pageData.pageIndex,
          where: { wallId: this.wall.id },
          sort: { id: 'desc' },
        })
        if (this.pageData.total > 0) {
          this.$emit('toggleTab', 'list')
        } else {
          this.$emit('toggleTab', 'theme')
        }
      } catch (err) {
        console.log(err)
        this.$notify.error({ title: '错误', message: err.msg })
      }
      this.loading = false
    },
    edit(row) {
      this.$router.push({
        name: 'wall-interact-mysterybox-edit',
        query: {
          wallFlag: this.$route.query.wallFlag,
          id: row.id,
          theme: row.theme,
          gameMode: row.gameMode,
        },
      })
    },
    handleCommand(row, command) {
      switch (command) {
        case 'del':
          this.deleteRow(row)
          break
        case 'end':
          this.endRow(row)
          break
      }
    },
    async deleteRow(row) {
      await this.$confirm('删除后中奖记录、活动数据将被清空，确认删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      try {
        await api.mysterybox.delete({
          where: { wallId: this.wall.id, id: row.id },
        })
        await this.renderPage()
        this.$notify.success({ title: '成功！', message: '删除成功！' })
      } catch (err) {
        this.$notify.error({ title: '错误', message: err.msg })
        console.log(err)
      }
    },
    async endRow(row) {
      await this.$confirm('确定结束活动吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      try {
        await api.mysterybox.finish({ wallId: this.wall.id, id: row.id })
        await this.renderPage()
        this.$notify.success({ title: '成功！', message: '结束成功！' })
      } catch (err) {
        this.$notify.error({ title: '错误', message: err.msg })
        console.log(err)
      }
    },
    async openCustomIndex(row) {
      this.actRowData = Hi.Object.copy(row)
      this.option.actId = row.id
      await this.$nextTick()
      this.$refs.customIndex.listDialog = true
    },
    async openAppend(row) {
      this.actRowData = Hi.Object.copy(row)
      this.option.actId = row.id
      this.appendDialog = true
    },
    // 获取当前活动的奖品信息
    async fetchAwards() {
      this.wallArawds = await api.awards.list({
        where: { wallId: this.wall.id, module: 'mysterybox' },
      })
      await this.fetchListDetail(this.wallArawds)
    },
    async fetchListDetail(list) {
      if (!list.length) return
      const res = await api.awards.sendInfo({
        where: {
          wallId: this.wall.id,
          idList: list.map((item) => item.id),
        },
      })
      this.listDetail = res.reduce((acc, item) => {
        acc[item.id] = item
        acc[item.id].sendAmount = item.sendAmount || 0
        return acc
      }, {})
    },
    copy(cont) {
      this.$refs[cont].select()
      document.execCommand('Copy')
      this.$message({
        message: '复制成功',
        type: 'success',
      })
    },
  },
  async mounted() {
    await this.renderPage()
    await this.fetchAwards()
    await this.fetchBlance() //账户信息
  },
}
</script>
<style scoped lang="stylus">
.mysterybox-list-box
  background #fff

  /deep/ .type-column
    .cell
      padding 0

    .type-img
      width 140px
      height 40px

      img
        width 100%
        height 100%
        object-fit cover

  .title-box
    margin 5px 0
    min-width 150px

  .w-140
    width 140px

  .font-12
    font-size 12px !important

  .ipt-path
    width 300px

  >>> .el-dialog__body
    padding 10px 20px 15px 20px

  .no-mobile
    margin 60px 0
    text-align center
    color #999

  .label-name
    font-weight 600
    font-size 14px
    line-height 30px

  .fobbid
    margin-left 20px
    color red

  >>> .el-divider--horizontal
    margin 10px 0

  .value
    margin 10px 0

.clearbutton
  padding 0
  border 0

  &:hover
    background-color #fff

.no-fliter
  line-height 100px
  text-align center

.copy-dialog
  line-height 30px
</style>
