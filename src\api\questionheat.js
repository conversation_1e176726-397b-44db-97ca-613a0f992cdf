import api from './api'
export default {
  addHeatByAdmin: postData => api.fetchBaseData('/pro/hxc/proquestionheat/addHeatByAdmin.htm', postData),
  sumHeatDetail: postData => api.fetchBaseData('/pro/hxc/proquestionheat/sumHeatDetail.htm', postData),
  pageByType: postData => api.fetchBaseData('/pro/hxc/proquestionheat/pageByType.htm', postData),
  detailwxpage: postData => api.fetchBaseData('/pro/hxc/proquestionheat/detailwxpage.htm', postData),
  msgListHeat: postData => api.fetchBaseData('/pro/hxc/proquestionheat/msgListHeat.htm', postData),
  listAdminData: postData => api.fetchBaseData('/pro/hxc/proquestionheat/listAdminData.htm', postData),
}