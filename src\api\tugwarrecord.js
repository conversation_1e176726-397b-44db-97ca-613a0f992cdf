import api from './api'
export default {
  page: (postData) => api.fetchBaseData('/pro/hxc/protugwarrecord/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/protugwarrecord/list.htm', postData),
  sumMoney: (postData) => api.fetchBaseData('/pro/hxc/protugwarrecord/sumMoney.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/protugwarrecord/update.htm', postData),
  teamscore: (postData) => api.fetchBaseData('/pro/hxc/protugwarrecord/teamscore.htm', postData),
}
