import api from './api'
export default {
  list: postData => api.fetchBaseData('/pro/hxc/proshakeawards/list.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/proshakeawards/batch.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/proshakeawards/add.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/proshakeawards/delete.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proshakeawards/update.htm', postData),
}
