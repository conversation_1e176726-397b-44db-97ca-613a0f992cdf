<template>
  <hi-wall-set-right class="wall-settery-box">
    <div slot="default">
      <hi-remove-disabled>
        <el-button class="mar-b-18 mar-t-14" type="primary" plain size="small" @click="$emit('toList')">&lt;&lt;返回轮次列表 </el-button>
      </hi-remove-disabled>
      <hi-result-control :config="config" @updateConfig="$emit('updateConfig')"></hi-result-control>
    </div>
    <div slot="control" class="control">
      <div>
        <hi-party-way :wall="wall" route="wall-seglottery"></hi-party-way>
        <hi-to-pcwall :wall="wall"></hi-to-pcwall>
      </div>
    </div>
  </hi-wall-set-right>
</template>
<script>
import HiResultControl from '../common/resultControl/index.vue'

export default {
  name: 'seglotteryResultControl',
  inject: ['wall'],
  provide() {
    return {
      elForm: this,
    }
  },
  props: {
    config: {
      default: () => ({}),
    },
  },
  components: { HiResultControl },
}
</script>
