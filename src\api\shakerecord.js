import api from './api'
export default {
  page: (postData) => api.fetchBaseData('/pro/hxc/proshakerecord/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proshakerecord/list.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proshakerecord/update.htm', postData),
  teamscore: (postData) => api.fetchBaseData('/pro/hxc/proshakerecord/teamscore.htm', postData),
}
