import api from './api'
export default {
  //订货会-轮次关联的商品
  read: postData => api.fetchBaseData('/pro/hxc/proplaceorderordergoods/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proplaceorderordergoods/update.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/proplaceorderordergoods/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proplaceorderordergoods/list.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/proplaceorderordergoods/batch.htm', postData),
}
