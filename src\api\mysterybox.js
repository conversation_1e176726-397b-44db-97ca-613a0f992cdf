import api from './api'
export default {
  // 盲盒抽奖、红包墙
  read: (v) => api.fetchBaseData('/pro/hxc/promysterybox/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/promysterybox/update.htm', v),
  add: (v) => api.fetchBaseData('/pro/hxc/promysterybox/add.htm', v),
  page: (v) => api.fetchBaseData('/pro/hxc/promysterybox/page.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/promysterybox/list.htm', v),
  delete: (postData) => api.fetchBaseData('/pro/hxc/promysterybox/delete.htm', postData),
  finish: (postData) => api.fetchBaseData('/pro/hxc/promysterybox/finish.htm', postData),
  copy: (postData) => api.fetchBaseData('/pro/hxc/promysterybox/copy.htm', postData),
}
