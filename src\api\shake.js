import api from './api'
export default {
  page: postData => api.fetchBaseData('/pro/hxc/proshake/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proshake/list.htm', postData),
  insert: postData => api.fetchBaseData('/pro/hxc/proshake/insert.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/proshake/read.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/proshake/delete.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proshake/update.htm', postData),
}
