<template>
  <div class="box" v-if="isShow">
    <template v-if="showOther">
      <div class="other-sel-box">
        <div class="tile">请选择平台：</div>
        <div class="radioType wechart-radio" :class="[{ active: typeTab === 'wechart' }]" @click="changeTypeTab('wechart')">
          <span class="radio-point"></span>
          <div class="right">
            <span class="icon"></span>
            <span>微信</span>
          </div>
        </div>
        <div class="radioType other-radio" :class="[{ active: typeTab === 'other' }]" @click="changeTypeTab('other')">
          <span class="radio-point"></span>
          <div class="right">
            <span class="icon"></span>
            <span>跨平台H5参与</span>
          </div>
        </div>
      </div>
    </template>
    <div class="conent">
      <div class="flex m10 flex-a-c" v-if="typeTab === 'wechart'">
        <div class="flex top mr-20" v-if="option.canSetType">
          <p>当前参与方式：</p>
          <p class="blond">&nbsp;&nbsp;{{ contentTypeName }}</p>
        </div>
        <template v-if="showOther">
          <el-checkbox v-model="wallConfig.allowThirdAppScanSwitch" true-label="Y" false-label="N" @change="changeSwitch"
            >允许其他平台APP扫码参与</el-checkbox
          >
          <el-tooltip placement="right" popper-class="ask-popper">
            <div slot="content" class="tooltip">
              <span class="item">1、勾选：其他可扫码浏览网页APP，可参与，但不支持在线支付，现金红包领取等功能 </span><br />
              <span class="item">2、不勾选：其他可扫码浏览网页APP，扫码后会提示无法参与</span><br />
            </div>
            <img class="pointer-img" src="~@/assets/wall/interact/ask.png" />
          </el-tooltip>
        </template>
      </div>
      <hi-infodetail v-if="showInfo" :type="typeTab" @close="showInfo = false"></hi-infodetail>
      <div class="right-info" @click="showInfo = true" v-if="showOther && showType !== 'dialog'">
        <i class="el-icon-warning-outline"></i>
        <span>不同平台参与说明</span>
        <i class="el-icon-arrow-right"></i>
      </div>
      <div v-if="typeTab === 'wechart'">
        <div v-if="showPolyv || !isMerchants">
          <el-tabs v-model="actTab" type="card">
            <el-tab-pane v-for="item in attendList" :key="item.id" :name="item.id">
              <div slot="label" class="atten-title">
                <icon :name="item.icon"></icon>
                {{ item.name }}
                <span class="is-main" v-if="item.id === vuxWall.wxauthType">主</span>
              </div>
              <div class="flex flex-a-c">
                <template v-if="showTypeInfo">
                  <el-button type="primary" v-if="item.id === vuxWall.wxauthType" disabled>当前主参与方式</el-button>
                  <el-button type="primary" v-else @click="updateAttendType(item.id)">设为主参与方式</el-button>
                </template>
                <div class="flex mrg-l-20">
                  <p class="blond">介绍：&nbsp;</p>
                  <p class="info">{{ infoText }}</p>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
          <hi-web-type
            v-if="['MULTI_PLATFORM_H5', 'H5'].includes(actTab)"
            :wallConfig="wallConfig"
            :wall="vuxWall"
            :actType="actType"
            :route="route"
            :actId="option.actId"
            @updateWallConfig="fetchWallConfig"
            :showOther="showOther"
          >
          </hi-web-type>
          <template v-if="actTab === 'wxapp'">
            <hi-wxapp-type
              v-if="hasMinProgram"
              :wallConfig="wallConfig"
              :wall="vuxWall"
              :actType="actType"
              :route="route"
              :actId="option.actId"
            ></hi-wxapp-type>
            <div v-else class="no-min">请联系服务商开通小程序参与方式</div>
          </template>
        </div>
        <div v-else>
          <h3 style="text-align: center">{{ merchantPartnerMsg }}</h3>
        </div>
      </div>
      <hi-common-type
        v-else
        :wallConfig="wallConfig"
        :wall="vuxWall"
        :actType="actType"
        :route="route"
        :actId="option.actId"
        @updateWallConfig="fetchWallConfig"
        @updateAttendType="updateAttendType"
      ></hi-common-type>
    </div>
  </div>
</template>
<script>
import api from '@/api'
import { mapActions, mapGetters } from 'vuex'
import HiCommonType from './commontype.vue'
import HiWebType from './h5type.vue'
import HiInfodetail from './infodetail.vue'
import HiWxappType from './wxapptype.vue'

const attendData = [
  {
    id: 'H5',
    name: 'H5网页',
    icon: 'attend_h5',
    component: 'HiWebType',
  },
  // {
  //   id: 'wxapp',
  //   name: '微信小程序',
  //   icon: 'attend_mini_program',
  //   component: 'HiWxappType',
  // },
]
export default {
  name: 'WallAttendtype',
  props: {
    //展示方式dialog为partway组件内用,page为页面使用
    showType: {
      type: String,
      default: 'dialog',
    },
    actType: {
      type: String,
    },
    route: {
      default: '',
    },
    wall: {
      type: Object,
      default: () => {
        return {}
      },
    },
    //是否需要更新wall数据
    isNeedWallUpdate: {
      type: Boolean,
      default: false,
    },
    //更多配置内容,用于特定互动例如在线抽奖
    option: {
      type: Object,
      default: () => {
        return {
          canSetType: true, //是否关闭设置方式
          hideTypes: [], //需要隐藏的方式
          atcId: '',
          actType: '',
        }
      },
    },
    showPolyv: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    HiWebType,
    HiWxappType,
    HiCommonType,
    HiInfodetail,
  },
  data() {
    return {
      showInfo: false,
      isShow: false,
      activeAttend: 'HiCommonType',
      actTab: 'H5',
      typeTab: 'wechart',
      wallConfig: {},
    }
  },
  computed: {
    ...mapGetters({
      wallFlagObj: 'wall/getWallFlagObj',
      hasMinProgram: 'user/getHasMinProgram',
      merchantPartnerMsg: 'user/getMerchantPartnerMsg',
      isHKDz: 'user/getIsHKDz',
    }),
    infoText() {
      var textObj = {
        H5: '微信快速扫描或点击链接参与，便于主办方在不同参与渠道内灵活调用',
        wxapp: '微信小程序参与，方便参与者快速找到互动入口，主办方公众号内灵活调用',
      }
      if (this.actTab) {
        return textObj[this.actTab]
      } else {
        return '微信扫码参与，方便参与者快速找到互动参与入口'
      }
    },
    contentTypeName() {
      let name = 'H5网页'
      if (this.vuxWall.wxauthType) {
        this.attendList.forEach((item) => {
          if (item.id === this.vuxWall.wxauthType) {
            name = item.name
          }
        })
      }
      return name
    },
    wallFlag() {
      return this.wall.wallFlag || this.$route.query.wallFlag
    },
    vuxWall() {
      return this.wallFlagObj[this.wallFlag] || {}
    },
    attendList() {
      return attendData.filter((item) => {
        return !this.option.hideTypes.includes(item.id)
      })
    },
    //是否展示切换方式和信息区域
    showTypeInfo() {
      if (this.actTab === 'wxapp') {
        return this.hasMinProgram
      } else {
        return this.option.canSetType
      }
    },
    showOther() {
      return true
    },
    isMerchants() {
      return false
    },
  },
  methods: {
    ...mapActions({
      fetchWall: 'wall/fetchWall',
    }),
    async updateAttendType(type) {
      try {
        await api.wall.update({
          where: { id: this.vuxWall.id },
          update: { wxauthType: type },
        })
        await this.fetchWall({
          wallFlag: this.wallFlag,
          force: true,
        })
        this.$notify.success({ title: '成功', message: '活动主参与方式设置完成' })
      } catch (err) {
        console.log(err)
        this.$notify.error(err.msg || '提交失败！')
      }
    },
    getActiveAttend(id) {
      let component = 'HiWebType'
      if (id) {
        this.attendList.forEach((item) => {
          if (item.id === id) {
            component = item.component
          }
        })
      }
      return component
    },
    async fetchWallConfig() {
      try {
        this.wallConfig = await api.wallconfig.read({
          where: { wallId: this.wall.id },
        })
      } catch (err) {
        console.log(err)
        this.$notify.error(err.msg || '提交失败！')
      }
    },
    async changeSwitch(val) {
      try {
        await api.wallconfig.update({
          where: { wallId: this.wall.id },
          update: { allowThirdAppScanSwitch: val },
        })
        await this.fetchWallConfig()
        this.$notify.success({ title: '成功', message: '修改成功!' })
      } catch (err) {
        console.log(err)
        this.$notify.error(err.msg || '提交失败！')
      }
    },
    changeTypeTab(val) {
      this.typeTab = val
      if (val === 'wechart') {
        this.actTab = 'H5'
        this.updateAttendType('H5')
      }
      if (val === 'other') {
        this.updateAttendType('MULTI_PLATFORM_H5')
      }
    },
  },

  async mounted() {
    // await this.wallHandler.call(this);
    await this.fetchWallConfig()
    await this.fetchWall({ wallFlag: this.wallFlag })
    //如果没有传入确定设置值，则根据主参与方式配置来，用于在线抽奖等参与方式需要控制的互动
    this.actTab = this.option.actType || this.vuxWall.wxauthType || 'H5'
    this.typeTab = this.vuxWall.wxauthType === 'MULTI_PLATFORM_H5' ? 'other' : 'wechart'
    this.isShow = true
  },
}
</script>
<style scoped lang="stylus">
.w-120
  width 120px
.mt-10
  margin-top 10px
.m10
  margin 10px 0
.box
  min-height 650px
  position relative
  .top
    font-size 14px
    margin-right 30px
    .blond
      font-weight 600
  .info
    color #999
  .mrg-l-20
    margin-left 20px
.atten-title
  position relative
  margin 0 5px
  .is-main
    width 20px
    height 20px
    background rgb(250, 99, 99)
    border-radius 50%
    line-height 20px
    text-align center
    color #fff
    position absolute
    right -20px
    top 0px
    font-size 12px
.no-min
  width 100%
  height 200px
  text-align center
  font-size 24px
  font-weight 600
  line-height 200px
.right-info
  display flex
  justify-content center
  align-items center
  font-size 14px
  margin 10px 0
  color #409eff
  position absolute
  right 0
  top 0
  cursor pointer
  i
    font-size 20px
.other-sel-box
  display flex
  align-items center
  gap 15px
  .tile
    font-size: 14px
    font-weight: 600
  .radioType
    width 175px
    height 35px
    border 1px solid #ddd
    border-radius 3px
    display flex
    justify-content center
    align-items center
    gap 5px
    cursor pointer
    box-sizing border-box
    position relative
    &.active
      color #00B95B
      border-color #00B95B
      .radio-point
        border-color #00B95B
    .right
      display flex
      align-items center
      align-items center
      gap 5px
      margin-left 5px
    span
      display inline-block
    .radio-point
      width 12px
      height 12px
      border-radius 50%
      border 4px solid #ddd
      background #fff
      box-sizing border-box
      position absolute
      left 8px
      top 50%
      transform translateY(-50%)
    .icon
      width 18px
      height 18px
      background-size contain
      background-repeat no-repeat
.wechart-radio
  .icon
    background url("./img/we1.png") no-repeat
    background-size contain
  &.active
    .icon
      background url("./img/we2.png") no-repeat
.other-radio
  .icon
    background url("./img/H5-1.png") no-repeat
  &.active
    border-color #ff6633 !important
    color #ff6633 !important
    .icon
      background url("./img/H5-2.png") no-repeat
      border-color #ff6633 !important
    .radio-point
      border-color #ff6633 !important
.pointer-img
  width 15px
  height 15px
  cursor pointer
  vertical-align middle
  display inline-block
.conent
  border 1px solid #ddd
  border-radius 5px
  box-sizing border-box
  padding 10px
  margin-top 10px
>>>.el-checkbox
  margin-right 5px !important
.tooltip
  width 340px
</style>
