import { env } from '@/libs/common'

// 默认配置 // goldcoin
const defaultConfig = [
  {
    title: '接金币',
    themeType: 'JIEJINBI_THEME', // 主题类型
    vatKey: 'jiejinbiTheme', //付费类型
    imgPath: require('@/assets/wall/interact/goldcoin/gold.png'), // 主题图片
    isFree: true, //是否免费
    notEdit: true, //不允许编辑
    themeData: {
      // 默认数据
      bulletShow: 'DEFAULT',
      element: 'https://res3.hixianchang.com/qn/up/82acbdbf03c92202c3a3e58c99c4a6d4.png', // 元素图片
      fall: [
        'https://res3.hixianchang.com/qn/up/72dffeb187fe1817015efb4f48800231.png',
        'https://res3.hixianchang.com/qn/up/bb7d2a406b78691256f5752daad3f462.png',
      ],
      reduceFall: ['https://res3.hixianchang.com/qn/up/f0fa10e9df0e02145dce2a866cce5b98.png'],
      waitBg: 'https://res3.hixianchang.com/qn/up/3898ec7758cc8d6a7238ebf82dccd6f6.png', // 等待图片
      inBg: 'https://res3.hixianchang.com/qn/up/3898ec7758cc8d6a7238ebf82dccd6f6.png', // 进行图片
      waitTitle: 'https://res3.hixianchang.com/qn/up/bb0ff390da436a5c7f65c0151c1d031c.png', // 等待页中心图
      waitMusic: 'https://res3.hixianchang.com/qn/up/12908978b7b35fccca2ee94fd840f4dc.mp3', // 等待页音乐
      inMusic: 'https://res3.hixianchang.com/qn/up/9dcd80146dc89f077ce244c1df911a69.mp3', // 进行页音乐
      endMusic: `${env.libdomain}web-admin/shake/waitMusic.mp3`, // 结束页音乐
      mobileBg: 'https://res3.hixianchang.com/qn/up/1a3efbec641d9fe2565c980535c3488e.png', // 手机背景图

      histogramBottom: require('@/assets/wall/interact/goldcoin/jiejinbiTheme/histogramBottom.png'), // 柱状图
      histogramMiddle: require('@/assets/wall/interact/goldcoin/jiejinbiTheme/histogramMiddle.png'), // 柱状图
      histogramTop: require('@/assets/wall/interact/goldcoin/jiejinbiTheme/histogramTop.png'), // 柱状图
    },
  },
  {
    title: '聚宝盆',
    themeType: 'JIELONGZHU_THEME', // 主题类型
    vatKey: 'jielongzhuTheme', //付费类型
    imgPath: require('@/assets/wall/interact/goldcoin/jielongzhu.png'), // 主题图片
    isFree: false, //是否免费
    notEdit: false, //不允许编辑
    themeData: {
      // 默认数据
      bulletShow: 'DEFAULT',
      element: 'https://res3.hixianchang.com/qn/up/29a33cb633f3969eacfd8fe7141acf43.png', // 元素图片
      fall: [
        'https://res3.hixianchang.com/qn/up/72dffeb187fe1817015efb4f48800231.png',
        'https://res3.hixianchang.com/qn/up/7df0a364821d0fdeb40a4bcb19d5e979.png',
        'https://res3.hixianchang.com/qn/up/99285021ad42c753e8ea27552a6c8505.png',
      ],
      reduceFall: ['https://res3.hixianchang.com/qn/up/ee12bda83a3f5bffcbc7048a54dbf7f6.png'],
      waitBg: 'https://res3.hixianchang.com/qn/up/b5e5454c450548504cd4e328d7f3cdbc.png', // 等待图片
      inBg: 'https://res3.hixianchang.com/qn/up/b5e5454c450548504cd4e328d7f3cdbc.png', // 进行图片
      waitTitle: 'https://res3.hixianchang.com/qn/up/7bf6a26b59f74f09ea16ad5da50c53f3.png', // 等待页中心图
      waitMusic: 'https://res3.hixianchang.com/qn/up/db8da115335dcfe7cc349493dc3b4ae4.mp3', // 等待页音乐
      inMusic: 'https://res3.hixianchang.com/qn/up/f2741de19f878dae58e4b17399f407f0.mp3', // 进行页音乐
      endMusic: `${env.libdomain}web-admin/shake/waitMusic.mp3`, // 结束页音乐
      mobileBg: 'https://res3.hixianchang.com/qn/up/d51c922dd3cb29d976b424530840fd6c.png', // 手机背景图

      histogramBottom: require('@/assets/wall/interact/goldcoin/jielongzhuTheme/histogramBottom.png'), // 柱状图
      histogramMiddle: require('@/assets/wall/interact/goldcoin/jielongzhuTheme/histogramMiddle.png'), // 柱状图
      histogramTop: require('@/assets/wall/interact/goldcoin/jielongzhuTheme/histogramTop.png'), // 柱状图
    },
  },
  {
    title: '金兔迎财',
    themeType: 'JINTUYINGCAI_THEME', // 主题类型
    vatKey: 'jintuyingcaiTheme', //付费类型
    imgPath: require('@/assets/wall/interact/goldcoin/jintuyingcai.png'), // 主题图片
    isFree: false, //是否免费
    notEdit: false, //不允许编辑
    themeData: {
      // 默认数据
      bulletShow: 'DEFAULT',
      element: 'https://res3.hixianchang.com/qn/up/acdb77e4eced7c7f3aac6967154455a2.png', // 飞机图片
      fall: [
        'https://res3.hixianchang.com/qn/up/f326e75ea0e24afd6408bab2cd48bb24.png',
        'https://res3.hixianchang.com/qn/up/8e7a2d8efdbbf87bde0329df9b82bca8.png',
      ],
      reduceFall: ['https://res3.hixianchang.com/qn/up/ee12bda83a3f5bffcbc7048a54dbf7f6.png'],
      waitBg: 'https://res3.hixianchang.com/qn/up/13e7fa29c3ac44961461144a8bac3255.jpg', // 等待图片
      inBg: 'https://res3.hixianchang.com/qn/up/13e7fa29c3ac44961461144a8bac3255.jpg', // 进行图片
      waitTitle: 'https://res3.hixianchang.com/qn/up/9248080772bde4446b6f9f58bd47ddde.png', // 等待页中心图
      waitMusic: 'https://res3.hixianchang.com/qn/up/db8da115335dcfe7cc349493dc3b4ae4.mp3', // 等待页音乐
      inMusic: 'https://res3.hixianchang.com/qn/up/f2741de19f878dae58e4b17399f407f0.mp3', // 进行页音乐
      endMusic: `${env.libdomain}web-admin/shake/waitMusic.mp3`, // 结束页音乐
      mobileBg: 'https://res3.hixianchang.com/qn/up/26806cc6e3822ddd6291a5fd584c06b0.jpg', // 手机背景图
      histogramBottom: require('@/assets/wall/interact/goldcoin/jintuyingcaiTheme/histogramBottom.png'), // 柱状图
      histogramMiddle: require('@/assets/wall/interact/goldcoin/jintuyingcaiTheme/histogramMiddle.png'), // 柱状图
      histogramTop: require('@/assets/wall/interact/goldcoin/jintuyingcaiTheme/histogramTop.png'), // 柱状图
    },
  },
  {
    title: '礼从天降',
    themeType: 'LICONGTIANJIANG_THEME', // 主题类型
    vatKey: 'licongtianjiangTheme', //付费类型
    imgPath: require('@/assets/wall/interact/goldcoin/baoxianglaile.png'), // 主题图片
    isFree: false, //是否免费
    notEdit: false, //不允许编辑
    themeData: {
      // 默认数据
      bulletShow: 'DEFAULT',
      element: 'https://res3.hixianchang.com/qn/up/09fa6fe01957c835bfbb0b4f870ff2fb.png', // 元素图片
      fall: [
        'https://res3.hixianchang.com/qn/up/f74e8a8c3076073068fe36438eed403f.png',
        'https://res3.hixianchang.com/qn/up/b70b51590ae6e7b1af13a39792fb5aeb.png',
      ],
      reduceFall: ['https://res3.hixianchang.com/qn/up/2ae0e7ec9d6ee9b8053a2bc447cff57c.png'],
      waitBg: 'https://res3.hixianchang.com/qn/up/5237d4339a41389c668ce70c4ef560ce.png', // 等待图片
      inBg: 'https://res3.hixianchang.com/qn/up/5237d4339a41389c668ce70c4ef560ce.png', // 进行图片
      waitTitle: 'https://res3.hixianchang.com/qn/up/711ccdcb4f58ca4dbd26fc064e8f0435.png', // 等待页中心图
      waitMusic: 'https://res3.hixianchang.com/qn/up/db8da115335dcfe7cc349493dc3b4ae4.mp3', // 等待页音乐
      inMusic: 'https://res3.hixianchang.com/qn/up/f2741de19f878dae58e4b17399f407f0.mp3', // 进行页音乐
      endMusic: `${env.libdomain}web-admin/shake/waitMusic.mp3`, // 结束页音乐
      mobileBg: 'https://res3.hixianchang.com/qn/up/1785ca50d499c62f514a48fcdee0feef.png', // 手机背景图

      histogramBottom: require('@/assets/wall/interact/goldcoin/licongtianjiangTheme/histogramBottom.png'), // 柱状图
      histogramMiddle: require('@/assets/wall/interact/goldcoin/licongtianjiangTheme/histogramMiddle.png'), // 柱状图
      histogramTop: require('@/assets/wall/interact/goldcoin/licongtianjiangTheme/histogramTop.png'), // 柱状图
    },
  },
  {
    title: '抢捧花',
    themeType: 'QIANGPENGHUA_THEME', // 主题类型
    vatKey: 'qiangpenghuaTheme', //付费类型
    imgPath: require('@/assets/wall/interact/goldcoin/western.png'), // 主题图片
    isFree: false, //是否免费
    notEdit: false, //不允许编辑
    themeData: {
      // 默认数据
      bulletShow: 'DEFAULT',
      element: 'https://res3.hixianchang.com/qn/up/7788f9a87bd3359e3700797be9100cbd.png', // 元素图片
      fall: [
        'https://res3.hixianchang.com/qn/up/0aad0b4667fb12fa92f4bb6fb650fcfe.png',
        'https://res3.hixianchang.com/qn/up/e6404e25716a6f776c23e615d9cf8a6a.png',
      ],
      reduceFall: ['https://res3.hixianchang.com/qn/up/fd29bcde4b19debf5f539b99cf204fd1.png'],
      waitBg: 'https://res3.hixianchang.com/qn/up/acade703be5b296d2dda9a490c561c4a.png', // 等待图片
      inBg: 'https://res3.hixianchang.com/qn/up/acade703be5b296d2dda9a490c561c4a.png', // 进行图片
      waitTitle: 'https://res3.hixianchang.com/qn/up/bea73602d1eca5397340b4cf7333adfc.png', // 等待页中心图
      waitMusic: 'https://res3.hixianchang.com/qn/up/db8da115335dcfe7cc349493dc3b4ae4.mp3', // 等待页音乐
      inMusic: 'https://res3.hixianchang.com/qn/up/f2741de19f878dae58e4b17399f407f0.mp3', // 进行页音乐
      endMusic: `${env.libdomain}web-admin/shake/waitMusic.mp3`, // 结束页音乐
      mobileBg: 'https://res3.hixianchang.com/qn/up/72f7f43242ef93715d2bd379afd01654.png', // 手机背景图
      histogramBottom: require('@/assets/wall/interact/goldcoin/qiangpenghuaTheme/histogramBottom.png'), // 柱状图
      histogramMiddle: require('@/assets/wall/interact/goldcoin/qiangpenghuaTheme/histogramMiddle.png'), // 柱状图
      histogramTop: require('@/assets/wall/interact/goldcoin/qiangpenghuaTheme/histogramTop.png'), // 柱状图
    },
  },
  {
    title: '接绣球',
    themeType: 'JIEXIUQIU_THEME', // 主题类型
    vatKey: 'jiexiuqiuTheme', //付费类型
    imgPath: require('@/assets/wall/interact/goldcoin/east.png'), // 主题图片
    isFree: false, //是否免费
    notEdit: false, //不允许编辑
    themeData: {
      // 默认数据
      bulletShow: 'DEFAULT',
      element: 'https://res3.hixianchang.com/qn/up/7f1359565c3ec31c675de145870451f5.png', // 元素图片
      fall: [
        'https://res3.hixianchang.com/qn/up/e9878dafb84193fa5f4bf5ad96677bd6.png',
        'https://res3.hixianchang.com/qn/up/8932f599988bc39d9875305500e78b0f.png',
      ],
      reduceFall: ['https://res3.hixianchang.com/qn/up/fd29bcde4b19debf5f539b99cf204fd1.png'],
      waitBg: 'https://res3.hixianchang.com/qn/up/89ac810781fd245355c80a30f32047e3.png', // 等待图片
      inBg: 'https://res3.hixianchang.com/qn/up/89ac810781fd245355c80a30f32047e3.png', // 进行图片
      waitTitle: 'https://res3.hixianchang.com/qn/up/3642cc1cba67a138212cedf441f84821.png', // 等待页中心图
      waitMusic: 'https://res3.hixianchang.com/qn/up/db8da115335dcfe7cc349493dc3b4ae4.mp3', // 等待页音乐
      inMusic: 'https://res3.hixianchang.com/qn/up/f2741de19f878dae58e4b17399f407f0.mp3', // 进行页音乐
      endMusic: `${env.libdomain}web-admin/shake/waitMusic.mp3`, // 结束页音乐
      mobileBg: 'https://res3.hixianchang.com/qn/up/523b7e03f2837c01f95c5e0a392e8ca3.png', // 手机中心图

      histogramBottom: require('@/assets/wall/interact/goldcoin/jiexiuqiuTheme/histogramBottom.png'), // 柱状图
      histogramMiddle: require('@/assets/wall/interact/goldcoin/jiexiuqiuTheme/histogramMiddle.png'), // 柱状图
      histogramTop: require('@/assets/wall/interact/goldcoin/jiexiuqiuTheme/histogramTop.png'), // 柱状图
    },
  },
]
defaultConfig.forEach((item) => {
  item.themeData.title = item.title
  item.themeData.themeType = item.themeType
})
export { defaultConfig }
