import api from './api'
export default {
  add: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3subject/add.htm', postData),
  batchAdd: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3subject/batchAdd.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3subject/update.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3subject/list.htm', postData),
  sort: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3subject/sort.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proanswerracev3subject/delete.htm', postData),
}
