<template>
  <div class="debug-access-page">
    <div class="access-card">
      <div class="header">
        <h2>{{ !hasAccess ? '🔐' : '🔓' }} 测试环境访问权限</h2>
        <p class="env-badge">{{ envInfo }}</p>
      </div>

      <div class="status-section">
        <div class="status-info">
          <div class="status-item">
            <span class="label">当前状态:</span>
            <span :class="['status-value', hasAccess ? 'success' : 'error']">
              {{ hasAccess ? '✅ 已授权' : '❌ 未授权' }}
            </span>
          </div>

          <div class="status-item" v-if="accessTime">
            <span class="label">授权时间:</span>
            <span class="status-value">{{ accessTime }}</span>
          </div>

          <div class="status-item" v-if="hasAccess">
            <span class="label">权限状态:</span>
            <span class="status-value success">永久有效</span>
          </div>
        </div>
      </div>

      <div class="action-section">
        <el-button v-if="!hasAccess" type="success" size="large" @click="grantAccess" icon="el-icon-unlock" :loading="loading">
          {{ loading ? '获取中...' : '获取访问权限' }}
        </el-button>

        <el-button v-if="hasAccess" type="primary" size="large" @click="goToLogin" icon="el-icon-right"> 前往登录页面 </el-button>

        <el-button v-if="hasAccess" type="warning" size="large" @click="revokeAccess" icon="el-icon-refresh-left" plain> 撤销权限 </el-button>
      </div>

      <div class="info-section">
        <div class="info-item">
          <i class="el-icon-warning"></i>
          <span>此页面仅在测试环境可用</span>
        </div>
        <div class="info-item">
          <i class="el-icon-info"></i>
          <span>获取权限后可正常访问测试环境所有功能</span>
        </div>
        <div class="info-item">
          <i class="el-icon-time"></i>
          <span>权限永久有效，除非手动重新获取</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { env, Hi } from '@/libs/common'

export default {
  name: 'DebugAccess',
  data() {
    return {
      loading: false,
      hasAccess: false,
      accessTime: '',
      timer: null,
    }
  },
  computed: {
    envInfo() {
      return `当前环境: ${env.env} | 域名: ${window.location.hostname}`
    },
  },
  methods: {
    // 检查权限状态
    checkAccessStatus() {
      const storageKey = Hi.generateStorageKey()
      const encryptedData = localStorage.getItem(storageKey)

      if (encryptedData) {
        const decrypted = Hi.decryptValue(encryptedData)
        if (decrypted && decrypted.data === '0108') {
          this.hasAccess = true
          this.accessTime = this.formatTime(decrypted.timestamp)
          return
        }
      }

      // 如果解密失败或数据无效，清除可能的无效数据
      this.clearInvalidAccess()
      this.hasAccess = false
      this.accessTime = ''
    },

    // 清除无效的访问数据
    clearInvalidAccess() {
      const storageKey = Hi.generateStorageKey()
      localStorage.removeItem(storageKey)
      // 同时清除可能的旧版本key
      localStorage.removeItem('debug_access_granted')
    },

    // 获取访问权限
    async grantAccess() {
      this.loading = true
      try {
        // 模拟异步操作
        await new Promise((resolve) => setTimeout(resolve, 1000))

        const timestamp = Date.now()
        const storageKey = Hi.generateStorageKey()
        const encryptedValue = Hi.encryptValue('0108')

        // 存储加密后的权限数据
        localStorage.setItem(storageKey, encryptedValue)

        this.hasAccess = true
        this.accessTime = this.formatTime(timestamp)

        this.$notify.success({
          title: '成功',
          message: '访问权限获取成功！',
          duration: 3000,
        })
      } catch (error) {
        this.$notify.error({
          title: '错误',
          message: '权限获取失败，请重试',
          duration: 3000,
        })
      } finally {
        this.loading = false
      }
    },

    // 重新获取权限
    async revokeAccess() {
      try {
        await this.$confirm('确定要撤销权限吗？这将清除当前权限状态。', '确认操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        // 清除加密的权限数据
        this.clearInvalidAccess()
        this.hasAccess = false
        this.accessTime = ''

        this.$notify.info({
          title: '提示',
          message: '权限已清除，请重新获取',
          duration: 3000,
        })
      } catch (error) {
        // 用户取消操作
      }
    },

    // 前往登录页面
    goToLogin() {
      window.location.href = '/pro/admin/login'
    },

    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      })
    },
  },
  mounted() {
    // 页面加载时检查权限状态
    this.checkAccessStatus()
  },
  beforeRouteEnter(to, from, next) {
    if (['dev', 'uat'].includes(env.env)) {
      next()
    } else {
      next({ name: 'wall-list' })
    }
  },
}
</script>

<style scoped lang="scss">
.debug-access-page {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: 'Microsoft YaHei', Arial, Helvetica, 'Avenir', sans-serif;
  overflow: hidden;

  /* 动态渐变背景层 */
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(135deg, #f2f6ff 0%, #e4edff 50%, #d6e4ff 100%);
    background-size: 400% 400%;
    filter: blur(60px);
    z-index: -2;
  }

  /* 模糊遮罩层 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(242, 246, 255, 0.3) 0%, rgba(228, 237, 255, 0.2) 50%, rgba(214, 228, 255, 0.3) 100%);
    backdrop-filter: blur(20px);
    z-index: -1;
  }
}

.access-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 32px;
  max-width: 520px;
  width: 100%;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(228, 237, 255, 0.6);
}

.header {
  text-align: center;
  margin-bottom: 24px;

  h2 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 22px;
    font-weight: 600;
  }

  .env-badge {
    background: #f2f6ff;
    color: #4886ff;
    padding: 6px 14px;
    border-radius: 16px;
    font-size: 12px;
    display: inline-block;
    border: 1px solid #e4edff;
    font-weight: 500;
  }
}

.status-section {
  margin-bottom: 24px;

  .status-info {
    background: #f8fafe;
    border-radius: 8px;
    padding: 18px;
    border-left: 3px solid #4886ff;
    border: 1px solid #e4edff;
  }

  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-weight: 500;
      color: #666;
      font-size: 14px;
    }

    .status-value {
      font-weight: 600;
      font-size: 14px;

      &.success {
        color: #67c23a;
      }

      &.error {
        color: #f56c6c;
      }

      &.warning {
        color: #e6a23c;
      }

      &.info {
        color: #409eff;
      }
    }
  }
}

.action-section {
  margin-bottom: 24px;
  text-align: center;

  .el-button {
    margin: 6px 8px;
    min-width: 140px;
    border-radius: 6px;
    font-weight: 500;

    &.el-button--large {
      padding: 12px 24px;
      font-size: 14px;
    }

    &.el-button--success {
      background-color: #4886ff;
      border-color: #4886ff;

      &:hover {
        background-color: #3c7dff;
        border-color: #3c7dff;
      }
    }

    &.el-button--primary {
      background-color: #4886ff;
      border-color: #4886ff;

      &:hover {
        background-color: #3c7dff;
        border-color: #3c7dff;
      }
    }
  }
}

.info-section {
  padding-top: 18px;
  border-top: 1px solid #e4edff;

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 13px;
    color: #999;
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }

    i {
      margin-right: 8px;
      font-size: 14px;
      flex-shrink: 0;

      &.el-icon-warning {
        color: #e6a23c;
      }

      &.el-icon-info {
        color: #4886ff;
      }

      &.el-icon-time {
        color: #909399;
      }
    }
  }
}
</style>
