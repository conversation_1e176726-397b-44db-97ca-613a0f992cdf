import api from './api'
export default {
  //摇号抽奖导入分组配置
  update: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3import/update.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3import/batch.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3import/list.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3import/page.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3import/add.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3import/delete.htm', postData),
  cleardata: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3import/cleardata.htm', postData),
  count: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3import/count.htm', postData),
  addNumOptions: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3import/addNumOptions.htm', postData),
}
