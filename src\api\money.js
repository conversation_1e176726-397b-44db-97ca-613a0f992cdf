import api from './api'
export default {
  page: postData => api.fetchBaseData('/pro/hxc/promoney/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/promoney/list.htm', postData),
  insert: postData => api.fetchBaseData('/pro/hxc/promoney/insert.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/promoney/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/promoney/update.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/promoney/delete.htm', postData),
}
