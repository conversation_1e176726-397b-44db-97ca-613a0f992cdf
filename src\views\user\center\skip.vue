<template>
  <div class="skip-wrap"></div>
</template>

<script>
import api from '@/api'
export default {
  data: function () {
    return {
      token: '',
    }
  },
  mounted() {
    this.token = window.location.href.split('=')[1]
    this.bindEmail.call(this)
    this.$router.push({
      name: 'user-center-accountinfo',
    })
  },
  methods: {
    async bindEmail() {
      try {
        await api.user.boundEmail({
          token: this.token,
        })
      } catch (err) {
        this.$message.error(err.msg)
      }
    },
  },
}
</script>

<style scoped></style>
