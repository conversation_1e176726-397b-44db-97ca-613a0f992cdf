import api from './api'
export default {
  groupByVoteId: postData => api.fetchBaseData('/pro/hxc/prowallvoteresult/groupByVoteId.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/prowallvoteresult/batch.htm', postData),
  groupBySuboptinId: postData => api.fetchBaseData('/pro/hxc/prowallvoteresult/groupBySuboptinId.htm', postData),
  detailPage: postData => api.fetchBaseData('/pro/hxc/prowallvoteresult/detailPage.htm', postData),
  ranking: postData => api.fetchBaseData('/pro/hxc/prowallvoteresult/ranking.htm', postData),
  // 票数查询接口
  count: postData => api.fetchBaseData('/pro/hxc/prowallvoteresult/count.htm', postData),
  // 投票人查询接口
  page: postData => api.fetchBaseData('/pro/hxc/prowallvoteresult/page.htm', postData),
  clearData: postData => api.fetchBaseData('/pro/hxc/prowallvoteresult/cleardata.htm', postData),
  // 和detailPage类似，数据稍有差异
  detailPage2: postData => api.fetchBaseData('/pro/hxc/prowallvoteresult/detailpage2.htm', postData),
  // 导出详细excel使用
  list: postData => api.fetchBaseData('/pro/hxc/prowallvoteresult/list.htm', postData),
}
