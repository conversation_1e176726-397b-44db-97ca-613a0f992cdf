<template>
  <div>
    <el-dialog title="编辑" :visible.sync="showDialog" width="960px" :close-on-click-modal="false" @close="$emit('close')">
      <div class="flex">
        <div v-if="['image', 'audio', 'backboard', 'avatar'].includes(activeName)" class="video-wrap">
          <video
            v-if="themeData.webBgtype === 'VIDEO'"
            autoplay="autoplay"
            loop="loop"
            muted
            :src="themeData.webBgvideo"
            style="width: 100%; height: 100%"
          ></video>
          <img v-else :src="themeData.webBgimg" style="width: 100%; height: 100%" alt="" />
          <div v-if="activeName === 'avatar'" class="avatar__group">
            <div
              v-if="themeData.headShowType === 'UNIFY'"
              class="avatar__item"
              :style="{ backgroundColor: themeData.headBgColor, color: themeData.headNickNameColor }"
            >
              昵称
            </div>
            <template v-else>
              <div v-for="item in _headBgColors" :style="{ backgroundColor: item }" :key="item" class="avatar__item">昵称</div>
            </template>
          </div>
          <div v-else-if="activeName === 'backboard'" class="backboard">
            <div v-if="themeData.themeType === 'SIMPLE'" class="simple">
              <div
                class="simple-item"
                :style="{
                  backgroundColor: themeData.bgColor,
                }"
              >
                <div
                  class="awards__text"
                  :style="{
                    backgroundColor: themeData.bgWordColor,
                  }"
                ></div>
              </div>
              <div
                class="simple-center"
                :style="{
                  backgroundColor: themeData.bgColor,
                  color: themeData.bgWordColor,
                }"
              >
                *****
              </div>
            </div>
            <div v-else class="awards">
              <div class="left" :style="_awardsBgColor('left')">
                <div
                  class="awards__text"
                  :style="{
                    backgroundColor: themeData.bgWordColor,
                  }"
                ></div>
              </div>
              <div class="center" :style="_awardsBgColor('center')">
                <div class="avatar">头像</div>
                <div class="description" :style="{ color: themeData.bgWordColor }">用户昵称</div>
              </div>
              <div v-if="_awardsBgColor('right').maskImage" class="right" :style="_awardsBgColor('right')">
                <div class="right-list">
                  <div v-for="item in 7" :key="item" style="margin-top: 9px">
                    <div class="avatar"></div>
                  </div>
                  <div class="avatar__text" :style="{ backgroundColor: themeData.bgWordColor }"></div>
                </div>
              </div>
            </div>
          </div>
          <img v-else :src="themeData.image" alt="" />
        </div>
        <div v-if="activeName === 'mobile'" class="displayStand">
          <div style="width: 300px">
            <div class="mobile-view" :style="`background-image: url(${themeData.mobileBgImg});`">
              <div v-if="!isMerchants" class="maskRegional positioning">广告区</div>
            </div>
          </div>
        </div>
        <div class="flex-1">
          <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="背景图" name="image">
              <el-form label-width="110px" label-position="left">
                <el-form-item label="大屏幕背景：">
                  <el-radio-group v-model="themeData.webBgtype">
                    <el-radio label="IMG">图片</el-radio>
                    <el-radio label="VIDEO">视频</el-radio>
                  </el-radio-group>
                  <div class="flex flex-a-fe">
                    <div class="pic-box">
                      <img v-if="themeData.webBgimg && themeData.webBgtype === 'IMG'" v-hiimg="themeData.webBgimg" />
                      <video
                        v-else-if="themeData.webBgvideo && themeData.webBgtype === 'VIDEO'"
                        :src="themeData.webBgvideo"
                        autoplay
                        loop
                        muted
                      ></video>
                      <div v-else class="blank">请上传</div>
                    </div>
                    <div class="mrg-l-10">
                      <hi-size-specification
                        placement="right-start"
                        :note="
                          themeData.webBgtype === 'VIDEO'
                            ? { 推荐视频比例: '1280*800(8:5)或1920*1080(16:9)', 大小: '小于20M', 格式: `MP4` }
                            : {
                                推荐图片比例: '1280*800(8:5)或1920*1080(16:9)',
                                大小: '小于5M',
                                格式: `jpg/bmp/png/gif/webp`,
                              }
                        "
                      >
                        <div slot="top" class="tac f999 font-12 mrg-b-10">
                          请上传与现场实际大屏幕投影分辨率尺寸比例相同的{{ themeData.webBgtype === 'VIDEO' ? '视频' : '图片' }}
                        </div>
                        <div slot="bottom" class="tac f999 font-12 mrg-t-10">(请按现场实际效果设计)</div>
                      </hi-size-specification>
                    </div>
                  </div>
                  <div class="mrg-t-20 flex">
                    <hi-upload-img
                      v-if="themeData.webBgtype === 'IMG'"
                      :disabled="isUpload"
                      class="mrg-r-10"
                      :action="$hi.url.upload"
                      :show-file-list="false"
                      :on-success="uploadImageSuccess"
                      :on-error="() => (isUpload = false)"
                    >
                      <el-button type="primary" :loading="isUpload">上传替换</el-button>
                    </hi-upload-img>
                    <hi-upload-media
                      v-else
                      :disabled="isUpload"
                      class="mrg-r-10"
                      :action="$hi.url.uploadvideo"
                      accept="video/mp4"
                      :show-file-list="false"
                      :on-success="uploadVideoSuccess"
                      :on-error="uploadError"
                    >
                      <el-button type="primary" :loading="isUpload">上传替换</el-button>
                    </hi-upload-media>
                    <el-button @click="resetConfig(`${themeData.webBgtype === 'IMG' ? 'webBgimg' : 'webBgvideo'}`)">恢复默认 </el-button>
                  </div>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="移动端背景" name="mobile">
              <div class="editArea">
                <el-form label-width="110px" label-position="left">
                  <el-form-item label="移动端背景图：">
                    <div class="flex">
                      <div class="card-box">
                        <img :src="themeData.mobileBgImg" alt="" />
                      </div>
                      <div class="mrg-l-10">
                        <hi-upload-img
                          :action="$hi.url.upload"
                          :on-success="(v) => (themeData.mobileBgImg = Hi.String.dealUrl(v.data.url))"
                          :show-file-list="false"
                          :maxSize="5"
                        >
                          <p class="mrg-t-10">
                            <el-button type="primary">上传替换</el-button>
                          </p>
                          <p class="mrg-t-10">
                            <el-button @click.stop="themeData.mobileBgImg = mobileBgDefault">恢复默认</el-button>
                          </p>
                        </hi-upload-img>
                        <hi-size-specification class="mrg-t-5" mode="image"> </hi-size-specification>
                        <template v-if="!isMerchants">
                          <div class="maskRegional">广告区</div>
                          <p class="sub">
                            固定广告区，可前往
                            <router-link :to="{ name: 'wall-info-copyrightad', query: $route.query }" target="_blank"> 第三方广告 </router-link>
                            关闭
                          </p>
                        </template>
                      </div>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane>
            <el-tab-pane label="底板设置" name="backboard">
              <el-form label-width="138px">
                <el-form-item label="底板颜色及透明度：">
                  <el-color-picker v-model="themeData.bgColor" show-alpha :predefine="predefineColors"></el-color-picker>
                </el-form-item>
                <el-form-item label="字体颜色及透明度：">
                  <el-color-picker v-model="themeData.bgWordColor" show-alpha :predefine="predefineColors"></el-color-picker>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="背景音乐" name="audio">
              <strong>抽奖前音乐</strong>
              <div class="flex mrg-t-10 mrg-b-20">
                <hi-audio-player :src="themeData.waitMusic"></hi-audio-player>
                <hi-upload-audio
                  :before-upload="(v) => (this.waitMusicLoading = true)"
                  :on-success="uploadWaitMusicSuccess"
                  :on-error="(v) => (this.waitMusicLoading = false)"
                  class="mrg-r-10"
                  :disabled="waitMusicLoading"
                >
                  <el-button type="primary" class="mrg-l-10" :loading="waitMusicLoading">上传替换</el-button>
                </hi-upload-audio>
                <el-button class="mrg-l-10" @click="themeData.waitMusic = `${env.libdomain}web-admin/lottery/waitMusic.mp3`">恢复默认</el-button>
              </div>
              <strong>抽奖进行中音乐</strong>
              <div class="flex mrg-t-10 mrg-b-20">
                <hi-audio-player :src="themeData.inMusic"></hi-audio-player>
                <hi-upload-audio
                  :before-upload="(v) => (this.inMusicLoading = true)"
                  :on-success="uploadInMusicSuccess"
                  :on-error="(v) => (this.inMusicLoading = false)"
                  class="mrg-r-10"
                  :disabled="inMusicLoading"
                >
                  <el-button type="primary" class="mrg-l-10" :loading="inMusicLoading">上传替换</el-button>
                </hi-upload-audio>
                <el-button class="mrg-l-10" @click="themeData.inMusic = `${env.libdomain}web-admin/lottery/inMusic.mp3`">恢复默认</el-button>
              </div>
              <strong>抽奖结果音乐</strong>
              <div class="flex mrg-t-10 mrg-b-20">
                <hi-audio-player :src="themeData.endMusic"></hi-audio-player>
                <hi-upload-audio
                  :before-upload="(v) => (this.endMusicLoading = true)"
                  :on-success="uploadEndMusicSuccess"
                  :on-error="(v) => (this.endMusicLoading = false)"
                  class="mrg-r-10"
                  :disabled="endMusicLoading"
                >
                  <el-button type="primary" class="mrg-l-10" :loading="endMusicLoading">上传替换</el-button>
                </hi-upload-audio>
                <el-button class="mrg-l-10" @click="themeData.endMusic = `${env.libdomain}web-admin/lottery/endMusic.mp3`">恢复默认</el-button>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="flex flex-j-fe mrg-t-30">
        <el-button type="primary" :disabled="_isDisabled" @click="handleThemeSave">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api'
import { Hi, env } from '@/libs/common'
import { themeOptions } from './themedata'
import { elementConfigMixin } from '@/libs/mixins'
import { mapGetters } from 'vuex'

export default {
  name: 'theme',
  props: {
    themeConfig: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  mixins: [elementConfigMixin],
  data() {
    return {
      Hi,
      env,
      themeData: {}, // 主题数据
      originThemeData: {}, //  主题数据原始数据
      showDialog: true, // 是否显示编辑弹窗
      waitMusicLoading: false,
      inMusicLoading: false,
      endMusicLoading: false,
      activeName: 'image',
      isUpload: false,
      mobileBgDefault: 'https://res3.hixianchang.com/qn/up/738bd64a9fb2a04a7be8e029eed636d9.png',
    }
  },
  computed: {
    ...mapGetters({
      isMerchants: 'user/isMerchants',
    }),
    _isDisabled() {
      return !Hi.Object.difference(this.originThemeData, this.themeData)
    },
    _headBgColors: {
      get() {
        if (typeof this.themeData.headBgColors === 'string') {
          return JSON.parse(this.themeData.headBgColors)
        }
        return this.themeData.headBgColors
      },
      set(val) {
        this.themeData.headBgColors = JSON.stringify(val)
      },
    },
    // 抽奖底色
    _awardsBgColor() {
      return (k) => {
        let theme = 'default_1'
        if (['HEXINCHUN', 'LUCKYFIREWORKS', 'HAPPYNEWYEAR', 'TIMESHIFT', 'TIMEROTATE', 'GALAXY'].includes(this.themeData.themeType)) {
          return {
            backgroundColor: this.themeData.bgColor,
            margin: '0 2px',
            border: '1px solid #FFCD16',
            borderRadius: '4px',
            padding: '2px',
            borderSpacing: '10px',
          }
        }
        return {
          maskImage: `url(${require(`@/assets/wall/interact/lotlottery/${theme}/${k}.png`)}`,
          backgroundColor: this.themeData.bgColor,
        }
      }
    },
  },
  methods: {
    uploadError(err) {
      this.isUpload = false
      this.$notify.error({
        title: '错误',
        message: JSON.parse(err.message).msg || '上传出错，请换个文件再试',
      })
    },
    //抽奖前音乐上传
    uploadWaitMusicSuccess(file) {
      const url = Hi.String.dealUrl(file.data.url)
      this.waitMusicLoading = false
      this.themeData.waitMusic = url
    },
    //抽奖进行中音乐上传
    uploadInMusicSuccess(file) {
      const url = Hi.String.dealUrl(file.data.url)
      this.inMusicLoading = false
      this.themeData.inMusic = url
    },
    //抽奖结果音乐上传
    uploadEndMusicSuccess(file) {
      const url = Hi.String.dealUrl(file.data.url)
      this.endMusicLoading = false
      this.themeData.endMusic = url
    },
    // 上传图片成功
    uploadImageSuccess(file) {
      const url = Hi.String.dealUrl(file.data.url)
      this.isUpload = false
      this.themeData.webBgimg = url
    },
    // 上传视频成功
    uploadVideoSuccess(file) {
      const url = Hi.String.dealUrl(file.data.url)
      this.isUpload = false
      this.themeData.webBgvideo = url
    },
    resetConfig(key) {
      const themeOption = themeOptions.find((item) => item.themeType === this.themeData.themeType)
      if (themeOption) {
        this.themeData[key] = themeOption[key]
      }
    },
    // 保存主题编辑
    async handleThemeSave() {
      const update = Hi.Object.difference(this.originThemeData, this.themeData)
      if (update) {
        await api.walllotterytheme.update({
          where: { id: this.themeData.id },
          update,
        })
      }
      this.$notify.success({ title: '成功', message: '保存成功' })
      this.showDialog = false
    },
    handleSelectColor(color) {
      this._headBgColors = [...this._headBgColors, color]
    },
    handleHeadBgColorsRemove(index) {
      this._headBgColors = this._headBgColors.filter((item, i) => i !== index)
    },
  },
  async mounted() {
    this.themeData = Hi.Object.copy(this.themeConfig)
    this.originThemeData = Hi.Object.copy(this.themeConfig)
  },
}
</script>

<style lang="stylus" scoped>
.sub {
  color: #999;
  font-size: 12px;
}
.theme-zone {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  &:first-child {
    margin-top: -20px;
  }
}

.cell {
  position: relative;
  margin: 30px 20px 20px;
  width: 204px;
  height: auto;
}

.theme-pic {
  position: relative;
  width: 100%;
  height: 124px;
  box-sizing: border-box;
  border: 2px solid #d1dbe5;
  cursor: pointer;

  > img {
    display: inline-block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  &.checked {
    border: 2px solid #4886ff;
  }
}

.triangle {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-top: 30px solid transparent;
  border-left: 30px solid transparent;

  > i {
    position: absolute;
    top: -26px;
    right: -2px;
    color: transparent;
    font-size: 12px;
  }
}

.checked .triangle {
  border-top: 30px solid #4886ff;

  > i {
    color: #fff;
  }
}

.theme-name {
  margin-top: 5px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;

  > .el-button {
    margin-left: 8px;
  }
}

.theme-price {
  z-index: 2
}

.is-disabled {
  pointer-events: none;
  user-select: none;

  .theme-pic.checked {
    border: 2px solid #999;
  }

  .checked .triangle {
    border-top: 30px solid #999;
  }
}

video {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.new-pic {
  position: absolute;
  left: 0;
  top: 0;
}

// 主题编辑
.video-wrap {
  width: 280px;
  height: 152px;
  margin-right: 20px;
  flex-shrink: 0;
  border: 1px solid #ebeef5;
  position: relative;

  img, video {
    position: absolute;
    z-index: 0;
  }

  img {
    width: 100%;
    height: auto;
  }

  .avatar__group {
    position: absolute;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    display: flex;
    height: 100%;
    width: 100%;
    z-index: 1;

    .avatar__item {
      width: 40px;
      height: 40px;
      margin: 0 5px;
      background-color: #409EFF;
      border-radius: 50%;
      font-size: 15px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .backboard {
    position: relative;
    width: 100%;
    height: 100%;
  }
}

.pic-box {
  padding: 2px;
  width: 192px;
  height: 120px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  background-clip: content-box;
  position: relative;

  .mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
  }
}

.displayStand {
  maring-top: 20px;
  display: flex;

  .mobile-view {
    width: 240px;
    height: 426px;
    border-radius: 4px;
    position: relative;
    margin: 0 auto;
    background-size: 100% 100%;
    background-position: center;
  }
}

.card-box {
  width: 209px;
  min-height: 350px;
  padding: 8px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-clip: content-box;

  img {
    width: 100%;
    height: 100%;
  }
}

.maskRegional {
  width: 151px;
  height: 74px;
  border: 2px dashed #858789;
  color: #858789;
  font-size: 20px;
  line-height: 74px;
  text-align: center;
  font-weight: 400;
  border-radius: 0.06rem;
}

.positioning {
  position: absolute;
  left: 28px;
  bottom: 77px;
}

.color__list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  align-items: center;
}

.color__item {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  &:hover {
    .close-button {
      display: block;
      background-color: rgba(0, 0, 0, 0.5);
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.close-button {
  width: 30px;
  height: 30px;
  display: none;
  line-height: 30px;
  color: #fff;
  font-size: 16px;
}

.color__item--add {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
  cursor: pointer;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #858789;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .picker {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
  }
}

.awards {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .left {
    width: 20%;
    height: 85%;
    mask-size: 100% 100%;
    position: relative;
  }

  .center {
    width: 50%;
    height: 85%;
    mask-size: 100% 100%;
    position: relative;

    .avatar {
      width: 50px;
      height: 50px;
      background-color: #fff;
      border-radius: 50%;
      margin: 20% auto 0;
      text-align: center;
      line-height: 50px;
    }

    .description {
      margin-top: 10px;
      text-align: center;
      font-size: 10px;
    }
  }

  .right {
    width: 20%;
    height: 85%;
    mask-size: 100% 100%;
    position: relative;

    .right-list {
      width: 80%;
      font-size: 10px;
      text-align: center;
      margin-top: 22px;
      margin-left: 2px;

      .avatar {
        width: 6px;
        height: 6px;
        background-color: #fff;
        border-radius: 50%;
        margin-left: 3px;
      }

      .description {
        margin-left: 5px;
      }
    }
  }
}

.simple {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;

  .simple-item {
    width: 20%;
    height: 85%;
    margin-left: 10%;
    position: relative;
  }

  .simple-center {
    width: 50%;
    height: 30%;
    margin-left: 10px;
    position: relative;
    border-radius: 4px;
    text-align: center;
    line-height: 50px;
    letter-spacing: 5px;
  }
}

.awards__text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-44%, -52%);
  width: 50px;
  height: 110px;
  mask-image: url('https://res3.hixianchang.com/qn/up/df41e63893a6c66f33a4a881f1caab78.png');
  mask-size: contain;
  mask-repeat: no-repeat;
  background-color: #fff;
}

.avatar__text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-44%, -52%);
  width: 50px;
  height: 110px;
  mask-image: url('https://res3.hixianchang.com/qn/up/944cfa2e1ba5ec9ef214404636ee90a5.png');
  mask-size: contain;
  mask-repeat: no-repeat;
  background-color: #fff;
}
</style>
