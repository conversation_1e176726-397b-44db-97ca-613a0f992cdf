import api from './api'
export default {
  add: (postData) => api.fetchBaseData('/pro/hxc/proresultvirtual/add.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proresultvirtual/update.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/proresultvirtual/read.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proresultvirtual/list.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/proresultvirtual/page.htm', postData),
}
