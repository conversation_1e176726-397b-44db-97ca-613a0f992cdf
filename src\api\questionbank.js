import api from './api'

export default {
  // 题目管理
  list: (postData) => api.fetchBaseData('/pro/hxc/proquestionbank/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proquestionbank/add.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proquestionbank/update.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proquestionbank/delete.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/proquestionbank/read.htm', postData),
  
  // 批量操作
  batch: (postData) => api.fetchBaseData('/pro/hxc/proquestionbank/batch.htm', postData),
  
  // 导入导出
  import: (postData) => api.fetchBaseData('/pro/hxc/proquestionbank/import.htm', postData),
  export: (postData) => api.fetchBaseData('/pro/hxc/proquestionbank/export.htm', postData),
  
  // 统计查询
  count: (postData) => api.fetchBaseData('/pro/hxc/proquestionbank/count.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/proquestionbank/page.htm', postData),
  
  // 分组管理
  groupList: (postData) => api.fetchBaseData('/pro/hxc/proquestionbankgroup/list.htm', postData),
  groupAdd: (postData) => api.fetchBaseData('/pro/hxc/proquestionbankgroup/add.htm', postData),
  groupUpdate: (postData) => api.fetchBaseData('/pro/hxc/proquestionbankgroup/update.htm', postData),
  groupDelete: (postData) => api.fetchBaseData('/pro/hxc/proquestionbankgroup/delete.htm', postData),
  
  // 选项管理
  optionList: (postData) => api.fetchBaseData('/pro/hxc/proquestionbankoption/list.htm', postData),
  optionBatch: (postData) => api.fetchBaseData('/pro/hxc/proquestionbankoption/batch.htm', postData)
}
