import api from './api'
export default {
  add: (v) => api.fetchBaseData('/pro/hxc/propiclottery/add.htm', v),
  read: (v) => api.fetchBaseData('/pro/hxc/propiclottery/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/propiclottery/update.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/propiclottery/list.htm', v),
  sort: (v) => api.fetchBaseData('/pro/hxc/propiclottery/sort.htm', v),
}
