import api from './api'
export default {
  //订货会-商品
  read: postData => api.fetchBaseData('/pro/hxc/proplaceordergoods/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proplaceordergoods/update.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/proplaceordergoods/add.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proplaceordergoods/list.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/proplaceordergoods/delete.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/proplaceordergoods/page.htm', postData),
  sort: postData => api.fetchBaseData('/pro/hxc/proplaceordergoods/sort.htm', postData),
}
