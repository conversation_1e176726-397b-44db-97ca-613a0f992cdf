<template>
  <hi-wall-set-right>
    <div slot="content">
      <!-- 顶部操作栏 -->
      <el-button class="back-button" @click="$emit('toList')">&lt; 返回轮次列表</el-button>

      <!-- 筛选和分组管理 -->
      <div class="filter-section">
        <div class="filter-left">
          <span class="filter-label">分组</span>
          <el-select class="w-200" v-model="selectedGroup" placeholder="全部" @change="handleGroupChange">
            <el-option label="全部" value=""></el-option>
            <el-option v-for="group in groupList" :key="group.id" :label="group.name" :value="group.id"></el-option>
          </el-select>
          <div>
            <el-button type="primary" @click="showGroupDialog = true">分组管理</el-button>
            <el-button type="primary" @click="handleClearAll">清空名单列表</el-button>
            <el-button type="danger" :disabled="selectedItems.length === 0" @click="handleDeleteSelected">删除</el-button>
          </div>
        </div>
        <div class="filter-right">
          <el-button type="primary" @click="showAddDialog = true">添加名单</el-button>
          <el-button type="primary" @click="showImportDialog = true">导入名单</el-button>
        </div>
      </div>

      <!-- 名单列表 -->
      <el-table
        :key="componentKey"
        class="w-full"
        v-loading="tableLoading"
        :data="pageData.dataList"
        border
        @selection-change="handleSelectionChange"
        row-key="id"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <template v-for="item in stageList">
          <el-table-column v-if="['d1', 'd2', 'd3'].includes(item.key)" :prop="item.key" :label="item.name"></el-table-column>
        </template>
        <el-table-column label="分组" key="group">
          <template v-slot="{ row }">
            <div v-if="importGroupMap[row.id]" class="gap">
              <span v-for="item in importGroupMap[row.id]" :key="item.groupId" class="tag">{{ item.groupName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" key="operation">
          <template v-slot="{ row }">
            <el-button class="red" type="text" size="small" @click="handleDeleteItem(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <hi-page :pageData="pageData" @renderPage="renderPage" />

      <!-- 添加名单弹窗 -->
      <add-dialog
        :visible.sync="showAddDialog"
        :config="config"
        :stage-list="stageList"
        :group-list="groupList"
        @refresh="refresh"
        @upgrade="$emit('upgrade')"
      />

      <!-- 导入名单弹窗 -->
      <import-dialog
        :visible.sync="showImportDialog"
        :config="config"
        :stage-list="stageList"
        :group-list="groupList"
        @refresh="refresh"
        @upgrade="$emit('upgrade')"
      />

      <!-- 分组管理弹窗 -->
      <group-dialog
        :visible.sync="showGroupDialog"
        :config="config"
        :stage-list="stageList"
        :group-list="groupList"
        @refresh="refresh"
        @upgrade="$emit('upgrade')"
      />
    </div>
  </hi-wall-set-right>
</template>

<script>
import api from '@/api'
import AddDialog from './addDialog.vue'
import ImportDialog from './importDialog.vue'
import GroupDialog from './groupDialog.vue'
import { pageMixin } from '@/libs/mixins'

export default {
  name: 'SegLotteryManage',
  inject: ['wall'],
  mixins: [pageMixin],
  components: {
    AddDialog,
    ImportDialog,
    GroupDialog,
  },
  props: {
    config: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      tableLoading: false,
      selectedItems: [],
      selectedGroup: '',
      seglotteryImportIdList: [],
      groupList: [], // 分组列表
      stageList: [], // 分段列表
      importGroupList: [], // 导入分组列表

      showAddDialog: false,
      showImportDialog: false,
      showGroupDialog: false,

      componentKey: 0,
    }
  },
  computed: {
    importGroupMap() {
      return this.importGroupList.reduce((map, item) => {
        ;(map[item.seglotteryImportId] = map[item.seglotteryImportId] || []).push({
          ...item,
          groupName: this.groupList.find((group) => group.id === item.groupId)?.name,
        })
        return map
      }, {})
    },
  },
  methods: {
    async refresh() {
      this.tableLoading = true
      await Promise.all([this.fetchStageList(), this.fetchGroupList(), this.fetchImportGroupList(), this.renderPage()])
      this.tableLoading = false
    },
    // 获取名单数据
    async renderPage() {
      try {
        const where = { wallId: this.wall.id }
        if (this.selectedGroup) {
          if (this.seglotteryImportIdList.length > 0) {
            where.idList = this.seglotteryImportIdList
          } else {
            this.pageData.pageIndex = 1
            this.pageData.pageSize = 10
            this.pageData.total = 0
            this.pageData.dataList = []
            return
          }
        }

        const data = await api.seglotteryimport.page({
          where,
          pageIndex: this.pageData.pageIndex,
          pageSize: this.pageData.pageSize,
        })

        this.pageData = data
      } catch (error) {
        console.error('获取名单数据失败:', error)
        this.$notify.error({ title: '错误', message: '获取名单数据失败' })
      }
    },

    // 获取分组列表
    async fetchGroupList() {
      try {
        const data = await api.seglotterygroup.list({
          where: { wallId: this.wall.id },
          sort: { id: 'asc' },
        })
        this.groupList = data
      } catch (error) {
        console.error('获取分组列表失败:', error)
        this.groupList = []
      }
    },

    // 获取分段列表
    async fetchStageList() {
      try {
        const data = await api.seglotterystage.list({ where: { wallId: this.wall.id } })
        this.stageList = data
      } catch (error) {
        console.error('获取分段列表失败:', error)
        this.stageList = []
      }
    },

    // 获取导入分组列表
    async fetchImportGroupList() {
      try {
        const data = await api.seglotteryimportgroup.list({ where: { wallId: this.wall.id } })
        this.importGroupList = data
      } catch (error) {
        console.error('获取导入分组列表失败:', error)
        this.importGroupList = []
      }
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedItems = selection
    },

    // 分组筛选变化
    handleGroupChange(id) {
      if (id) {
        const group = this.importGroupList.filter((item) => item.groupId === id)
        this.seglotteryImportIdList = group.map((item) => item.seglotteryImportId)
      }
      this.pageData.pageIndex = 1
      this.renderPage()
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pageData.pageSize = val
      this.pageData.pageIndex = 1
      this.renderPage()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pageData.pageIndex = val
      this.renderPage()
    },

    // 删除选中项
    async handleDeleteSelected() {
      if (this.selectedItems.length === 0) {
        this.$message.warning('请先选择要删除的项')
        return
      }

      try {
        await this.$confirm(`确定删除选中的 ${this.selectedItems.length} 条数据吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        const idsToDelete = this.selectedItems.map((item) => item.id)
        await api.seglotteryimport.delete({
          where: { wallId: this.wall.id, idList: idsToDelete },
        })

        this.$notify.success({ title: '成功', message: '删除成功' })
        this.renderPage()
      } catch (err) {
        if (err !== 'cancel') {
          console.error(err)
          this.$notify.error({ title: '错误', message: '删除失败' })
        }
      }
    },

    // 删除单项
    async handleDeleteItem(row) {
      try {
        await this.$confirm('删除后无法恢复，是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        await api.seglotteryimport.delete({
          where: { wallId: this.wall.id, idList: [row.id] },
        })

        this.$notify.success({ title: '成功', message: '删除成功' })
        this.renderPage()
      } catch (err) {
        if (err !== 'cancel') {
          console.error(err)
          this.$notify.error({ title: '错误', message: '删除失败' })
        }
      }
    },

    // 清空所有数据
    async handleClearAll() {
      try {
        await this.$confirm('清除后无法恢复，确定清除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        await api.seglotteryimport.cleardata({ where: { wallId: this.wall.id } })
        this.$notify.success({ title: '成功', message: '清除列表成功' })
        this.renderPage()
      } catch (err) {
        if (err !== 'cancel') {
          console.error(err)
          this.$notify.error({ title: '错误', message: '清除列表失败' })
        }
      }
    },
  },

  async mounted() {
    try {
      await this.refresh()
    } catch (error) {
      console.error('初始化数据失败:', error)
      this.$notify.error({ title: '错误', message: '初始化数据失败' })
    }
  },
}
</script>

<style scoped lang="scss">
.back-button {
  margin-bottom: 20px;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .filter-left {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.gap {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}
.tag {
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
}
</style>
