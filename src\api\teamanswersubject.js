import api from './api';

export default {
  insert: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubject/insert.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubject/delete.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubject/read.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubject/update.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubject/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubject/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubject/add.htm', postData),
  import: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubject/import.htm', postData),
  sort: (postData) => api.fetchBaseData('/pro/hxc/proteamanswersubject/sort.htm', postData),
};
