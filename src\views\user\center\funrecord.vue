<template>
  <div class="fun-record-box" v-loading="loading">
    <p class="detail flex flex-a-c flex-j-fe">
      <el-select v-model="vatKey" placeholder="请选择">
        <el-option v-for="item in funcList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select>
    </p>
    <el-table :data="pageData.dataList" border>
      <el-table-column prop="key" label="功能" :width="270" :formatter="formatfFun"></el-table-column>
      <el-table-column label="活动名称" :formatter="formatTheme"></el-table-column>
      <el-table-column prop="description" label="详情"></el-table-column>
      <el-table-column prop="changeCount" label="次数">
        <template slot-scope="scope">
          <p :class="color(scope.row.changeCount)"><span v-if="scope.row.changeCount > 0">+</span>{{ scope.row.changeCount }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="createDate" label="操作时间" :width="180"></el-table-column>
    </el-table>
    <hi-page :pageData="pageData" @renderPage="renderPage"></hi-page>
  </div>
</template>
<script>
import api from '@/api'
import { pageMixin } from '@/libs/mixins'
export default {
  name: 'funRecord',
  mixins: [pageMixin],
  props: ['allFunction', 'priceObj'],
  data() {
    return {
      loading: true,
      vatKey: 'all',
      idList: [],
      wallObj: {},
      pageWhere: {},
      funcList: [
        {
          value: 'all',
          label: '全部功能',
        },
      ],
    }
  },
  watch: {
    vatKey(v) {
      if (v === 'all') {
        this.pageWhere.key && delete this.pageWhere.key
      } else {
        this.$set(this.pageWhere, 'key', v)
      }
      this.renderPage.call(this)
    },
  },
  methods: {
    formatfFun(r) {
      return this.getFunctionName(r.key)
    },
    formatTheme(r) {
      return r.wallId ? (this.wallObj[r.wallId] ? this.wallObj[r.wallId].theme || r.wallTheme : '-') : '-'
    },
    getFunctionName(key) {
      if (this.allFunction[key]) {
        return this.allFunction[key].vatName
      } else {
        return ''
      }
    },
    color(count) {
      return count < 0 ? 'green' : 'red'
    },
    // 增值功能次数列表
    async getFunctionList() {
      try {
        let listData = await api.userfunction.list({
          where: {},
          sort: {
            id: 'desc',
          },
        })
        listData.forEach((item) => {
          this.$set(item, 'price', this.priceObj[item.key])
        })
        let data = listData.filter((item) => {
          return item.price > 0 && item.key !== 'walltime'
        })
        this.funcList = [
          {
            value: 'all',
            label: '全部功能',
          },
        ]
        data.forEach((item) => {
          this.funcList.push({
            value: item.key,
            label: this.getFunctionName.call(this, item.key),
          })
        })
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    // 渲染分页
    async renderPage() {
      this.loading = true
      try {
        let data = await api.userfunction.page({
          where: this.pageWhere,
          sort: {
            id: 'desc',
          },
          pageIndex: this.pageData.pageIndex,
          pageSize: this.pageData.pageSize,
        })
        this.idList = []
        let originList = []
        let tempData =
          data.dataList.length &&
          data.dataList.filter((item) => {
            return item.wallId !== 0
          })
        tempData.length &&
          tempData.forEach((item) => {
            originList.push(item.wallId)
          })
        // 去重
        originList.length &&
          originList.forEach((item) => {
            if (this.idList.indexOf(item) == -1) {
              this.idList.push(item)
            }
          })
        this.idList.length && (await this.getWallInfo.call(this))
        this.pageData = data
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
      setTimeout(() => {
        this.loading = false
      }, 800)
    },
    // 关联活动
    async getWallInfo() {
      try {
        let wallInfo = await api.wall.allList({
          where: {
            idList: this.idList,
          },
          include: ['id', 'theme'],
        })
        this.wallObj = {}
        wallInfo.forEach((item) => {
          this.$set(this.wallObj, item.id, item)
        })
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
  },
  async mounted() {
    await this.getFunctionList.call(this)
    await this.renderPage.call(this)
  },
}
</script>
<style scoped lang="stylus">
.detail
  height 72px
  color #666
</style>
