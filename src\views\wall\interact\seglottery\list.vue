<template>
  <hi-wall-set-right class="wall-settery-box">
    <div slot="content">
      <div class="state-box">
        <el-tabs v-model="actState" type="card">
          <el-tab-pane label="主题选择" name="THEME">
            <HiTheme :config="config" @updateConfig="$emit('updateConfig')" @themeList="onThemeList" @create="onCreate"></HiTheme>
          </el-tab-pane>
          <el-tab-pane label="轮次列表" name="NORMAL">
            <hi-round-list-table
              :themeList="themeList"
              :config="config"
              :hiddenColumns="['limitCondition']"
              @edit="(row) => $emit('edit', row)"
              @create="actState = 'THEME'"
              @updateConfig="$emit('updateConfig')"
            ></hi-round-list-table>
          </el-tab-pane>
        </el-tabs>
        <div class="flex btns">
          <el-button @click="$emit('toManage')">名单管理</el-button>
          <el-button v-if="actState !== 'THEME'" type="primary" @click="actState = 'THEME'">新建抽奖</el-button>
        </div>
      </div>
    </div>
    <div slot="control" class="control">
      <hi-switch-big v-model="configObj.openState" active-value="Y" inactive-value="N" :disabled="false"></hi-switch-big>
      <div>
        <hi-party-way :wall="wall" route="wall-seglottery"></hi-party-way>
        <hi-to-pcwall :wall="wall"></hi-to-pcwall>
        <hi-to-result-control @click="$emit('toResult')"></hi-to-result-control>
      </div>
      <span></span>
    </div>
  </hi-wall-set-right>
</template>
<script>
import api from '@/api'
import { env, Hi } from '@/libs/common'
import HiRoundListTable from '@/views/wall/interact/common/roundListTable.vue'
import HiTheme from '@/views/wall/interact/common/themev3.vue'

export default {
  name: 'HiWallseglottery',
  components: { HiRoundListTable, HiTheme },
  inject: ['wall'],
  props: {
    config: {
      type: Object,
      default: () => ({}),
    },
  },
  provide() {
    return {
      elForm: this,
    }
  },
  data() {
    return {
      Hi,
      env,
      configObj: Hi.Object.copy(this.config),
      pageLoad: false,
      actState: 'NORMAL',
      isDel: false,
      dataList: [], //轮次列表
      loading: false,
      themeList: [],
    }
  },
  computed: {
    disabled() {
      return this.configObj.openState !== 'Y'
    },
  },
  watch: {
    'configObj.openState'(v) {
      let update = { openState: v }
      this.updateConfig.call(this, update)
    },
    async actState(v) {
      this.renderPage(v)
    },
  },
  methods: {
    getWhere() {
      let where = {
        wallId: this.wall.id,
      }
      return where
    },
    onThemeList(v) {
      this.themeList = v
    },
    async renderPage(key) {
      if (key === 'THEME') return
      try {
        !key && (this.pageLoad = true) //无key情况才加loading
        let dataList = await api.seglottery.list({
          where: this.getWhere(),
          sort: { sort: 'asc' },
        })
        this.dataList = dataList
      } catch (err) {
        console.error(err)
      }
      this.pageLoad = false
    },
    async updateConfig(update) {
      try {
        await api.seglotteryconfig.update({
          where: { id: this.configObj.id, wallId: this.configObj.wallId },
          update,
        })
        this.$notify({
          title: '成功',
          message: '更新成功',
          type: 'success',
        })
        this.$emit('updateConfig')
      } catch (err) {
        this.configObj[Object.keys(update)[0]] = this.config[Object.keys(update)[0]]
        this.$notify.error({
          title: '错误',
          message: err ? err.msg || '更新失败，请稍后重试！' : '更新失败，请稍后重试！',
        })
      }
    },
    onCreate(item) {
      this.$emit('edit', { themeId: item.id })
    },
  },
  async mounted() {
    let out = Hi.Exe.timeout(() => this.$emit('loaded'), 200)
    await this.renderPage.call(this)
    if (this.dataList.length > 0) {
      this.actState = 'NORMAL'
    } else {
      this.actState = 'THEME'
    }
    out()
  },
}
</script>
<style scoped lang="scss">
.wall-settery-box {
  min-width: 1100px;
}

.state-box {
  position: relative;

  .btns {
    position: absolute;
    right: 0;
    top: 4px;
  }
}

.control {
  padding-left: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
