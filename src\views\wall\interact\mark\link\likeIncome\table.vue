<template>
  <div>
    <el-table :data="pageData.dataList" v-loading="pageLoad" ref="table" border class="table" draggable="true">
      <el-table-column label="参评对象" prop="name">
        <template v-slot="{ row }">
          <div class="role">
            <p class="role-img">
              <img :src="row.headImg" />
            </p>
            <p class="role-name">{{ row.name }}<span v-if="row.deleteTag === 'Y'">(已删除)</span></p>
          </div>
        </template>
      </el-table-column>
      <template v-if="type === 'pay'">
        <el-table-column label="获得支持金额" :formatter="formatterPrize"></el-table-column>
        <el-table-column label="支持人数" :formatter="formatterPayNum"></el-table-column>
      </template>
      <template v-else>
        <el-table-column label="获得点赞个数" :formatter="formatterFreeCnt"></el-table-column>
        <el-table-column label="点赞人数" :formatter="formatterFreeNum"></el-table-column>
      </template>
      <el-table-column label="操作">
        <template v-slot="{ row }">
          <div class="operate">
            <span class="blue" @click="subjectInfo = row">详情</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <hi-detail v-if="subjectInfo" :type="type" :subjectInfo="subjectInfo" :markId="markId" @close="subjectInfo = null"> </hi-detail>
  </div>
</template>
<script>
import api from '@/api'
import { fenToYuan } from '@/libs/common'
import { pageMixin } from '@/libs/mixins'
import HiDetail from './detail'
export default {
  name: 'markkincomeTable',
  inject: ['wall'],
  mixins: [pageMixin],
  props: ['markId', 'type'],
  components: {
    HiDetail,
  },
  data() {
    return {
      pageLoad: false,
      reportObj: {}, //参评对象点赞和付费数据
      //查看详情对象
      subjectInfo: null,
    }
  },
  watch: {
    markId: {
      immediate: true,
      handler(v) {
        v && this.renderPage()
      },
    },
  },
  methods: {
    //formatter价格
    formatterPrize(row) {
      return (this.reportObj[row.id] ? fenToYuan(this.reportObj[row.id].payPrice) : 0).toFixed(2) + '元'
    },
    //formatter支付人数
    formatterPayNum(row) {
      return (this.reportObj[row.id] ? this.reportObj[row.id].payNum : 0) + '人'
    },
    //formatter免费点赞个数
    formatterFreeCnt(row) {
      return (this.reportObj[row.id] ? this.reportObj[row.id].freeCnt : 0) + '个'
    },
    //formatter免费点赞人数
    formatterFreeNum(row) {
      return this.reportObj[row.id] ? this.reportObj[row.id].freeNum + '人' : '0人'
    },
    //渲染数据
    async renderPage() {
      this.pageLoad = true
      try {
        const data = await api.marksubject.allist({
          where: {
            wallId: this.wall.id,
            markId: this.markId,
          },
          sort: { id: 'asc' },
        })
        let subjectIdList = []
        data.forEach((item) => subjectIdList.push(item.id))
        //关联参评对象的点赞和付费情况
        await this.relationMarkUpvote(subjectIdList)
        this.pageData.dataList = data
      } catch (err) {
        console.log(err)
        this.$notify.error({ title: '错误', message: err.msg || '获取评分收入统计数据失败！' })
      }
      this.pageLoad = false
    },
    //关联参评对象的点赞和付费情况
    async relationMarkUpvote(subjectIdList) {
      try {
        let tmpList = []
        subjectIdList.forEach((item) => this.reportObj[item] || tmpList.push(item))
        if (tmpList.length) {
          let data = await api.markupvote.report({
            where: {
              wallId: this.wall.id,
              markId: this.markId,
              subjectIdList: tmpList,
            },
          })
          data.forEach((item) => this.$set(this.reportObj, item.subjectId, item))
        }
      } catch (err) {
        console.log(err)
      }
    },
    //导出
    async exportData() {
      try {
        //判断是否有数据导出
        if (!this.pageData.dataList.length) {
          this.$notify.error({ title: '错误', message: '没有要导出的数据' })
          return
        }
        return {
          name: this.type === 'pay' ? '付费点赞' : '免费点赞',
          ref: this.$refs.table,
          data: this.pageData.dataList,
        }
      } catch (error) {
        console.error(error)
      }
    },
  },
  mounted() {},
}
</script>
<style scoped lang="stylus">
.red
  color #ff6161
.blue
  color #4886ff
.operate
  display flex
  span
    margin 0 5px
    cursor pointer
.role
  display flex
  justify-content flex-start
  align-items center
  width 100%
  margin 5px 0
  .role-img
    width 50px
    height 50px
    overflow hidden
  img
    width 100%
    height 100%
    object-fit container
    object-position center
  .role-name
    margin-left 20px
    span
      color red
</style>
