import api from './api'
export default {
  list: (postData) => api.fetchBaseData('/pro/hxc/prowalllottery/list.htm', postData),
  // 活动
  page: (postData) => api.fetchBaseData('/pro/hxc/prowalllottery/page.htm', postData),
  insert: (postData) => api.fetchBaseData('/pro/hxc/prowalllottery/insert.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/prowalllottery/update.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/prowalllottery/read.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/prowalllottery/delete.htm', postData),
  clearData: (postData) => api.fetchBaseData('/pro/hxc/prowalllottery/clearData.htm', postData),
  sort: (postData) => api.fetchBaseData('/pro/hxc/prowalllottery/sort.htm', postData),
  group: (postData) => api.fetchBaseData('/pro/hxc/prowalllottery/group.htm', postData),
}
