import api from './api'
export default {
  // 活动查询接口
  login: (postData) => api.fetchBaseData('/pro/hxc/prouser/login.htm', postData),
  register: (postData) => api.fetchBaseData('/pro/hxc/prouser/register.htm', postData),
  checkResetCode: (postData) => api.fetchBaseData('/pro/hxc/prouser/checkResetCode.htm', postData),
  passwdReset: (postData) => api.fetchBaseData('/pro/hxc/prouser/passwdReset.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/prouser/read.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/prouser/update.htm', postData),
  updatePasswd: (postData) => api.fetchBaseData('/pro/hxc/prouser/updatePasswd.htm', postData),
  checkPhone: (postData) => api.fetchBaseData('/pro/hxc/prouser/checkPhone.htm', postData),
  updatePhone: (postData) => api.fetchBaseData('/pro/hxc/prouser/updatePhone.htm', postData),
  checkEmail: (postData) => api.fetchBaseData('/pro/hxc/prouser/checkEmail.htm', postData),
  updateEmail: (postData) => api.fetchBaseData('/pro/hxc/prouser/updateEmail.htm', postData),
  sendEmailBoundLink: (postData) => api.fetchBaseData('/pro/hxc/prouser/sendEmailBoundLink.htm', postData),
  loginOut: () => api.fetchGetBaseData('/pro/hxc/prouser/logout.htm', {}),
  info: (postData) => api.fetchBaseData('/pro/hxc/prouser/info.htm', postData),
  boundEmail: (postData) => api.fetchBaseData('/pro/hxc/prouser/boundEmail.htm', postData),
  boundAmbInfo: (postData) => api.fetchBaseData('/pro/hxc/prouser/boundAmbInfo.htm', postData),
  boundAmb: (postData) => api.fetchBaseData('/pro/hxc/prouser/boundAmb.htm', postData),
  unboundAmbApply: (postData) => api.fetchBaseData('/pro/hxc/prouser/unboundAmbApply.htm', postData),
  hlogin: (postData) => api.fetchBaseData('/pro/hxc/prouser/open/hlogin.htm', postData),
  boundWxSqrcode: (postData) => api.fetchBaseData('/pro/hxc/prouser/boundWxSqrcode.htm', postData),
  checkResetCodeV2: (postData) => api.fetchBaseData('/pro/hxc/prouser/v2/checkResetCode.htm', postData),
  passwdResetV2: (postData) => api.fetchBaseData('/pro/hxc/prouser/v2/passwdReset.htm', postData),
  updateUserName: (postData) => api.fetchBaseData('/pro/hxc/prouser/updateUserName.htm', postData),
  agreeCashAgreement: (postData) => api.fetchBaseData('/pro/hxc/prouser/agreeCashAgreement.htm', postData),
  deregister: (postData) => api.fetchBaseData('/pro/hxc/prouser/deregister.htm', postData),
  checkPhoneMatch: (postData) => api.fetchBaseData('/pro/hxc/prouser/checkPhoneMatch.htm', postData),
  oemuserlogin: (postData) => api.fetchBaseData('/pro/hxc/prouser/oemuserlogin.htm', postData),
  oemlogin: (postData) => api.fetchBaseData('/pro/hxc/prouser/oemlogin.htm', postData),
}
