import api from './api'

export default {
  insert: postData => api.fetchBaseData('/pro/hxc/proshoot/insert.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/proshoot/delete.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/proshoot/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proshoot/update.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/proshoot/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proshoot/list.htm', postData),
}
