<template>
  <!-- 回复设置 -->
  <el-dialog title="回复设置" :visible.sync="showDig" width="760px" :close-on-click-modal="false" :append-to-body="true" @close="close">
    <!-- 内容区域 -->
    <div class="replycontent flex flex-a-c flex-j-sb">
      <!-- 左侧区域 -->
      <div class="left">
        <p class="keyword-wxtitle">{{ nowConfig.name }}</p>
        <div class="left-content">
          <div class="msg-item flex" v-if="nowConfig.subcribeReplySwitch === 'Y'">
            <p class="head"><img :src="nowConfig.headImg || require('@/assets/user/nopublic-icon.png')" /></p>
            <div class="msg">{{ nowConfig.subcribeReply }}</div>
          </div>
          <template v-if="nowConfig.msgReplySwitch === 'Y'">
            <p class="ques-msg mrg-t-20"><img src="@/assets/user/public-ques.png" /></p>
            <div class="msg-item flex mrg-t-20">
              <p class="head"><img :src="nowConfig.headImg || require('@/assets/user/nopublic-icon.png')" /></p>
              <div class="msg">{{ nowConfig.msgReply }}</div>
            </div>
          </template>
        </div>
      </div>
      <!-- 中间区域 -->
      <div class="middle"></div>
      <!-- 右侧区域 -->
      <div class="right">
        <div class="label">公众号绑定授权给Hi现场期间：</div>
        <div class="flex magin-15">
          <p>被关注回复：</p>
          <hi-switch v-model="nowConfig.subcribeReplySwitch" active-value="Y" inactive-value="N" active-text="开启" inactive-text="关闭"></hi-switch>
        </div>
        <el-input type="textarea" v-if="nowConfig.subcribeReplySwitch === 'Y'" :rows="4" v-model="nowConfig.subcribeReply"></el-input>
        <div class="flex magin-15">
          <p>收到消息回复：</p>
          <hi-switch v-model="nowConfig.msgReplySwitch" active-value="Y" inactive-value="N" active-text="开启" inactive-text="关闭"></hi-switch>
        </div>
        <el-input v-if="nowConfig.msgReplySwitch === 'Y'" type="textarea" :rows="4" v-model="nowConfig.msgReply"></el-input>
        <!-- 按钮区域 -->
        <div class="btn-zone">
          <el-button @click="close">取消</el-button>
          <el-button type="primary" @click="saveChange">保存</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import api from '@/api'
import { Hi } from '@/libs/common'
export default {
  name: 'Wxreplay',
  props: {
    wxauthconfig: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      pageLoad: false,
      showDig: true,
      nowConfig: Hi.Object.copy(this.wxauthconfig),
    }
  },
  methods: {
    async saveChange() {
      try {
        let update = Hi.Object.difference(this.wxauthconfig, this.nowConfig)
        if (update) {
          await api.wxauthconfig.update({
            where: { id: this.wxauthconfig.id },
            update,
          })
          this.$emit('update')
          this.$notify.success({ title: '成功', message: '保存成功！' })
        }
        this.close()
      } catch (err) {
        console.log(err)
        this.$notify.error({ title: '错误', message: err.msg || '保存失败！' })
      }
    },
    close() {
      this.$emit('close')
    },
  },
  async mounted() {},
}
</script>
<style scoped lang="stylus">
.replycontent
  width calc(100% - 60px)
  height 410px
  padding 0 30px
  font-size 14px
  .left
    width 260px
    height 420px
    background url("~@/assets/wall/info/attendtype/wxbac2.png") top center no-repeat
    background-size contain
    box-sizing border-box
    padding 0 10px
    padding-top 43px
    position relative
    .keyword-wxtitle
      width 180px
      height 15px
      line-height 15px
      text-align center
      position absolute
      top 21px
      left 30px
      font-size 12px
      font-weight 600
    .mrg-t-20
      margin-top 20px
    .ques-msg
      text-align right
    .left-content
      height 320px
      overflow-y auto
      box-sizing border-box
      padding 10px
    .msg-item
      .head
        width 30px
        height 30px
        border-radius 50%
        overflow hidden
        img
          width 100%
          height 100%
          object-fit cover
      .msg
        max-width 140px
        margin-left 5px
        padding 6px 8px
        background #fff
        color #333
        font-size 12px
        border-radius 3px
        line-height 1.4
  .middle
    width 52px
    height 40px
    background url("~@/assets/user/replysettingarrow.png") top center no-repeat
    background-size contain
  .right
    width 288px
    height 100%
    padding-top 23px
    line-height 1.6
    position relative
    >>>.el-textarea__inner
      resize none
    .label
      margin-bottom 20px
  .btn-zone
    text-align center
    position absolute
    bottom 30px
    right 20px
.magin-15
  margin 15px 0
</style>
