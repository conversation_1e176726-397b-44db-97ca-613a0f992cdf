<template>
  <el-dialog
    title="添加名单"
    :visible.sync="localVisible"
    :close-on-click-modal="false"
    destroy-on-close
    width="504px"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form :model="manualObj" :rules="manualRules" label-width="124px" ref="manualForm">
      <el-form-item label="分组" prop="group">
        <el-select class="w-300" v-model="manualObj.group" multiple filterable allow-create default-first-option placeholder="请选择分组">
          <el-option v-for="item in groupList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
      </el-form-item>
      <template v-for="item in stageList">
        <el-form-item v-if="labelName[item.key]" :label="labelName[item.key].name" :prop="item.key" :key="item.key">
          <el-select v-if="labelName[item.key].type === 'group'" v-model="manualObj[item.key]" class="w-300" placeholder="请选择">
            <el-option v-for="option in labelName[item.key].value" :key="option.value" :label="option.label" :value="option.value"></el-option>
          </el-select>
          <el-input v-else class="w-300" v-model.trim="manualObj[item.key]" maxlength="20"></el-input>
        </el-form-item>
      </template>
    </el-form>

    <div v-if="showLimit" class="fred font-12 mrg-t-5">
      数据总数超过{{ importCountMaxLimit }}条，无法添加，请<span v-if="isOem"> {{ wall.wallVersion === 'product' ? '联系客服' : '升级活动' }}</span>
      <span class="uphint" v-else-if="activityType !== 'product-advance'" @click="$emit('upgrade')"> 升级高级活动 </span>
      <span v-else>联系客服</span>
    </div>
    <span slot="footer" class="dialog-footer flex flex-j-sb flex-a-c">
      <div class="tal">
        <p>当前名单数量限制：{{ importCountMaxLimit }}</p>
        <p v-if="!isOem" class="mrg-t-5 font-12 f666">
          {{ activityType === 'product-advance' ? '扩容名单数量请联系客服' : '升级活动可扩容人名单数量' }}
          <el-button type="text" class="mrg-l-5" @click="showServiceQRcode" v-if="activityType === 'product-advance'"> 联系客服 </el-button>
        </p>
      </div>
      <el-button type="primary" @click="queryManualAdd">添 加</el-button>
    </span>
  </el-dialog>
</template>
<script>
import api from '@/api'
import { Hi, showServiceQRcode } from '@/libs/common'
import { mapGetters } from 'vuex'

export default {
  name: 'AddDialog',
  inject: ['wall'],
  props: {
    config: {
      type: Object,
      default: () => ({}),
    },
    stageList: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
    groupList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      localVisible: false,
      showLimit: false,
      manualObj: {
        d1: '',
        d2: '',
        d3: '',
        group: [],
      },
      labelName: {},
      manualRules: {
        d1: [{ required: true, message: '请填写必填项', trigger: ['change', 'blur'] }],
        d2: [{ required: true, message: '请填写必填项', trigger: ['change', 'blur'] }],
        d3: [{ required: true, message: '请填写必填项', trigger: ['change', 'blur'] }],
      },
    }
  },
  computed: {
    ...mapGetters({
      isOem: 'user/isOem',
    }),
    isTest() {
      return this.wall.wallVersion && this.wall.wallVersion === 'test'
    },
    activityType() {
      return this.$store.getters['wall/getActivityType']
    },
    importCountMaxLimit() {
      return this.$store.getters['user/getCountMaxLimit'](this.config, this.wall)
    },
  },
  watch: {
    visible(val) {
      this.localVisible = val
    },
    activityType: {
      handler(val) {
        this.localVisible = false
      },
    },
  },
  methods: {
    handleClose() {
      this.$refs.manualForm.resetFields()
      this.$emit('update:visible', false)
    },
    showServiceQRcode() {
      showServiceQRcode('name')
    },
    async queryManualAdd() {
      await this.$refs.manualForm.validate()
      try {
        const count = await api.seglotteryimport.count({
          where: {
            wallId: this.wall.id,
          },
        })
        if (count >= this.importCountMaxLimit) {
          this.showLimit = true
          return
        } else {
          this.showLimit = false
        }
        const seglotteryImportId = await api.seglotteryimport.add({
          wallId: this.wall.id,
          ...this.manualObj,
        })

        if (this.manualObj.group.length > 0) {
          const saveData = Hi.Object.batchSave(
            [],
            this.manualObj.group.map((groupId) => ({
              wallId: this.wall.id,
              seglotteryImportId,
              groupId,
            }))
          )
          await api.seglotteryimportgroup.batch(saveData)
        }

        this.$emit('refresh')
        this.$emit('update:visible', false)
        this.$notify.success({ title: '成功', message: '添加成功' })
      } catch (err) {
        console.log(err)
        this.$notify.error({ title: '错误', message: err.msg || '添加失败' })
      }
    },
    async handleOpen() {
      this.labelName = {
        d1: '',
        d2: '',
        d3: '',
        group: [],
      }
      this.stageList.forEach((item) => {
        this.labelName[item.key] = item
      })
    },
  },
}
</script>
<style scoped lang="stylus">
.w-300
  width 300px
.uphint
  cursor pointer
  text-decoration underline
  color #4886FF
</style>
