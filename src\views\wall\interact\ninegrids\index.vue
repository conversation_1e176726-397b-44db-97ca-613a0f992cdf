<template>
  <div class="relative">
    <hi-wall-set-right class="wall-set-diglett-box">
      <div slot="content">
        <div class="state-box">
          <el-tabs v-model="actTab" type="card">
            <el-tab-pane label="活动主题" name="theme">
              <hi-theme :config="config"></hi-theme>
            </el-tab-pane>
            <el-tab-pane label="已建活动" name="list">
              <hi-list :wall="wall" :config="config" v-if="wall.id" @toggleTab="(val) => (actTab = val)"></hi-list>
            </el-tab-pane>
          </el-tabs>
          <div class="flex btns">
            <el-button v-if="actTab === 'list'" type="primary" @click="actTab = 'theme'">新建轮次</el-button>
          </div>
        </div>
      </div>
      <div slot="control" class="control">
        <hi-switch-big
          v-if="config.openState"
          v-model="config.openState"
          active-value="Y"
          inactive-value="N"
          :disabled="false"
          @change="updateConfig"
        ></hi-switch-big>
        <div></div>
        <span></span>
      </div>
    </hi-wall-set-right>
    <hi-unlock vatKey="ninegridsModuleLimit" :actInfo="config" placement="middle" @readConfig="unlockModuleLimit"></hi-unlock>
  </div>
</template>
<script>
import { mapGetters, mapActions, mapMutations } from 'vuex'
import { wallMixin } from '@/libs/mixins'
import api from '@/api'
import HiList from './list.vue'
import HiTheme from './theme.vue'
export default {
  name: 'WallNinegrids',
  mixins: [wallMixin],
  components: {
    HiList,
    HiTheme,
  },
  data() {
    return {
      actTab: 'list',
    }
  },
  computed: {
    ...mapGetters({
      config: 'NinegridsEdit/ninegridsConfig',
      enableSave: 'NinegridsEdit/enableSave',
    }),
    disabled() {
      return this.config.openState === 'N'
    },
  },
  methods: {
    ...mapMutations('NinegridsEdit', ['setWall', 'setConfig']),
    ...mapActions('NinegridsEdit', ['fetchConfig']),
    unlockModuleLimit() {
      this.updateConfig('Y')
    },
    async updateConfig(v) {
      try {
        await api.ninegridsconfig.update({
          where: { id: this.config.id, wallId: this.wall.id },
          update: { openState: v },
        })
        this.$notify.success({ title: '成功', message: '更新成功' })
      } catch (err) {
        console.log(err)
        this.$notify.error({ title: '错误', message: err.msg })
      }
      await this.fetchConfig()
    },
  },
  async mounted() {
    await this.wallHandler()
    await this.setWall(this.wall)
    await this.fetchConfig()
  },
}
</script>
<style scoped lang="stylus">
.wall-set-diglett-box {
  min-width: 1100px;
  position: relative;
}

.state-box {
  position: relative;
  .btns {
    position: absolute;
    right: 0;
    top: 4px;
  }
}

.control {
  padding-left: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

::v-deep .el-tabs__content {
  overflow: visible;
}
</style>
