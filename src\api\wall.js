import api from './api'
export default {
  read: (postData) => api.fetchBaseData('/pro/hxc/prowall/read.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/prowall/list.htm', postData),
  allList: (postData) => api.fetchBaseData('/pro/hxc/prowall/alllist.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/prowall/page.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/prowall/add.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/prowall/update.htm', postData),
  del: (postData) => api.fetchBaseData('/pro/hxc/prowall/delete.htm', postData),
  count: (postData) => api.fetchBaseData('/pro/hxc/prowall/count.htm', postData),
  // 查询活动所有配置信息
  allConfig: (postData) => api.fetchBaseData('/pro/hxc/prowall/allConfig.htm', postData),

  wallStatics: (postData) => api.fetchBaseData('/pro/hxc/prowall/wallStatics.htm', postData),
  staticsByWallFlag: (postData) => api.fetchBaseData('/pro/hxc/prowall/wallStaticsByWallFlag.htm', postData),
  publish: (postData) => api.fetchBaseData('/pro/hxc/prowall/publish.htm', postData),

  //活动发布的时候开通的功能列表
  pubVatList: (postData) => api.fetchBaseData('/pro/hxc/prowall/pubVatList.htm', postData),
  //正式活动的增值功能列表 --- 删除的时候提示使用
  usedVatList: (postData) => api.fetchBaseData('/pro/hxc/prowall/usedVatList.htm', postData),

  // 查询 父账号 活动
  fatherList: (postData) => api.fetchBaseData('/pro/hxc/prowall/plist.htm', postData),
  // 复制主账号活动
  addfromtemplate: (postData) => api.fetchBaseData('/pro/hxc/prowall/addfromtemplate.htm', postData),
  // 复制活动
  copy: (postData) => api.fetchBaseData('/pro/hxc/prowall/copywall.htm', postData),
  readWall: (postData) => api.fetchBaseData('/pro/hxc/prowall/readWall.htm', postData),
  //通用二维码
  commonqrcode: (postData) => api.fetchBaseData('/pro/hxc/prowall/commonqrcode.htm', postData),
  // 判断当前账号下处于是否有处于有效期内的正式活动已开通任意互动的现金红包功能
  checkCash: (postData) => api.fetchBaseData('/pro/hxc/prowall/checkCash.htm', postData),

  // wallId 必填
  // openState 布尔值 必填
  // moduleArr 需要修改的模块集合，右上角那个开启/关闭所有互动时不传
  updateConfig: (postData) => api.fetchBaseData('/pro/hxc/prowall/updateConfig.htm', postData),
  end: (postData) => api.fetchBaseData('/pro/hxc/prowall/end.htm', postData),
  activityLimit: (postData) => api.fetchBaseData('/pro/hxc/prowall/activityLimit.htm', postData),

  // 根据wallFlag查agentId
  readAgentId: (postData) => api.fetchBaseData('/pro/hxc/prowall/readAgentId.htm', postData),
}
