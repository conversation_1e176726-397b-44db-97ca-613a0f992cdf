import api from './api'
export default {
  init: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3importform/init.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3importform/add.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3importform/delete.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/prolistlotteryv3importform/update.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3importform/list.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/propiclotteryv3importform/batch.htm', postData),
}
