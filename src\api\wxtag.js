import api from './api'
export default {
  page: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/list.htm', postData),
  recordlist: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/list.htm', postData),
  count: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/count.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/delete.htm', postData),
  insert: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/insert.htm', postData),

  // 粉丝加入自定义分组
  wxtogroup: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/wxtogroup.htm', postData),

  // 加入白名单
  white: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/white.htm', postData),
  // 移出白名单
  whiteout: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/whiteout.htm', postData),

  // 添加手工派奖人员
  handlottery: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/handlottery.htm', postData),
  //手工派奖删除
  handlotterydel: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/handlotterydel.htm', postData),

  tree: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/tree.htm', postData),
  onetree: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/onetree.htm', postData),
  onetreev2: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/v2/onetree.htm', postData),
  wxuseridpage: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/wxuseridpage.htm', postData),
  wxuseridlist: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/wxuseridlist.htm', postData),
  getTotalCntByGroup: (postData) => api.fetchBaseData('/pro/hxc/prowxtag/getTotalCntByGroup.htm', postData),
}
