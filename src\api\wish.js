import api from './api';
export default {
  read: (v) => api.fetchBaseData('/pro/hxc/prowish/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/prowish/update.htm', v),
  add: (v) => api.fetchBaseData('/pro/hxc/prowish/add.htm', v),
  page: (v) => api.fetchBaseData('/pro/hxc/prowish/page.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/prowish/list.htm', v),
  delete: (postData) => api.fetchBaseData('/pro/hxc/prowish/delete.htm', postData),
  finish: (postData) => api.fetchBaseData('/pro/hxc/prowish/finish.htm', postData),
};
