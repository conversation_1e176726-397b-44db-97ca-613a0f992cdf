import api from './api'
export default {
  //订货会-分组用户
  read: (postData) => api.fetchBaseData('/pro/hxc/proplaceordergroupuser/read.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proplaceordergroupuser/update.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proplaceordergroupuser/list.htm', postData),
  changeGroup: (postData) => api.fetchBaseData('/pro/hxc/proplaceordergroupuser/changeGroup.htm', postData),
}
