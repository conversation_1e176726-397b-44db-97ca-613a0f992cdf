<template>
  <div class="question-selector">
    <el-dialog
      title="题库选择"
      :width="isFixed ? '1200px' : '600px'"
      :visible="visible"
      :close-on-click-modal="false"
      destroy-on-close
      :append-to-body="true"
      @open="handleOpen"
      @close="handleClose"
    >
      <template v-if="isFixed">
        <hi-question-bank-manager v-if="visible" ref="questionBankManagerRef" mode="select" />
      </template>

      <template v-else>
        <div>
          <el-radio-group v-model="type" @change="handleTypeChange">
            <el-radio label="DIFFICULTY">题目难度抽取</el-radio>
            <el-radio label="GROUP">题目分类抽取</el-radio>
            <el-radio label="TYPE">题目类型抽取</el-radio>
          </el-radio-group>
        </div>
        <div class="mrg-t-20">
          <el-table :data="tableData" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="name" :label="tableLabel" />
            <el-table-column prop="subjectCount" label="数量" :formatter="formatSubjectCount" />
          </el-table>
        </div>
      </template>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import HiQuestionBankManager from './QuestionBankManager.vue'

export default {
  name: 'QuestionSelector',
  components: {
    HiQuestionBankManager,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    actConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      type: 'DIFFICULTY',
      tableData: [],
      selectedItems: [],
    }
  },
  computed: {
    ...mapState('questionBank', ['subjectDifficultyList', 'subjectGroupList', 'questionList']),
    isFixed() {
      return this.actConfig.chooseSubjectType === 'FIXED'
    },
    // 表格列标题
    tableLabel() {
      const labelMap = {
        DIFFICULTY: '难度',
        GROUP: '分类名称',
        TYPE: '题目类型',
      }
      return labelMap[this.type] || '名称'
    },
    // 题目类型选项
    typeOptions() {
      return [
        { id: 'RADIO', name: '单选题', subjectCount: 0, type: 'TYPE' },
        { id: 'CHECKBOX', name: '多选题', subjectCount: 0, type: 'TYPE' },
      ]
    },
  },
  methods: {
    formatSubjectCount(row) {
      if (row.type === 'TYPE') {
        return this.questionList.filter((item) => item.type === row.id).length
      }
      return row.subjectCount || 0
    },
    initTableData() {
      let sourceData = []

      switch (this.type) {
        case 'DIFFICULTY':
          sourceData = this.subjectDifficultyList
          break
        case 'GROUP':
          sourceData = this.subjectGroupList
          break
        case 'TYPE':
          sourceData = this.typeOptions
          break
        default:
          sourceData = []
      }

      this.tableData = sourceData.map((item) => ({
        id: item.id,
        name: item.name,
        subjectCount: item.subjectCount || 0,
        type: this.type,
      }))
    },
    handleOpen() {
      if (!this.isFixed) {
        this.initTableData()
      }
    },
    handleClose() {
      this.$emit('close')
    },
    handleTypeChange() {
      this.initTableData()
    },
    handleSave() {
      if (this.isFixed) {
        const selectedQuestions = this.$refs.questionBankManagerRef?.selectedQuestions || []
        if (selectedQuestions.length > 0) {
          this.$emit('save', selectedQuestions)
        } else {
          this.$notify.warning({ title: '提示', message: '请选择题目!' })
        }
      } else {
        if (this.selectedItems.length > 0) {
          this.$emit('save', this.selectedItems)
        } else {
          this.$notify.warning({ title: '提示', message: '请至少选择一项!' })
        }
      }
    },
    handleSelectionChange(selection) {
      this.selectedItems = selection
    },
  },
}
</script>

<style scoped lang="scss">
.question-selector {
  .mrg-t-20 {
    margin-top: 20px;
  }

  .el-radio-group {
    .el-radio {
      margin-right: 20px;
      margin-bottom: 10px;
    }
  }

  .el-table {
    .el-checkbox {
      display: flex;
      justify-content: center;
    }
  }
}
</style>
