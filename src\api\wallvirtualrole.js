import api from './api'
export default {
  add: postData => api.fetchBaseData('/pro/hxc/prowallvirtualrole/add.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prowallvirtualrole/update.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prowallvirtualrole/list.htm', postData),
  send: postData => api.fetchBaseData('/pro/hxc/prowallvirtualrole/msg/send.htm', postData),
  //再次发送
  again: postData => api.fetchBaseData('/pro/hxc/prowallvirtualrole/msg/again.htm', postData),
  //次数
  count: postData => api.fetchBaseData('/pro/hxc/prowallvirtualrole/msg/count.htm', postData),
  last: postData => api.fetchBaseData('/pro/hxc/prowallvirtualrole/msg/last.htm', postData),
}
