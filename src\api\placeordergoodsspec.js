import api from './api'
export default {
  //订货会-商品规格
  read: postData => api.fetchBaseData('/pro/hxc/proplaceordergoodsspec/read.htm', postData),
  batch: postData => api.fetchBaseData('/pro/hxc/proplaceordergoodsspec/batch.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/proplaceordergoodsspec/update.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proplaceordergoodsspec/list.htm', postData),
}
