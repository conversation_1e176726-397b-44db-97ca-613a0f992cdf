import api from './api'
export default {
  // 盲盒抽奖、红包墙，奖品信息
  read: (v) => api.fetchBaseData('/pro/hxc/propacketwallawards/read.htm', v),
  update: (v) => api.fetchBaseData('/pro/hxc/propacketwallawards/update.htm', v),
  add: (v) => api.fetchBaseData('/pro/hxc/propacketwallawards/add.htm', v),
  page: (v) => api.fetchBaseData('/pro/hxc/propacketwallawards/page.htm', v),
  list: (v) => api.fetchBaseData('/pro/hxc/propacketwallawards/list.htm', v),
  batch: (v) => api.fetchBaseData('/pro/hxc/propacketwallawards/batch.htm', v),
}
