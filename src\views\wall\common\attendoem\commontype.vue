<template>
  <div class="wall-attendtype-box flex">
    <div>
      <div class="flex flex-a-c mt-10">
        <template>
          <el-button type="primary" v-if="'MULTI_PLATFORM_H5' === wall.wxauthType" disabled>当前主参与方式</el-button>
          <el-button type="primary" v-else @click="$emit('updateAttendType','MULTI_PLATFORM_H5')">设为主参与方式</el-button>
        </template>
      </div>
      <div>
        <div class="line">
          <label> <i class="disc">●</i>方式一：</label>
          <div class="value">请用微信直接扫码参与现场互动</div>
        </div>
        <div class="line">
          <label></label>
          <div class="value flex flex-a-c">
            <hi-qrcode :value="wall | entryCommon(route) | addId(actId) | addH5" size="120" level="L"></hi-qrcode>
            <a class="qrcode-down" :href="wall | entryCommon(route) | addId(actId) | addH5 | qrcode | downloadQrcode('qrcode')">
              下载该二维码图片
            </a>
          </div>
        </div>
        <div class="line">
          <label> <i class="disc">●</i>方式二：</label>
          <div>
            <div class="mb-15">请用微信直接扫码参与现场互动</div>
            <div class="value flex flex-a-c">
              <div class="width-105" :data-d="wall | entryCommon(route)">上墙地址</div>
              <el-input type="text" :value="wall | entryCommon(route) | addId(actId) | addH5" ref="wallhref" readonly></el-input>
              <div class="block"></div>
              <el-button plain type="info" @click="copy('wallhref')">复制</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="common-box">
      <div class="info">不支持在线支付、现金红包奖品领取等功能,需自行上传昵称头像</div>
      <div class="info2">编辑头像昵称页面</div>
      <div class="img" :style="commpnBgStyle">
        <img src="~@/assets/wall/info/attendtype/common.png" alt="">
      </div>
      <!-- <div class="tbns">
        <hi-upload-img class="pcbac-upload" list-type="picture-card" :action="$hi.url.upload" :show-file-list="false" :on-success="upbgSuccess" :on-progress="(v) => (sbgLoad = true)" :on-error="(v) => (sbgLoad = false)">
          <el-button type="primary">上传替换</el-button>
        </hi-upload-img>
        <el-button class="mt-10" type="primary" plain @click="toDefault">恢复默认</el-button>
      </div> -->
    </div>
  </div>
</template>
<script>
import api from '@/api'
import { Hi } from '@/libs/common'
export default {
  name: 'otherh5',
  components: {},
  props: ['wall', 'route', 'actId', 'wallConfig'],
  data() {
    return {}
  },
  computed: {
    commpnBgStyle() {
      return {
        'background-image': `url(${this.wallConfig.thirdAppScanBgUrl})`,
      }
    },
  },
  methods: {
    copy(cont) {
      this.$refs[cont].select()
      document.execCommand('Copy')
      this.$message({
        message: '复制成功',
        type: 'success',
      })
    },
    //上传成功
    async upbgSuccess(res) {
      try {
        let data = res.data
        let url = Hi.String.dealUrl(data.url)
        await api.wallconfig.update({
          where: { wallId: this.wall.id },
          update: { thirdAppScanBgUrl: url },
        })
        await this.$emit('updateWallConfig')
        this.$notify.success({ title: '成功', message: '上传成功!' })
      } catch (err) {
        console.log(err)
        this.$notify.error(err.msg || '提交失败！')
      }
    },
    async toDefault() {
      await api.wallconfig.update({
        where: { wallId: this.wall.id },
        update: { thirdAppScanBgUrl: '' },
      })
      await this.$emit('updateWallConfig')
    }
  },
  async mounted() { },
}
</script>
<style scoped lang="stylus">
.wall-attendtype-box
  position relative
  min-height 480px
.mb-15
  margin-bottom 15px
.mt-10
  margin-top 10px
.width-105
  width 105px
.width-150
  width 150px
.block
  width 10px
.clearbutton
  padding 0
  border 0
  &:hover
    background-color #fff
.disc
  color #4886ff
  font-style normal
  margin-right 5px
// 下载二维码按钮
.qrcode-down
  margin-top 8px
  margin-left 36px
  display block
  width 128px
  height 12px
  padding 9px 15px
  font-size 12px
  border-radius 3px
  color #fff
  text-align center
  background-color #409eff
  border-color #409eff
  line-height 12px
  border 0
  &:hover
    background #66b1ff
    border-color #66b1ff
    color #fff
.line
  margin 16px 0
  display flex
  label
    padding-left 5px
    padding-right 20px
    font-weight bold
    font-size 15px
    white-space nowrap
    width 80px
.common-box
  height 420px
  width 320px
  margin-left 25px
  box-sizing border-box
  padding-top 10px
  position relative
  span
    font-weight bold
  .img
    width 200px
    height 356px
    background #F0F6FF
    margin-top 10px
    background-size 100%
    background-position top center
    background-repeat no-repeat
    img
      width 100%
      height 100%
      object-fit cover
  .tbns
    position absolute
    bottom 0
    right 0
.pcbac-upload>>>.el-upload
  width initial
  height initial
  border none
  line-height initial
.info
  line-height 1.5
  i
    color red
.info2
  color #666
  margin-top 20px
</style>
