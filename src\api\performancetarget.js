import api from './api'
export default {
  add: v => api.fetchBaseData('/pro/hxc/properformancetarget/add.htm', v),
  read: v => api.fetchBaseData('/pro/hxc/properformancetarget/read.htm', v),
  update: v => api.fetchBaseData('/pro/hxc/properformancetarget/update.htm', v),
  list: v => api.fetchBaseData('/pro/hxc/properformancetarget/list.htm', v),
  delete: v => api.fetchBaseData('/pro/hxc/properformancetarget/delete.htm', v),
  page: v => api.fetchBaseData('/pro/hxc/properformancetarget/page.htm', v),
  clear: v => api.fetchBaseData('/pro/hxc/properformancetarget/clear.htm', v),
}
