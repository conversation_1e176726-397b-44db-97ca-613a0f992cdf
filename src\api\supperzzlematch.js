import api from './api'

export default {
  // prosupperzzleawards
  list: postData => api.fetchBaseData('/pro/hxc/prosupperzzlematch/list.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/prosupperzzlematch/page.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prosupperzzlematch/update.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/prosupperzzlematch/delete.htm', postData),
  clear: postData => api.fetchBaseData('/pro/hxc/prosupperzzlematch/clear.htm', postData),
}
