import api from './api'
export default {
  add: postData => api.fetchBaseData('/pro/hxc/proseglotteryimport/add.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/proseglotteryimport/delete.htm', postData),
  cleardata: postData => api.fetchBaseData('/pro/hxc/proseglotteryimport/cleardata.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/proseglotteryimport/list.htm', postData),
  count: postData => api.fetchBaseData('/pro/hxc/proseglotteryimport/count.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/proseglotteryimport/page.htm', postData),
  batchAdd: postData => api.fetchBaseData('/pro/hxc/proseglotteryimport/batchAdd.htm', postData),
}
