<template>
  <div v-loading="loading" element-loading-text="拼命加载中">
    <hi-lottery-manage v-if="isManage" :config="config" @toList="isManage = false" @upgrade="checkActivityLimit"></hi-lottery-manage>
    <!-- 结果控制视图 -->
    <hi-result-control v-else-if="isResultControl" :config="config" @toList="isResultControl = false" @updateConfig="readConfig"></hi-result-control>
    <!-- 编辑视图 -->
    <hi-lottery-edit
      v-else-if="isEdit"
      @loaded="loaded"
      :editId="editObj.id"
      :themeId="editObj.themeId"
      :config="config"
      @list="handleReturnToList"
      @updateConfig="readConfig"
    ></hi-lottery-edit>
    <!-- 列表视图 -->
    <hi-lottery-list
      v-else-if="config.id"
      :config="config"
      @loaded="loaded"
      @edit="handleEdit"
      @updateConfig="readConfig"
      @toResult="isResultControl = true"
      @toManage="isManage = true"
    ></hi-lottery-list>

    <!-- 普通升级 -->
    <hi-wall-publish
      v-if="isUpgrade && activityType === 'normal'"
      :wallFlag="wall.wallFlag"
      @close="(v) => (isUpgrade = v)"
      @pay="payfn"
    ></hi-wall-publish>
    <!-- 高级升级 -->
    <hi-wall-upgrade
      v-if="isUpgrade && activityType === 'product'"
      :wallFlag="wall.wallFlag"
      @close="(v) => (isUpgrade = v)"
      @pay="payfn"
    ></hi-wall-upgrade>
  </div>
</template>
<script>
import api from '@/api'
import { showServiceQRcode } from '@/libs/common'
import { wallSetMixin } from '@/libs/mixins.js'
import HiWallPublish from '@/views/wall/common/wallpublish'
import HiWallUpgrade from '@/views/wall/common/wallupgrade'
import { mapActions, mapGetters } from 'vuex'
import HiLotteryEdit from './edit.vue'
import HiLotteryList from './list.vue'
import HiLotteryManage from './manage/index.vue'
import HiResultControl from './resultControl.vue'

export default {
  name: 'seglottery',
  inject: ['wall'],
  mixins: [wallSetMixin],
  components: {
    HiResultControl,
    HiLotteryList,
    HiLotteryEdit,
    HiLotteryManage,
    HiWallPublish,
    HiWallUpgrade,
  },
  data() {
    return {
      config: {},
      isResultControl: false,
      isManage: false,
      loading: false,
      isUpgrade: false,
    }
  },
  computed: {
    ...mapGetters({
      wallFlagObj: 'wall/getWallFlagObj',
      activityType: 'wall/getActivityType',
      userRole: 'user/getUserRole',
      isWallLimitExceeded: 'wall/isWallLimitExceeded',
    }),
    isEdit() {
      return this.editObj !== null
    },
  },
  methods: {
    ...mapActions({
      fetchWall: 'wall/fetchWall',
    }),
    handleEdit(v) {
      this.setEdit(v)
    },
    handleReturnToList() {
      this.setList()
      this.readConfig()
    },
    async payfn(pay) {
      try {
        await this.$hi.pay(pay)
        await this.fetchWall({
          wallFlag: this.wall.wallFlag,
          force: true,
        })
        await this.readConfig()
      } catch (e) {
        console.error(e)
      }
    },
    // 检查活动数量是否超过三个
    checkActivityLimit() {
      if (this.isWallLimitExceeded) {
        const isVip = this.userRole === 'VIP'
        this.$confirm('当前正式活动数量已达上限，无法发布更多正式活动，点击马上咨询，购买解锁更多权益', '提示', {
          showCancelButton: false,
          confirmButtonText: isVip ? '马上咨询' : '点击开通会员',
          type: 'warning',
          distinguishCancelAndClose: true,
        }).then(async () => {
          if (isVip) {
            showServiceQRcode('restrict')
          } else {
            window.open('/members.html', '_blank')
          }
        })
        return
      }
      this.isUpgrade = true
    },
    async readConfig() {
      this.loading = true
      try {
        if (!this.wall?.id) {
          console.warn('无法读取配置，缺少 wall.id。')
          return
        }
        const result = await api.seglotteryconfig.read({
          where: { wallId: this.wall.id },
        })

        if (!result) {
          // 没有配置 就去创建
          await api.seglotteryconfig.add({ wallId: this.wall.id })
          this.config = await api.seglotteryconfig.read({ where: { wallId: this.wall.id } })
        } else {
          this.config = result
        }
      } catch (err) {
        console.error('读取抽奖配置失败:', err)
        this.$notify.error({
          title: '配置加载失败',
          message: err.msg || '获取抽奖配置失败，请稍后重试！',
        })
        this.config = {}
      } finally {
        this.loaded()
      }
    },
    loaded() {
      if (typeof this.$options.mixins.find((m) => m.methods?.loaded)?.methods.loaded === 'function') {
        this.$options.mixins.find((m) => m.methods?.loaded).methods.loaded.call(this)
      } else {
        this.loading = false
      }
    },
  },
  async mounted() {
    await this.readConfig()
    if (this.$route.query.open === 'roster') {
      this.isManage = true
    }
  },
}
</script>
