import api from './api';

export default {
  insert: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlinksubject/insert.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlinksubject/delete.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlinksubject/read.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlinksubject/update.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlinksubject/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlinksubject/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlinksubject/add.htm', postData),
  batch: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlinksubject/batch.htm', postData),
  sort: (postData) => api.fetchBaseData('/pro/hxc/proteamanswerlinksubject/sort.htm', postData),
};
