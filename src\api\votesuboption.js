import api from './api'
export default {
  batch: postData => api.fetchBaseData( '/pro/hxc/prowallvotesuboption/batch.htm', postData ),
  list: postData => api.fetchBaseData( '/pro/hxc/prowallvotesuboption/list.htm', postData ),
  update: postData => api.fetchBaseData( '/pro/hxc/prowallvotesuboption/update.htm', postData ),
  report: postData => api.fetchBaseData( '/pro/hxc/prowallvotesuboption/report.htm', postData ),
  rankinglist: postData => api.fetchBaseData( '/pro/hxc/prowallvotesuboption/rankinglist.htm', postData ),
  page: postData => api.fetchBaseData( '/pro/hxc/prowallvotesuboption/page.htm', postData ),
}