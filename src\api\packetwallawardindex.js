import api from './api';

export default {
  // 盲盒抽奖、红包墙，指定奖品编号
  update: v => api.fetchBaseData('/pro/hxc/propacketwallawardindex/update.htm', v),
  add: v => api.fetchBaseData('/pro/hxc/propacketwallawardindex/add.htm', v),
  list: v => api.fetchBaseData('/pro/hxc/propacketwallawardindex/list.htm', v),
  page: v => api.fetchBaseData('/pro/hxc/propacketwallawardindex/page.htm', v),
  delete: postData => api.fetchBaseData('/pro/hxc/propacketwallawardindex/delete.htm', postData),
};
