import api from './api';

export default {
  insert: (postData) => api.fetchBaseData('/pro/hxc/proteamanswer/insert.htm', postData),
  delete: (postData) => api.fetchBaseData('/pro/hxc/proteamanswer/delete.htm', postData),
  read: (postData) => api.fetchBaseData('/pro/hxc/proteamanswer/read.htm', postData),
  update: (postData) => api.fetchBaseData('/pro/hxc/proteamanswer/update.htm', postData),
  page: (postData) => api.fetchBaseData('/pro/hxc/proteamanswer/page.htm', postData),
  list: (postData) => api.fetchBaseData('/pro/hxc/proteamanswer/list.htm', postData),
  add: (postData) => api.fetchBaseData('/pro/hxc/proteamanswer/add.htm', postData),
};
