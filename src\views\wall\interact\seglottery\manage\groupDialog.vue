<template>
  <el-dialog
    title="分组管理"
    :visible.sync="localVisible"
    width="600px"
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 分组列表 -->
    <div class="group-list">
      <div class="group-header">
        <el-button type="primary" size="small" @click="handleGroupOperation()">添加分组</el-button>
      </div>

      <el-table :data="groupList" border class="group-table" height="300px">
        <el-table-column prop="name" label="名称"></el-table-column>
        <el-table-column label="操作" width="200">
          <template v-slot="{ row }">
            <el-button v-if="row.type !== 'DEFAULT'" type="text" size="small" @click="handleGroupOperation(row)"> 编辑 </el-button>
            <el-button v-if="row.type !== 'DEFAULT'" class="red" type="text" size="small" @click="handleDelete(row)"> 删除 </el-button>
            <span v-if="row.type === 'DEFAULT'" class="default-group-tip">默认分组</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import api from '@/api'

export default {
  name: 'GroupDialog',
  inject: ['wall'],
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    groupList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      localVisible: false,
    }
  },
  watch: {
    visible(val) {
      this.localVisible = val
    },
  },
  methods: {
    // 分组操作（添加/编辑）
    async handleGroupOperation(row) {
      const isEdit = !!row
      const title = isEdit ? '编辑分组' : '添加分组'
      const successMessage = isEdit ? '更新分组成功' : '添加分组成功'
      const errorMessage = isEdit ? '更新分组失败' : '添加分组失败'

      try {
        const promptOptions = {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^.{1,20}$/,
          inputErrorMessage: '分组名称长度在 1 到 20 个字符',
          inputPlaceholder: '请输入分组名称',
        }

        // 编辑时设置默认值
        if (isEdit) {
          promptOptions.inputValue = row.name
        }

        const { value: groupName } = await this.$prompt('请输入分组名称', title, promptOptions)

        if (!groupName || !groupName.trim()) {
          return
        }

        const trimmedName = groupName.trim()

        // 检查分组名称是否重复
        const exists = isEdit
          ? this.groupList.some((group) => group.name === trimmedName && group.id !== row.id)
          : this.groupList.some((group) => group.name === trimmedName)

        if (exists) {
          this.$message.warning('分组名称已存在')
          return
        }

        // 执行API操作
        if (isEdit) {
          await api.seglotterygroup.update({
            where: { id: row.id, wallId: this.wall.id },
            update: { name: trimmedName },
          })
        } else {
          await api.seglotterygroup.add({ wallId: this.wall.id, name: trimmedName, type: 'CUSTOM' })
        }

        this.$notify.success({ title: '成功', message: successMessage })
        this.$emit('refresh')
      } catch (error) {
        if (error !== 'cancel') {
          console.error(`${errorMessage}:`, error)
          this.$notify.error({ title: '错误', message: error.msg || errorMessage })
        }
      }
    },
    // 删除分组
    async handleDelete(row) {
      try {
        await this.$confirm('删除后无法恢复，是否继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        await api.seglotterygroup.delete({
          where: { id: row.id, wallId: this.wall.id },
        })

        this.$notify.success({ title: '成功', message: '删除分组成功' })
        this.$emit('refresh')
      } catch (err) {
        if (err !== 'cancel') {
          console.error(err)
          this.$notify.error({ title: '错误', message: '删除分组失败' })
        }
      }
    },
    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style scoped>
.red {
  color: #f56c6c;
}

.group-list {
  margin-bottom: 20px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.group-table {
  width: 100%;
}

.default-group-tip {
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
