<template>
  <div class="accountinfo-box">
    <div class="info-content">
      <div v-if="isOem" class="info-top">
        <div>
          <div class="h-36 flex flex-a-c">
            <label>账号：</label>
            <p class="w-154">{{ userInfo.userName }}</p>
          </div>
          <div class="h-36 flex flex-a-c">
            <label>密码：</label>
            <p class="w-154">{{ userInfo.passwd }}</p>
            <el-button type="text" @click="changePwd()">修改密码</el-button>
          </div>
        </div>
      </div>
      <div v-if="!isOem" class="info-top">
        <div class="user-info">
          <div class="user-pic">
            <hi-upload-img list-type="picture-card" :action="$hi.url.upload" :show-file-list="false" :on-success="uploadSuccess">
              <img v-if="dialogImageUrl" class="pic" v-hiimg="dialogImageUrl" alt="" />
              <img v-else class="pic" :src="require('@/assets/user/default.png')" alt="" />
              <p>上传头像</p>
            </hi-upload-img>
          </div>
          <div class="user-nickname">
            <div class="name">
              <div class="nickname">{{ userInfo.companyName || userInfo.nickName }}</div>
              <el-button type="text" @click="changeNickName()"> 编辑企业名称/品牌名称</el-button>
            </div>
            <div class="account-type">
              账号类型：{{ accountTypeInfo.name }}
              <img class="role-icon" v-if="accountTypeInfo.icon" :src="accountTypeInfo.icon" alt="" />
            </div>
            <div v-if="accountTypeInfo.type === 'VIP'" class="account-type">{{ vipDateRange }}</div>
          </div>
        </div>
        <div class="haidou-info">
          <div class="income-box">
            <div class="num">{{ wallwxuserInfo || 0 }}</div>
            <div>
              <span>完成活动（场） </span>
              <el-tooltip class="item" effect="dark" content="参与人数超过20人且已结束" placement="right">
                <img class="doubt" src="~@/assets/wall/interact/doubt.png" alt="" />
              </el-tooltip>
            </div>
          </div>
          <div class="line"></div>
          <div class="income-box">
            <div class="num">{{ accountObj.actIncome | fenToYuan }}</div>
            <div>
              <span>活动收入 （元） </span>
              <el-button type="text" style="text-indent: -999px; width: 0; overflow: hidden; cursor: default"> 1 </el-button>
            </div>
          </div>
          <div class="line" v-if="showHibenInfo"></div>
        </div>
        <el-dialog
          v-if="dialogFormVisible"
          custom-class="dialog-outer"
          width="470px"
          :class="['dialog-style', currentEvent === '人数详情' ? 'dialog-special' : '']"
          :title="title"
          :visible.sync="dialogFormVisible"
          :close-on-click-modal="false"
          :lock-scroll="false"
          @open="openDialogOuter"
          @close="reset"
        >
          <p class="tit" :style="{ visibility: hintShow }">{{ hint }}</p>
          <!-- 更改昵称 -->
          <div v-show="currentEvent === '编辑企业名称/品牌名称'" class="set-nickname unify">
            <span>名称</span>
            <div class="code-box">
              <el-input v-model="reviseName" auto-complete="off" :maxlength="20"></el-input>
            </div>
          </div>

          <el-form :model="changePwdObj" :rules="rules" ref="changePwdForm">
            <!-- 更改密码 -->
            <div v-show="currentEvent === '修改密码' || currentEvent === '设置密码'" style="margin-top: 20px">
              <el-form-item v-if="userInfo.passwd" label="原密码" :label-width="formLabelWidth" prop="oldPasswd">
                <el-input v-model="changePwdObj.oldPasswd" auto-complete="off" type="password"></el-input>
              </el-form-item>
              <el-form-item label="新密码" :label-width="formLabelWidth" prop="newPasswd" style="margin: 20px 0">
                <el-input v-model="changePwdObj.newPasswd" auto-complete="off" type="password"></el-input>
              </el-form-item>
              <el-form-item label="确认新密码" :label-width="formLabelWidth" prop="confirmNewPwd">
                <el-input v-model="changePwdObj.confirmNewPwd" auto-complete="off" type="password"></el-input>
              </el-form-item>
            </div>
            <!-- 更改手机号 -->
            <div v-show="currentEvent === '修改手机号'" style="margin-top: 20px">
              <el-row type="flex" justify="space-between" v-if="verifyState === ''">
                <el-col :span="24">
                  <el-form-item label="原手机号" :label-width="formLabelWidth" style="margin-bottom: 0">
                    <el-input v-model.trim="changePhoneObj.oldPhone" auto-complete="off"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row type="flex" justify="space-between" v-if="verifyState === 'SUCCESS'">
                <el-col :span="24">
                  <el-form-item label="新手机号" :label-width="formLabelWidth">
                    <el-input v-model.trim="changePhoneObj.newPhone" auto-complete="off"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="unify set-phone">
                <span>短信验证码</span>
                <div class="code-box">
                  <el-input v-model="changePhoneObj.notCode" auto-complete="off"></el-input>
                  <el-button type="primary" plain @click="getCode()" :disabled="disconfirm">{{ getTit }}</el-button>
                </div>
              </div>
            </div>
          </el-form>
          <!-- 更改邮箱 -->
          <div v-show="currentEvent === '修改邮箱'">
            <div v-show="!dialogSendCode" :flag="flag">
              <div class="unify set-mail" v-show="verifyEmail === ''">
                <label>原邮箱</label>
                <div class="flex-1 pading10">
                  <el-input v-model="changeMailObj.oldMailBox" auto-complete="off" placeholder="请输入原邮箱"> </el-input>
                </div>
              </div>
              <div class="unify set-mail" v-show="verifyEmail === 'SUCCESS'">
                <span>新邮箱</span>
                <div class="flex-1 pading10">
                  <el-input v-model="changeMailObj.newMailBox" auto-complete="off" placeholder="请输入新邮箱"> </el-input>
                </div>
              </div>
              <div class="unify set-mail">
                <span>验证码</span>
                <div class="code-box">
                  <el-input v-model="changeMailObj.verifyCodeNewMailBox" auto-complete="off"></el-input>
                  <el-button type="primary" plain @click="getCode()" :disabled="disconfirm">{{ getTit }}</el-button>
                </div>
              </div>
            </div>
            <div class="bind-email" v-show="dialogSendCode" :flag="flag">
              <p>
                已发送验证邮件至
                <span>{{ bindMailObj.premail }}</span>
                <span>{{ bindMailObj.midmail }}</span>
                <span>{{ bindMailObj.nextmail }}</span>
              </p>
              <span>长时间未收到邮件?</span>
              <el-button type="text" @click="resend()">重新发送</el-button>
            </div>
          </div>
          <!-- 绑定邮箱 -->
          <div v-show="currentEvent === '绑定邮箱'">
            <div class="unify" v-show="!dialogSendCode" :flag="flag">
              <span>邮箱</span>
              <div class="code-box">
                <el-input v-model="bindMailObj.mailbox" auto-complete="off" placeholder="请输入要绑定的邮箱"> </el-input>
              </div>
            </div>
            <div class="bind-email" v-show="dialogSendCode" :flag="flag">
              <p>
                已发送验证邮件至
                <span>{{ bindMailObj.premail }}</span>
                <span>{{ bindMailObj.midmail }}</span>
                <span>{{ bindMailObj.nextmail }}</span>
              </p>
              <span>长时间未收到邮件?</span>
              <el-button type="text" @click="resend()">重新发送</el-button>
            </div>
          </div>
        </el-dialog>
        <!-- 验证码弹窗 -->
        <el-dialog
          title="请输入验证码"
          custom-class="dialog-inner"
          :visible.sync="dialogFormVisibleCode"
          :close-on-click-modal="false"
          width="470px"
          @open="openDialogInner"
        >
          <p class="tit" :style="{ visibility: changePhoneObj.codeHintShow }">{{ changePhoneObj.codeHint }}</p>
          <div class="set-code mrg-t-15">
            <span>验证码</span>
            <el-input v-model="changePhoneObj.imgvcode" auto-complete="off"></el-input>
            <div class="verify-code" @click="changeCode()"><img :src="changePhoneObj.verifyCode" alt="" /></div>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="confirmCode()">确 定</el-button>
          </div>
        </el-dialog>
      </div>
      <div v-if="!isOem" class="accunt-bind">
        <div class="one-line">
          <div>
            <span class="icon"></span>
            <span class="item-name">所在行业：</span>
            <span class="type-val">
              {{ industryObj[userInfo.industryId] || '其他' }}
            </span>
          </div>
          <el-button type="text" @click="industryDialog = true">修改行业</el-button>
        </div>
        <div class="one-line">
          <div>
            <span class="icon"></span>
            <span class="item-name">用户名：</span>
            <span class="type-val">
              {{ userInfo.userName }}
            </span>
          </div>
          <el-button type="text" @click="changeAcountName()">修改用户名</el-button>
        </div>
        <div class="one-line">
          <div>
            <span class="icon"></span>
            <span class="item-name">手机号：</span>
            <span class="type-val">{{ userInfo.phone }}</span>
          </div>
          <el-button type="text" @click="changeCellPhone()">修改手机</el-button>
        </div>
        <div class="one-line">
          <div :class="[userInfo.email ? '' : 'red-font']">
            <span class="icon"></span>
            <span class="item-name">保密邮箱：</span>
            <span class="type-val" v-if="!userInfo.email">未绑定</span>
            <span class="type-val" v-else>{{ userInfo.email }}</span>
            <span class="weixin-info">(可用于找回密码）</span>
          </div>
          <el-button type="text" @click="userInfo.email ? changeEmail() : bindEmail()">
            {{ userInfo.email ? '修改邮箱' : '绑定邮箱' }}
          </el-button>
        </div>
        <div class="one-line">
          <div>
            <span class="icon"></span>
            <span class="item-name">密码：</span>
            <span class="type-val">{{ userInfo.passwd ? '已设置' : '未设置' }}</span>
          </div>
          <el-button type="text" @click="changePwd()">{{ userInfo.passwd ? '修改密码' : '设置密码' }}</el-button>
        </div>
        <div class="one-line">
          <div :class="[bindOptions.weixin.id ? '' : 'red-font']">
            <span class="icon"></span>
            <span class="item-name">微信：</span>
            <span class="type-val" v-if="!isBindWx">未绑定</span>
            <span class="type-val" v-else>{{ bindOptions.weixin.nickName || '已绑定' }}</span>
            <span class="weixin-info">(可用于微信登录与接收网站账户提现）</span>
          </div>
          <el-button type="text" v-if="bindOptions.weixin.id" @click="unBindThirdParty('WEIXIN')">解除绑定</el-button>
          <el-button type="text" v-else @click="bindThirdParty('WEIXIN')">绑定微信</el-button>
        </div>
        <div class="one-line">
          <div></div>
          <el-button type="text" @click="handleLogout" style="color: #999">注销账号</el-button>
        </div>
      </div>
      <div v-if="!isOem" class="enterprise-bind">
        <el-form :model="enterpriseConfig" :rules="registerRule" ref="enterpriseForm" label-width="220px" class="mrg-t-15">
          <el-form-item label="企业认证：" prop="auditState">
            <span v-if="!isEnterprise">未认证</span>
            <span v-else>{{ enterpriseState[enterpriseConfig.auditState] }}</span>
            <span class="subtitle">已认证企业支持在线申请开票，企业认证上传后如需更改请联系客服</span>
          </el-form-item>

          <el-form-item label="企业/组织名称：" prop="companyName">
            <el-input
              v-if="!isEnterprise || enterpriseConfig.auditState === 'nopass'"
              v-model="enterpriseConfig.companyName"
              class="input-width"
              placeholder="请输入企业/组织名称"
              :maxlength="50"
              :show-word-limit="true"
            ></el-input>
            <div v-else>{{ enterpriseConfig.companyName }}</div>
          </el-form-item>
          <el-form-item class="toUpperCase" label="统一社会信用代码：" prop="creditCode">
            <el-input
              v-if="!isEnterprise || enterpriseConfig.auditState === 'nopass'"
              v-model="enterpriseConfig.creditCode"
              class="input-width"
              placeholder="统一社会信用代码"
              :maxlength="20"
              :show-word-limit="true"
            >
            </el-input>
            <div v-else>{{ enterpriseConfig.creditCode }}</div>
          </el-form-item>
          <el-form-item label="证书/执照：" prop="licenseImg">
            <div class="up-box">
              <hi-upload-img
                class="uploader"
                :action="$hi.url.upload"
                :disabled="enterpriseConfig.auditState !== 'nopass' && isEnterprise"
                :show-file-list="false"
                :on-success="(v) => (enterpriseConfig.licenseImg = Hi.String.dealUrl(v.data.url))"
              >
                <img v-if="enterpriseConfig.licenseImg" v-hiimg="enterpriseConfig.licenseImg" style="border-radius: 0" alt="“证书/执照”" />
                <i class="el-icon-plus uploader-icon"></i>
              </hi-upload-img>

              <div class="sub">
                <p>下列中任选一例上传即可：</p>
                <p>1.《社会团队法人登记证书》</p>
                <p>2.《民办非企业单位登记证书》</p>
                <p>3.《中华人民共和国组织机构代码证》</p>
                <p>4.《营业执照》</p>
              </div>
              <div class="sub">
                <p>注意事项：</p>
                <p>1.需清晰可见</p>
                <p>2.真实有效，不得更改</p>
                <p>推荐格式：png/jpg等</p>
                <p>推荐大小：不超过5M</p>
                <p>
                  <a href="#" @click.prevent="viewExample">查看示例</a>
                </p>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="">
            <div>
              <el-button
                v-if="!isEnterprise || enterpriseConfig.auditState === 'nopass'"
                class="mrg-r-10"
                type="primary"
                :disabled="!isEnterpriseUpdate"
                @click="saveEnterpriseInfo"
              >
                提交认证
              </el-button>
              <a href="#" @click.prevent="showAgreementDialog = true">查看《Hi现场公司认证服务协议》</a>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <el-dialog
      v-if="dialogFormVisible"
      custom-class="dialog-outer"
      width="470px"
      :class="['dialog-style', currentEvent === '人数详情' ? 'dialog-special' : '']"
      :title="title"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :lock-scroll="false"
      @open="openDialogOuter"
      @close="reset"
    >
      <p class="tit" :style="{ visibility: hintShow }">{{ hint }}</p>
      <!-- 更改昵称 -->
      <div v-show="currentEvent === '编辑企业名称/品牌名称'" class="set-nickname unify">
        <span>名称</span>
        <div class="code-box">
          <el-input v-model="reviseName" auto-complete="off" :maxlength="20"></el-input>
        </div>
      </div>

      <el-form :model="changePwdObj" :rules="rules" ref="changePwdForm">
        <!-- 更改密码 -->
        <div v-show="currentEvent === '修改密码' || currentEvent === '设置密码'" style="margin-top: 20px">
          <el-form-item v-if="currentEvent === '修改密码'" label="原密码" :label-width="formLabelWidth">
            <el-input v-model="changePwdObj.oldPasswd" auto-complete="off" type="password"></el-input>
          </el-form-item>
          <el-form-item label="新密码" :label-width="formLabelWidth" style="margin: 20px 0" prop="newPasswd">
            <el-input v-model="changePwdObj.newPasswd" auto-complete="off" type="password"></el-input>
          </el-form-item>
          <el-form-item label="确认新密码" :label-width="formLabelWidth" prop="confirmNewPwd">
            <el-input v-model="changePwdObj.confirmNewPwd" auto-complete="off" type="password"></el-input>
          </el-form-item>
        </div>
        <!-- 更改手机号 -->
        <div v-show="currentEvent === '修改手机号'" style="margin-top: 20px">
          <el-row type="flex" justify="space-between" v-if="verifyState === ''">
            <el-col :span="24">
              <el-form-item label="原手机号" :label-width="formLabelWidth" style="margin-bottom: 0">
                <el-input v-model.trim="changePhoneObj.oldPhone" auto-complete="off"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" justify="space-between" v-if="verifyState === 'SUCCESS'">
            <el-col :span="24">
              <el-form-item label="新手机号" :label-width="formLabelWidth">
                <el-input v-model.trim="changePhoneObj.newPhone" auto-complete="off"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="unify set-phone">
            <span>短信验证码</span>
            <div class="code-box">
              <el-input v-model="changePhoneObj.notCode" auto-complete="off"></el-input>
              <el-button type="primary" plain @click="getCode()" :disabled="disconfirm">{{ getTit }}</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <!-- 更改邮箱 -->
      <div v-show="currentEvent === '修改邮箱'">
        <div v-show="!dialogSendCode" :flag="flag">
          <div class="unify set-mail" v-show="verifyEmail === ''">
            <label>原邮箱</label>
            <div class="flex-1 pading10">
              <el-input v-model="changeMailObj.oldMailBox" auto-complete="off" placeholder="请输入原邮箱"> </el-input>
            </div>
          </div>
          <div class="unify set-mail" v-show="verifyEmail === 'SUCCESS'">
            <span>新邮箱</span>
            <div class="flex-1 pading10">
              <el-input v-model="changeMailObj.newMailBox" auto-complete="off" placeholder="请输入新邮箱"> </el-input>
            </div>
          </div>
          <div class="unify set-mail">
            <span>验证码</span>
            <div class="code-box">
              <el-input v-model="changeMailObj.verifyCodeNewMailBox" auto-complete="off"></el-input>
              <el-button type="primary" plain @click="getCode()" :disabled="disconfirm">{{ getTit }}</el-button>
            </div>
          </div>
        </div>
        <div class="bind-email" v-show="dialogSendCode" :flag="flag">
          <p>
            已发送验证邮件至
            <span>{{ bindMailObj.premail }}</span>
            <span>{{ bindMailObj.midmail }}</span>
            <span>{{ bindMailObj.nextmail }}</span>
          </p>
          <span>长时间未收到邮件?</span>
          <el-button type="text" @click="resend()">重新发送</el-button>
        </div>
      </div>
      <!-- 绑定邮箱 -->
      <div v-show="currentEvent === '绑定邮箱'">
        <div class="unify" v-show="!dialogSendCode" :flag="flag">
          <span>邮箱</span>
          <div class="code-box">
            <el-input v-model="bindMailObj.mailbox" auto-complete="off" placeholder="请输入要绑定的邮箱"> </el-input>
          </div>
        </div>
        <div class="bind-email" v-show="dialogSendCode" :flag="flag">
          <p>
            已发送验证邮件至
            <span>{{ bindMailObj.premail }}</span>
            <span>{{ bindMailObj.midmail }}</span>
            <span>{{ bindMailObj.nextmail }}</span>
          </p>
          <span>长时间未收到邮件?</span>
          <el-button type="text" @click="resend()">重新发送</el-button>
        </div>
      </div>
      <div v-if="currentEvent === '人数详情'">
        <el-table v-loading="pageLoad" class="table" style="width: 100%; font-size: 14px" :data="pageData.dataList">
          <el-table-column prop="date" label="" width="calc(100% - 285px)">
            <template slot-scope="scope">
              <span class="column_1">{{
                scope.row.changeType === 'ACT_ADD' ? `"${getWallName(scope.row.wallId).theme || scope.row.wallTheme}"活动 贡献` : '兑换嗨豆'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="changeNumber" label="人数" width="120">
            <template slot-scope="scope">
              <span :style="{ color: scope.row.changeType !== 'ACT_ADD' ? '#13ce66' : '#ff0000' }">
                {{ scope.row.changeType === 'ACT_ADD' ? '+' : '-' }}{{ scope.row.changeNumber }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="createDate" label="时间" width="165" :formatter="formatter" style="padding-left: 0"> </el-table-column>
        </el-table>
        <div class="mark">注：只计算参与人数超过100人，活动已结束或删除的活动。</div>
        <el-pagination
          background
          class="pagination"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageData.pageSize"
          :current-page="pageData.pageIndex"
          :total="pageData.total"
          @size-change="(v) => (pageData.pageSize = v)"
          @current-change="(v) => (pageData.pageIndex = v)"
        >
        </el-pagination>
      </div>
      <div v-if="currentEvent !== '人数详情'" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm()">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 验证码弹窗 -->
    <el-dialog
      title="请输入验证码"
      custom-class="dialog-inner"
      :visible.sync="dialogFormVisibleCode"
      :close-on-click-modal="false"
      width="470px"
      @open="openDialogInner"
    >
      <p class="tit" :style="{ visibility: changePhoneObj.codeHintShow }">{{ changePhoneObj.codeHint }}</p>
      <div class="set-code mrg-t-15">
        <span>验证码</span>
        <el-input v-model="changePhoneObj.imgvcode" auto-complete="off"></el-input>
        <div class="verify-code" @click="changeCode()"><img :src="changePhoneObj.verifyCode" alt="" /></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmCode()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="Hi现场公司认证服务协议"
      width="700px"
      :visible.sync="showAgreementDialog"
      :close-on-click-modal="false"
      @close="showAgreementDialog = false"
    >
      <div class="flex flex-d-c flex-a-c">
        <div class="agreement">
          <h2 class="text-center">公司认证服务协议1.0</h2>
          <div>
            <p>欢迎使用北京中网梵天互动娱乐科技公司的网站服务（www.hixianchang.com、www.fthuyu.com），以下简称“网站服务”。</p>
            <p>
              中网梵天用户账号所属公司实体的认证，是北京中网梵天互动娱乐科技公司提供给网站用户的一项用户资质认证服务，用于确认网站账号所属的唯一公司实体。
              以下简称“中网梵天认证服务”。
            </p>
            采用本服务须知：
            <p>1、需要您上传营业执照副本、统一社会信用代码等信息，均为您方企业授权可用，且真实有效；如有造假或企业追溯，您将承担相关责任。</p>
            <p>
              2、您上传的营业执照、统一社会信用代码等信息，仅用于中网梵天认证服务，不适用于其他服务。我们将严格保密，在未取得您的授权前提下，中网梵天不会向任何第三方披露、泄漏您的保密信息。
            </p>
            <p>以上内容，在您提交认证时，将被视为默认同意。</p>
          </div>
        </div>
        <el-button v-if="showAgreementButton" :disabled="count !== 0" type="primary" @click="handerAuthentication">
          {{ agreementButtonInnerText }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog width="400px" title="绑定微信" :visible.sync="wexinDialog" :close-on-click-modal="false" @close="closeQrdig">
      <div class="flex flex-j-c flex-a-c qrcode-box" v-loading="!wexinPathInfo.url">
        <hi-qrcode :value="wexinPathInfo.url" size="140" level="L"></hi-qrcode>
        <p class="qr-info" v-if="wexinPathInfo.expire > 0">请使用微信扫描二维码绑定</p>
        <div class="on-ser flex flex-j-c flex-a-c" v-if="wexinPathInfo.expire <= 0">
          <el-button type="primary" @click="getWxQrcodeUrl">刷新</el-button>
          <p class="qr-info">二维码已失效，请点击刷新</p>
        </div>
      </div>
    </el-dialog>
    <!--   修改行业信息的弹窗  -->
    <industry-dialog v-if="industryDialog" :user="userInfo" show-close @close="industryDialog = false" @save="saveIndustryCallback"></industry-dialog>

    <!-- 修改用户名 -->
    <el-dialog width="400px" title="修改用户名" :visible.sync="editNameDig" :close-on-click-modal="false">
      <el-form>
        <el-form-item label="用户名" :label-width="formLabelWidth">
          <el-input v-model="acountName" auto-complete="off" type="text"></el-input>
        </el-form-item>
      </el-form>
      <el-button :disabled="!acountName" class="query-acount" type="primary" @click="queryChangeAcountName">确定 </el-button>
    </el-dialog>

    <!-- 注销账号 -->
    <el-dialog width="520px" title="注销账号" :visible.sync="logoutDialog" :close-on-click-modal="false">
      <div class="logout">
        <p class="logout-title">
          <img src="https://res3.hixianchang.com/qn/up/fd55a6960f3376d0b5493ca7bafbc9b7.png" alt="" />
          确认注销当前账号
        </p>
        <p>请注意，注销完成后所有与当前账号关联的数据及服务将被永久</p>
        <p>删除，注销后立即生效，且无法恢复。</p>
        <p>请确保您已备份账号内重要信息，无未使用服务</p>
        <!-- 已知晓并同意 -->
        <el-checkbox v-model="isAgree">已知晓，继续注销</el-checkbox>
      </div>
      <!-- 验证码 -->
      <el-form class="mrg-t-20">
        <el-form-item label="手机号" label-width="80px">
          <div class="flex flex-a-c">
            <el-input v-model.number.lazy="logoutObj.phone" class="w-200" type="tel" :disabled="!isAgree" auto-complete="off"></el-input>
            <el-button class="mrg-l-10" type="text" @click="getVerifyCode" :disabled="!isAgree || disconfirm">{{ getTit }}</el-button>
          </div>
        </el-form-item>
        <el-form-item label="验证码" label-width="80px">
          <div class="verify-code">
            <el-input v-model="logoutObj.notCode" class="w-200" :disabled="!isAgree" auto-complete="off" type="text"></el-input>
          </div>
        </el-form-item>
      </el-form>
      <span class="flex flex-j-fe">
        <el-button class="mrg-t-10" type="primary" :disabled="logoutDisabled" @click="logoutSubmit">注销</el-button>
        <el-button class="mrg-t-10" plain @click="logoutDialog = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api'
import { Hi, fenToYuan, timer } from '@/libs/common'
import IndustryDialog from '@/views/common/industry/index.vue'
import { mapActions, mapGetters, mapMutations } from 'vuex'

let checkPass = (_, value, callback) => {
  let len = value.length
  if (!value || len < 8 || len > 20) {
    callback(new Error('请输入8-20位密码'))
  } else {
    // 检查密码复杂度
    let hasUpperCase = /[A-Z]/.test(value)
    let hasLowerCase = /[a-z]/.test(value)
    let hasNumber = /\d/.test(value)
    let hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value)

    let complexityCount = [hasUpperCase, hasLowerCase, hasNumber, hasSpecialChar].filter(Boolean).length

    if (complexityCount < 2) {
      callback(new Error('密码至少包含大写、小写、数字、特殊符号中的两种'))
    } else {
      callback()
    }
  }
}
let infoTimer = null
export default {
  components: { IndustryDialog },
  data() {
    return {
      Hi,
      editNameDig: false,
      pageLoad: false,
      dialogVisible: false,
      // 头像上传
      dialogImageUrl: require('@/assets/user/default.png'),
      // 弹窗
      dialogFormVisible: false,
      dialogFormVisibleCode: false, // 验证码弹窗
      formLabelWidth: '82px',
      // 用户信息
      accountObj: {},
      currentEvent: '', //控制弹窗显示与隐藏
      // 活动信息
      // 邮箱的绑定还是修改
      showeEmail: false,
      // 对话框的提示信息
      hint: '',
      hintShow: 'hidden',
      // 修改昵称数据绑定
      reviseName: '',
      // 修改密码数据绑定
      changePwdObj: {
        oldPasswd: '',
        newPasswd: '',
        confirmNewPwd: '',
      },
      // 更改手机号数据绑定
      changePhoneObj: {
        oldPhone: '',
        newPhone: '',
        notCode: '', // 短信验证码
        verifyCode: '', // 图片验证码地址
        imgvcode: '', // 图片验证码 input
        codeHint: '', // 图片验证码提示信息
        codeHintShow: 'hidden', // 图片验证码提示信息显示隐藏
      },
      //绑定邮箱 数据绑定
      bindMailObj: {
        mailbox: '',
        premail: '', // 邮箱地址的拆分
        midmail: '',
        nextmail: '',
      },
      // 修改邮箱数据绑定
      changeMailObj: {
        oldMailBox: '',
        newMailBox: '',
        verifyCodeNewMailBox: '',
      },
      changeHaidouObj: {
        personCount: 10,
        changeCount: null,
        pass: '',
      },
      // 注销账号数据绑定
      logoutObj: {
        phone: '',
        notCode: '',
        verifyCode: '',
      },
      // 弹窗相关
      title: '', // 弹窗标题
      getTit: '获取验证码', // 获取图片code的 butoon内容
      // 控制弹窗的显示隐藏
      dialogSendCode: false,
      // dialog 确定按钮 是否关闭dialog
      flag: false,
      // 发送验证码按钮状态
      disconfirm: false,
      count: 60,
      unbIndComeFrom: '',
      verifyState: '', // 原手机号验证状态
      verifyEmail: '', // 原邮箱的验证状态

      pageData: {
        dataList: [],
        pageSize: 10,
        pageIndex: 1,
        total: 0,
      },
      activeObj: {},
      //三方绑定
      bindOptions: {
        weixin: {},
      },
      wexinDialog: false,
      wexinPathInfo: {
        url: '',
        expire: 0,
      },
      wallwxuserInfo: '', //举报场次数据
      industryDialog: false,
      industryObj: {},
      acountName: '',
      enterpriseConfig: {},
      enterpriseConfigOriginal: {},
      exampleImageUrl: 'https://res3.hixianchang.com/qn/up/c66701beac0e1e7830834ece9ee60d6e.png',
      showAgreementDialog: false,
      showAgreementButton: false,
      agreementButtonInnerText: '我已阅读并同意(5s)',
      enterpriseState: {
        pass: '已认证',
        waitaudit: '审核中',
        nopass: '审核未通过',
      },
      registerRule: {
        companyName: [{ required: true, message: '企业/组织名称不能为空', trigger: 'blur' }],
        creditCode: [{ required: true, message: '统一社会信用代码不能为空', trigger: 'blur' }],
        licenseImg: [{ required: true, message: '请上传证书/执照', trigger: 'blur' }],
      },
      logoutDialog: false,
      isAgree: false,
    }
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/getUser',
      showOpenVip: 'user/showOpenVip', // 显示vip
      usermember: 'user/getUsermember',
      isOem: 'user/isOem',
    }),
    isBindWx() {
      return !!this.bindOptions.weixin.id
    },
    showTips() {
      return this.changeHaidouObj.pass === false
    },
    showHibenInfo() {
      return !this.userInfo.registerSource || (this.userInfo.registerSource && this.userInfo.registerSource !== 'OPEN_PLATFORM')
    },
    accountTypeInfo() {
      let info = { name: '普通用户' }
      if (this.userInfo.schoolFlag === 'Y') {
        info.name = '校园用户'
        info.type = 'SCHOOL'
        info.icon = require('@/assets/user/school.png')
      } else if (this.userInfo.role === 'WEDDING') {
        info.name = '婚庆用户'
        info.type = 'WEDDING'
        info.icon = require('@/assets/user/wedding.png')
      } else if (this.userInfo.role === 'VIP') {
        info.type = 'VIP'
        if (this.usermember.authType === 'BASE') {
          info.name = '基础会员'
          info.icon = require('@/assets/user/base.png')
        }
        if (this.usermember.authType === 'STANDARD') {
          info.name = '标准会员'
          info.icon = require('@/assets/user/standard.png')
        }
      }
      return info
    },
    vipDateRange() {
      try {
        let startDate = this.usermember.startDate
        let endDate = this.usermember.endDate
        return Hi.Date.format(startDate, 'yyyy年MM月dd日') + '-' + Hi.Date.format(endDate, 'yyyy年MM月dd日')
      } catch (error) {}
    },
    isEnterprise() {
      return !!(this.enterpriseConfig && this.enterpriseConfig.id)
    },
    isEnterpriseUpdate() {
      return !!Hi.Object.difference(this.enterpriseConfig, this.enterpriseConfigOriginal)
    },
    logoutDisabled() {
      return !this.isAgree || !this.logoutObj.phone || !this.logoutObj.notCode
    },
    rules() {
      return {
        newPasswd: [{ validator: checkPass, trigger: 'blur' }],
        confirmNewPwd: [{ validator: checkPass, trigger: 'blur' }],
      }
    },
  },
  watch: {
    'pageData.pageIndex'() {
      this.renderPage.call(this)
    },
    'pageData.pageSize'() {
      this.renderPage.call(this)
    },
    showAgreementDialog(val) {
      if (val) {
        // 5s倒计时逻辑
        this.count = 5
        clearInterval(infoTimer)
        this.agreementButtonInnerText = `我已阅读并同意(${this.count}s)`
        infoTimer = setInterval(() => {
          this.agreementButtonInnerText = `我已阅读并同意(${--this.count}s)`
          if (this.count === 0) {
            clearInterval(infoTimer)
            this.agreementButtonInnerText = `我已阅读并同意`
          }
        }, 1000)
      } else {
        this.showAgreementButton = false
      }
    },
  },
  methods: {
    ...mapActions({
      fetchUser: 'user/fetchUser',
      logout: 'user/logout',
      fetchUserconfig: 'user/fetchUserconfig',
    }),
    ...mapMutations({
      setShowVip: 'user/setShowVip',
    }),
    handleLogout() {
      this.isAgree = false
      this.logoutDialog = true
    },
    async getVerifyCode() {
      if (!this.isAgree) {
        this.$notify.error({ title: '错误', message: '请先同意协议' })
        return
      }

      if (!this.userInfo.phone) {
        this.$notify.error({ title: '错误', message: '请先绑定手机号' })
        return
      }

      // 手机号验证
      if (!this.logoutObj.phone) {
        this.$notify.error({ title: '错误', message: '请输入手机号' })
        return
      }

      if (!/^1[3456789]\d{9}$/.test(this.logoutObj.phone)) {
        this.$notify.error({ title: '错误', message: '请输入正确的手机号' })
        return
      }

      try {
        await api.user.checkPhoneMatch({ phone: this.logoutObj.phone })
        this.title = '注销账号'
        this.dialogFormVisibleCode = true
        this.changePhoneObj.verifyCode = '/pro/hxc/verification/imgCode.htm?v=' + Date.now()
        this.changePhoneObj.oldPhone = this.logoutObj.phone
      } catch (e) {
        this.$notify.error({ title: '错误', message: e.msg || e.message })
        return
      }
    },
    async logoutSubmit() {
      try {
        await api.user.deregister({
          vcode: this.logoutObj.notCode,
        })
        this.$notify.success({ title: '成功', message: '注销成功' })
        await timer(1000)
        this.$router.push({ name: 'user-login' })
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    changeAcountName() {
      this.acountName = this.userInfo.userName
      this.editNameDig = true
    },
    async queryChangeAcountName() {
      try {
        if (/.*[\u4e00-\u9fa5]+.*$/.test(this.acountName)) {
          throw new Error('用户名不允许出现中文')
        }
        await api.user.updateUserName({
          userName: this.acountName,
        })
        this.editNameDig = false
        await this.getUserInfo()
        this.$notify.success({ title: '成功', message: '修改成功' })
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg || e.message })
      }
    },
    // 获取用户信息
    async getUserInfo() {
      try {
        await this.fetchUser()
        this.dialogImageUrl = this.userInfo.headPic
        this.reviseName = this.userInfo.nickName
        this.userInfo.email ? (this.showeEmail = true) : (this.showeEmail = false)
        await this.updateAccountObj.call(this)
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    async updateAccountObj() {
      try {
        this.accountObj = await api.useraccount.read({})
        this.changeHaidouObj.personCount = this.accountObj.remainingWallwxuser
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    // 头像上传
    uploadSuccess(file) {
      file = file.data
      this.dialogImageUrl = file.url
      this.reviseUserInfo.call(this)
      this.$notify.success({ title: '成功', message: '头像修改成功' })
    },
    // 更改昵称 和头像
    async reviseUserInfo() {
      try {
        await api.user.update({
          nickName: this.reviseName,
          headPic: this.dialogImageUrl,
        })
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    //更改密码
    async revisePwd() {
      try {
        let params = {
          oldPasswd: this.changePwdObj.oldPasswd,
          newPasswd: this.changePwdObj.newPasswd,
        }

        // 没有密码就不需要传 oldPasswd
        if (!this.userInfo.passwd) {
          delete params.oldPasswd
        }
        await api.user.updatePasswd(params)
        await this.fetchUserconfig()
        this.$notify.success({ title: '成功', message: '密码修改成功' })
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    // 变更手机号  发送验证码  到手机
    async getCodeToPhone() {
      try {
        await api.verification.msgCode({
          phone: this.verifyState === 'SUCCESS' ? this.changePhoneObj.newPhone : this.changePhoneObj.oldPhone,
          imgvcode: this.changePhoneObj.imgvcode,
        })
        this.$notify.success({ title: '成功', message: '验证码发送成功' })
        this.dialogFormVisibleCode = false
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
        this.dialogFormVisibleCode = true
        this.changeCode()
      }
    },
    // 验证 原手机号
    async verifyOldPhone() {
      try {
        let data = await api.user.checkPhone({
          oldPhone: this.changePhoneObj.oldPhone,
          vcode: this.changePhoneObj.notCode,
        })
        this.verifyState = data
        this.count = 0
        this.changePhoneObj.notCode = ''
      } catch (e) {
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    // 更改手机号
    async reviseCellPhone() {
      try {
        await api.user.updatePhone({
          newPhone: this.changePhoneObj.newPhone,
          vcode: this.changePhoneObj.notCode,
        })
        this.$notify.success({ title: '成功', message: '手机号修改成功' })
        await this.getUserInfo.call(this)
        this.dialogFormVisible = false
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
        this.dialogFormVisible = true
      }
    },

    // 更改邮箱时 发送验证码到邮箱
    async getCodeToEmail() {
      try {
        await api.verification.emailCode({
          email: this.verifyEmail === 'SUCCESS' ? this.changeMailObj.newMailBox : this.changeMailObj.oldMailBox,
          imgvcode: this.changePhoneObj.imgvcode,
        })
        this.$notify.success({ title: '成功', message: '验证码发送成功' })
        this.dialogFormVisibleCode = false
      } catch (err) {
        console.log(err)
        this.$notify.error({ title: '错误', message: err.msg })
        this.dialogFormVisibleCode = true
      }
    },
    // 验证原邮箱
    async verifyOldEmail() {
      try {
        let data = await api.user.checkEmail({
          oldEmail: this.changeMailObj.oldMailBox,
          emailvcode: this.changeMailObj.verifyCodeNewMailBox,
        })
        this.verifyEmail = data
        this.count = 0
        this.changeMailObj.verifyCodeNewMailBox = ''
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    // 变更邮箱
    async reviseMailBox() {
      try {
        await api.user.updateEmail({
          newEmail: this.changeMailObj.newMailBox,
          emailvcode: this.changeMailObj.verifyCodeNewMailBox,
        })
        this.$notify.success({ title: '成功', message: '邮箱修改成功' })
        await this.getUserInfo.call(this)
        this.dialogFormVisible = false
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
        this.dialogFormVisible = true
      }
    },
    // 绑定邮箱账户邮箱发送链接
    async getEmailCode() {
      try {
        await api.user.sendEmailBoundLink({
          email: this.bindMailObj.mailbox,
        })
        this.$notify.success({ title: '成功', message: '发送成功！' })
        this.dialogSendCode = true
        this.flag = true
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
        this.dialogFormVisible = true
      }
    },

    // 点击事件
    changeNickName() {
      this.currentEvent = '编辑企业名称/品牌名称'
      this.dialogFormVisible = true
      this.title = '编辑企业名称/品牌名称'
    },
    changePwd() {
      this.currentEvent = this.userInfo.passwd ? '修改密码' : '设置密码'
      this.dialogFormVisible = true
      this.title = this.userInfo.passwd ? '修改密码' : '设置密码'
    },
    changeCellPhone() {
      this.currentEvent = '修改手机号'
      this.dialogFormVisible = true
      this.title = '修改手机号'
    },
    changeEmail() {
      this.currentEvent = '修改邮箱'
      this.dialogFormVisible = true
      this.title = '修改邮箱'
    },
    bindEmail() {
      this.currentEvent = '绑定邮箱'
      this.dialogFormVisible = true
      this.title = '绑定邮箱'
    },
    successTip() {
      this.dialogFormVisible = true
      this.currentEvent = '嗨豆兑换成功'
      this.title = '提示'
    },
    haidouInputChange(e) {
      if (this.changeHaidouObj.changeCount > this.changeHaidouObj.personCount) {
        this.changeHaidouObj.changeCount = this.changeHaidouObj.personCount
      }
    },
    haidouBlur() {
      let val = Number(this.changeHaidouObj.changeCount)
      if (val > 0 && val % 100 === 0) {
        this.changeHaidouObj.pass = true
      } else {
        this.changeHaidouObj.pass = false
      }
    },
    toHaidou() {
      this.reset.call(this)
      this.dialogFormVisible = false
      this.$router.push({
        name: 'user-center-hibean',
      })
    },
    // 弹窗确认事件
    async confirm() {
      if (this.title === '编辑企业名称/品牌名称') {
        if (!this.reviseName) {
          this.hintShow = 'visible'
          this.hint = '企业名称/品牌名称不能为空'
          this.dialogFormVisible = true
          return false
        }
        if (this.reviseName.length > 20) {
          this.hintShow = 'visible'
          this.hint = '企业名称/品牌名称不能超过20个字'
          this.dialogFormVisible = true
          return false
        }
        await this.reviseUserInfo.call(this)
        this.$notify.success({ title: '成功', message: '修改成功' })
        await this.getUserInfo.call(this)
        this.dialogFormVisible = false
      }
      // 修改密码
      if (this.title === '修改密码' || this.title === '设置密码') {
        await this.$refs.changePwdForm.validate()
        if (!this.changePwdObj.oldPasswd && this.userInfo.passwd) {
          this.hintShow = 'visible'
          this.hint = '请输入原密码'
          return false
        }
        if (!this.changePwdObj.newPasswd) {
          this.hintShow = 'visible'
          this.hint = '请输入新密码'
          return false
        }
        if (this.changePwdObj.newPasswd !== this.changePwdObj.confirmNewPwd) {
          this.hintShow = 'visible'
          this.hint = '两次输入的密码不一致'
          return false
        }
        this.revisePwd.call(this)
        this.dialogFormVisible = false
      }
      // 手机号修改
      if (this.title === '修改手机号') {
        var reg = /^[0-9]{11}$/
        if (!this.verifyState) {
          if (!this.changePhoneObj.oldPhone) {
            this.hintShow = 'visible'
            this.hint = '原手机号不能为空'
            return false
          }
          if (!reg.test(this.changePhoneObj.oldPhone)) {
            this.hintShow = 'visible'
            this.hint = '原手机号码格式不正确'
            return false
          }
        }
        if (this.verifyState) {
          if (!this.changePhoneObj.newPhone) {
            this.hintShow = 'visible'
            this.hint = '新手机号不能为空'
            return false
          }
          if (!reg.test(this.changePhoneObj.newPhone)) {
            this.hintShow = 'visible'
            this.hint = '新手机号码格式不正确'
            return false
          }
        }
        if (!this.changePhoneObj.notCode) {
          this.hintShow = 'visible'
          this.hint = '短信验证码不能为空'
          return false
        }
        if (this.verifyState === '') {
          await this.verifyOldPhone.call(this)
        } else {
          await this.reviseCellPhone.call(this)
        }
      }
      // 邮箱修改
      if (this.title === '修改邮箱') {
        var reg =
          /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
        if (!this.verifyEmail) {
          if (!this.changeMailObj.oldMailBox) {
            this.hintShow = 'visible'
            this.hint = '原邮箱不能为空'
            return false
          }
          if (!reg.test(this.changeMailObj.oldMailBox)) {
            this.hintShow = 'visible'
            this.hint = '原邮箱格式不正确'
            return false
          }
        }
        if (this.verifyEmail) {
          if (!this.changeMailObj.newMailBox) {
            this.hintShow = 'visible'
            this.hint = '新邮箱不能为空'
            return false
          }
          if (!reg.test(this.changeMailObj.newMailBox)) {
            this.hintShow = 'visible'
            this.hint = '新邮箱格式不正确'
            return false
          }
        }
        if (!this.changeMailObj.verifyCodeNewMailBox) {
          this.hintShow = 'visible'
          this.hint = '验证码不能为空'
          return false
        }
        if (this.verifyEmail === '') {
          await this.verifyOldEmail.call(this)
        } else {
          await this.reviseMailBox.call(this)
        }
      }
      // 邮箱绑定
      if (this.title === '绑定邮箱') {
        var reg =
          /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
        if (!this.bindMailObj.mailbox) {
          this.hintShow = 'visible'
          this.hint = '邮箱不能为空'
          return false
        }
        if (!reg.test(this.bindMailObj.mailbox)) {
          this.hintShow = 'visible'
          this.hint = '邮箱格式不正确'
          return false
        }
        if (!this.flag) {
          var len = this.bindMailObj.mailbox.length
          var index = this.bindMailObj.mailbox.indexOf('@') - 1
          var str = ''
          this.bindMailObj.premail = this.bindMailObj.mailbox.substring(0, 1)
          for (var i = 0; i < index - 1; i++) {
            str += '*'
          }
          this.bindMailObj.midmail = str
          this.bindMailObj.nextmail = this.bindMailObj.mailbox.substring(index, len)
          this.getEmailCode.call(this)
        } else {
          this.dialogFormVisible = false
        }
      }
      if (this.title === '兑换嗨豆') {
        if (!this.changeHaidouObj.pass) {
          return
        }

        try {
          await api.useraccount.convertPoints({
            convertNumber: Number(this.changeHaidouObj.changeCount),
          })
          await this.updateAccountObj.call(this)
          this.reset.call(this)
          this.dialogFormVisible = false
          this.successTip.call(this)
        } catch (e) {
          console.log(e)
          this.$notify.error({ title: '错误', message: e.msg })
        }
      }
    },
    // 在验证码弹窗的确认事件
    async confirmCode() {
      function time(o) {
        if (o.count === 0) {
          o.disconfirm = false
          o.getTit = '获取验证码'
          o.count = 60
          o.disconfirm = false
        } else {
          o.disconfirm = true
          o.getTit = '重新发送(' + o.count + ')秒'
          o.count--
          o.disconfirm = true
          setTimeout(function () {
            time(o)
          }, 1000)
        }
      }

      if (!this.changePhoneObj.imgvcode) {
        this.changePhoneObj.codeHintShow = 'visible'
        this.changePhoneObj.codeHint = '验证码不能为空'
        this.dialogFormVisibleCode = true
        return false
      }

      if (this.title === '修改手机号' || this.title === '注销账号') {
        await this.getCodeToPhone.call(this)
      }
      if (this.title === '修改邮箱') {
        await this.getCodeToEmail.call(this)
      }
      if (!this.dialogFormVisibleCode) {
        this.count = 60
        time(this)
        this.dialogFormVisibleCode = false
      }
    },
    // 获取验证码
    getCode() {
      var reg =
        /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
      let phoneReg = /^[0-9]{11}$/
      if (this.title === '修改手机号' && !this.verifyState) {
        if (!this.changePhoneObj.oldPhone) {
          this.hintShow = 'visible'
          this.hint = '原手机号不能为空'
          return false
        }
        if (!phoneReg.test(this.changePhoneObj.oldPhone)) {
          this.hintShow = 'visible'
          this.hint = '原手机号格式不正确'
          return false
        }
      }
      if (this.title === '修改手机号' && this.verifyState) {
        if (!this.changePhoneObj.newPhone) {
          this.hintShow = 'visible'
          this.hint = '新手机号不能为空'
          return false
        }

        if (!phoneReg.test(this.changePhoneObj.newPhone)) {
          this.hintShow = 'visible'
          this.hint = '新手机号格式不正确'
          return false
        }
      }

      if (this.title === '修改邮箱' && !this.verifyEmail) {
        if (!this.changeMailObj.oldMailBox) {
          this.hintShow = 'visible'
          this.hint = '原邮箱不能为空'
          return false
        }
        if (!reg.test(this.changeMailObj.oldMailBox)) {
          this.hintShow = 'visible'
          this.hint = '原邮箱格式不正确'
          return false
        }
      }
      if (this.title === '修改邮箱' && this.verifyEmail) {
        if (!this.changeMailObj.newMailBox) {
          this.hintShow = 'visible'
          this.hint = '新邮箱不能为空'
          return false
        }
        if (!reg.test(this.changeMailObj.newMailBox)) {
          this.hintShow = 'visible'
          this.hint = '新邮箱格式不正确'
          return false
        }
      }
      this.dialogFormVisibleCode = true
      this.changePhoneObj.verifyCode = '/pro/hxc/verification/imgCode.htm?v=' + Date.now()
    },
    // 更改验证码
    changeCode() {
      this.changePhoneObj.verifyCode = '/pro/hxc/verification/imgCode.htm?v=' + Date.now()
    },
    // 重新发送 邮箱绑定邮件
    resend() {
      this.getEmailCode.call(this)
    },
    // 外层弹窗打开事件
    openDialogOuter(done) {},
    reset() {
      this.hintShow = 'hidden' // 隐藏错误提示
      this.changePwdObj.oldPasswd = ''
      this.changePwdObj.newPasswd = ''
      this.changePwdObj.confirmNewPwd = ''
      this.changePhoneObj.oldPhone = ''
      this.changePhoneObj.newPhone = ''
      this.changePhoneObj.notCode = ''
      this.bindMailObj.mailbox = ''
      this.changeMailObj.oldMailBox = ''
      this.changeMailObj.newMailBox = ''
      this.changePhoneObj.imgvcode = ''
      this.changeMailObj.verifyCodeNewMailBox = ''
      this.reviseName = this.userInfo.nickName
      this.companyName = this.userInfo.companyName
      this.disconfirm = false
      this.getTit = '点击获取'
      this.count = 0
      this.verifyEmail = ''
      this.changeHaidouObj.changeCount = null
      this.changeHaidouObj.pass = ''
      this.pageData = {
        dataList: [],
        pageSize: 10,
        pageIndex: 1,
        total: 0,
      }
    },
    // 内层弹窗打开事件
    openDialogInner() {
      this.hintShow = 'hidden' // 隐藏错误提示
      this.changePhoneObj.imgvcode = ''
    },
    // 获取第三方绑定信息
    async thirdPartyList() {
      try {
        let data = await api.thirdwxuser.getBoundWxUser({ type: 'WEIXIN' })
        if (data) {
          this.bindOptions.weixin = data
        } else {
          this.bindOptions.weixin = {}
        }
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    // 第三方绑定。目前只是微信
    async bindThirdParty() {
      this.wexinDialog = true
      await this.getWxQrcodeUrl()
    },
    async getWxQrcodeUrl() {
      try {
        this.wexinPathInfo = {}
        this.wexinPathInfo = await api.user.boundWxSqrcode()
        clearInterval(this.timerBack)
        clearInterval(this.timerCheck)
        this.timerBack = setInterval(() => {
          if (this.wexinPathInfo.expire > 0) {
            this.wexinPathInfo.expire--
          } else {
            clearInterval(this.timerBack)
            clearInterval(this.timerCheck)
          }
        }, 1000)
        this.timerCheck = setInterval(async () => {
          await this.thirdPartyList()
          if (this.bindOptions.weixin.id) {
            clearInterval(this.timerCheck)
            this.wexinDialog = false
            this.$notify.success({ title: '成功！', message: '绑定成功！' })
          }
        }, 1500)
      } catch (err) {
        console.log(err)
      }
    },
    // 第三方的解绑
    async unBindThirdParty() {
      await this.$confirm('确认解除与该微信的绑定？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      try {
        await api.thirduser.unBound({ type: 'WEIXIN' })
        this.thirdPartyList()
        this.$notify.success({ title: '成功', message: '解绑成功' })
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg || '操作失败！' })
      }
    },
    closeQrdig() {
      clearInterval(this.timerBack)
      clearInterval(this.timerCheck)
    },
    async renderPage() {
      try {
        let pageData = await api.wxusertrade.page({
          where: {},
          sort: {
            createDate: 'desc',
          },
          pageIndex: this.pageData.pageIndex,
          pageSize: this.pageData.pageSize,
        })
        await this.relationActiveName.call(this, pageData.dataList)
        this.pageData = pageData
      } catch (error) {
        console.log(error)
      }
    },
    getWallName(id) {
      return this.activeObj[id] || {}
    },
    async relationActiveName(dataArr) {
      if (!dataArr || !dataArr.length) {
        return
      }
      let idList = []
      dataArr.filter((item) => item.changeType === 'ACT_ADD').forEach((item) => this.activeObj[item.wallId] || idList.push(item.wallId))
      if (idList.length) {
        try {
          let wallArr = await api.wall.list({
            where: {
              idList: idList,
              deleteTagAll: 'Y',
            },
          })
          wallArr.forEach((item) => (this.activeObj[item.id] = item))
        } catch (e) {
          console.log(e)
        }
      }
    },
    formatter(a, b, c, d) {
      return Hi.Date.format(c, 'yyyy-MM-dd hh:mm')
    },
    // 获取用户超过20人活动的次数
    async getWallwxuserInfo() {
      try {
        this.wallwxuserInfo = await api.wallwxuser.gt20Count({})
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    // 获取用户行业
    async initIndustry() {
      try {
        const data = await api.wallindustry.list({ sort: { sort: 'asc' } })
        const obj = {}
        data.forEach((item) => {
          obj[item.id] = item.name
        })
        this.industryObj = obj
      } catch (e) {}
    },
    // 保存用户行业完成，重新获取用户信息
    async saveIndustryCallback() {
      try {
        this.$notify.success({ title: '成功', message: '所在行业修改成功' })
        this.industryDialog = false
        this.getUserInfo()
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    async saveEnterpriseInfo() {
      await this.$refs.enterpriseForm.validate()
      this.showAgreementDialog = true
      this.showAgreementButton = true
    },
    async handerAuthentication() {
      try {
        if (this.enterpriseConfig.id) {
          const update = Hi.Object.difference(this.enterpriseConfigOriginal, this.enterpriseConfig)
          if (update) {
            await api.companycertification.update({
              where: {
                id: this.enterpriseConfig.id,
              },
              update,
            })
          }
        } else {
          await api.companycertification.add(this.enterpriseConfig)
        }
        this.$notify.success({ title: '成功', message: '企业信息修改成功' })
        await this.fetchEnterpriseInfo()
        this.showAgreementDialog = false
      } catch (e) {
        console.log(e)
        this.$notify.error({ title: '错误', message: e.msg })
      }
    },
    viewExample() {
      const h = this.$createElement
      this.$msgbox({
        title: '示例',
        message: h('div', {
          style: {
            width: '1000px',
            height: '674px',
            backgroundImage: `url(${this.exampleImageUrl})`,
            backgroundSize: 'cover',
          },
        }),
        customClass: 'user-view-example',
        showCancelButton: false,
        showConfirmButton: false,
        beforeClose: (action, _, done) => {
          done()
        },
      })
    },
    async fetchEnterpriseInfo() {
      try {
        const data = await api.companycertification.read({
          where: {
            userId: this.userInfo.id,
          },
        })
        this.enterpriseConfig =
          data && data.id
            ? data
            : {
                auditState: 'waitaudit',
                companyName: '',
                creditCode: '',
                licenseImg: '',
              }
        this.enterpriseConfigOriginal = Hi.Object.copy(this.enterpriseConfig)
      } catch (error) {
        console.log(error)
      }
    },
    async preloadImageFunc() {
      const preloadImage = (url) => {
        return new Promise((resolve, reject) => {
          const img = new Image()
          img.onload = () => resolve(url)
          img.onerror = () => reject(new Error('Could not load image at ' + url))
          img.src = url
        })
      }
      await preloadImage(this.exampleImageUrl)
    },
  },
  async mounted() {
    if (!this.userInfo.id) {
      await this.fetchUser()
    }
    // 获取 params传参
    let params = this.$route.params
    await this.fetchEnterpriseInfo()

    if (Object.keys(params).length) {
      const newConfig = Object.assign({}, this.enterpriseConfig, params)
      if (!this.enterpriseConfig.id || this.enterpriseConfig.auditState === 'nopass') {
        this.enterpriseConfig = newConfig
        this.enterpriseConfigOriginal = {
          auditState: 'waitaudit',
          companyName: '',
          creditCode: '',
          licenseImg: '',
        }
      }
    }

    await Promise.all([this.thirdPartyList(), this.getUserInfo(), this.initIndustry(), this.getWallwxuserInfo(), this.preloadImageFunc()])
    // TODO: 没有手机的号的时候不验证原手机号
    if (!this.userInfo.phone) {
      this.verifyState = 'SUCCESS'
    }
  },
  destroyed() {
    clearInterval(this.timerBack)
    clearInterval(this.timerCheck)
  },
}
</script>
<style>
.user-view-example {
  width: auto !important;
}
</style>
<style scoped lang="stylus">
.input-width {
  width: 506px;
}

.text-center {
  text-align: center;
}

.pading10 {
  padding-right: 10px;
}

.accountinfo-box {
  min-width: 1100px;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 7px;
  margin: 18px 0;

  // height calc(100% - 36px) !important
  // 内容部分
  .info-content {
    flex: 1;
    padding: 31px 25px 0;

    .info-top {
      .tit {
        position: absolute;
        width: 470px;
        background-color: #ff7777;
        text-align: center;
        height: 30px;
        line-height: 30px;
        color: #fff;
        font-size: 14px;
        padding: 0;
        margin: -30px 0 20px -20px;
      }
    }
  }

  >>> .el-upload {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 0;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }

    p {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.5);
      position: absolute;
      left: 0;
      top: 0;
      visibility: hidden;
      color: #fff;
      text-align: center;
      line-height: 120px;
    }

    &:hover {
      p {
        visibility: visible;
      }
    }
  }

  >>> .el-dialog__body {
    padding: 30px 20px;
  }
}

.accunt-bind, .accountinfo-box .info-content .info-top {
  width: 100%;
  height: 162px;
  border: 1px solid #dfe6ec;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  overflow: hidden;
  margin-bottom: 25px;
  font-family: 'Microsoft YaHei';
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.user-pic {
  width: 120px;
  height: 120px;
  position: relative;
  margin-right: 28px;

  img {
    object-fit: contain;
    object-position: center;
  }
}

.user-nickname {
  font-size: 14px;
  color: #333;
  display: flex;
  flex-direction: column;

  .name {
    font-size: 18px;
    display: flex;
    align-items: flex-start;
    height: 42px;
  }

  .nickname {
    display: inline-block;
    max-width: 272px;
    word-break: break-all;
    margin-right: 10px;
    line-height: 1.2;
    height: auto;
  }

  .el-button {
    font-size: 14px;
    padding: 0;
    padding-top: 6px;
  }
}

.account-type {
  margin-top: 10px;
  display: flex;
  align-items: center;

  .dredge-vip {
    width: 72px;
    height: 23px;
    background: linear-gradient(270deg, #FBE3B7 0%, #F9D485 100%);
    border-radius: 4px;
    border: 1px solid #F2C96B;
    text-align: center;
    line-height: 23px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #A15B00;
    margin-left: 10px;
    cursor: pointer;
  }
}

.role-icon {
  width: 20px;
  height: 20px;
  margin-left: 5px;
}

.wedding {
  width: 22px;
  height: 22px;
  background: #e33e33;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 22px;
  margin-left: 5px;
}

.haidou-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 70px;

  & > div {
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: #999;
    font-size: 14px;
  }

  .line {
    width: 1px;
    height: 100%;
    background-color: #cdcdcd;
    margin: 0 15px 0 30px;
  }

  .num {
    font-size: 30px;
    color: #333;
    margin-bottom: 4px;
  }

  .partnum-box {
    .num {
      padding-right: 45px;
    }
  }
}

.haidoubtn {
  position: absolute;
  right: -8px;
  top: -7px;
  border: 1px solid #4886ff;
  border-radius: 4px;
  padding: 5px;
  color: #4886ff;
  font-size: 12px;
}

.accunt-bind {
  height: auto;
  padding: 18px 50px;
  flex-direction: column;

  .one-line {
    width: calc(100% - 88px);
    padding: 10px 0 10px 88px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    color: #333;
  }
}

.one-line {
  & > div {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .icon {
    display: inline-block;
    width: 18px;
    height: 18px;
    margin-right: 12px;
  }

  .type-val {
    width: 220px;
    margin-left: 90px;
    display: flex;
    align-items: center;
  }

  .weixin-name {
    margin: 0 30px;
  }

  .weixin-info {
    margin: 0 25px;
    color: #999;
  }

  .el-button {
    padding: 0;
    font-size: 14px;
    width: 60px;
    text-align: left;
  }

  .item-name {
    width: 70px;
    text-align: right;
  }

  .red-font {
    .type-val {
      color: #ff0000;
    }
  }
}

.dialog-special {
  margin: 0 auto;

  >>> .el-dialog {
    width: 800px !important;
  }
}

.dialog-style {
  >>> .el-dialog__title {
    font-size: 16px;
  }

  >>> .el-dialog__body {
    .unify {
      display: flex;
      align-items: center;
      margin-top: 20px;

      .el-input {
        margin: 0 10px;
      }

      &.set-nickname {
        justify-content: center;
      }

      &.set-mail {
        margin-left: 20px;
      }
    }

    .haidou {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap;

      .el-input {
        width: calc(100% - 170px);
        margin: 0 10px;
        margin-left: 0px;
      }

      .name {
        display: inline-block;
        width: 90px;
        text-align: right;
      }

      .val {
        margin-left: 10px;
      }
    }
  }

  >>> .el-table {
    .cell {
      padding: 0;
    }
  }

  >>> .cell {
    .column_1 {
      padding-left: 18px;
    }
  }

  .code-box {
    flex: 1;
    display: flex;
    justify-content: space-between;
  }
}

.haidou {
  .tips {
    display: inline-block;
    margin-left: 42px;

    span {
      font-size: 18px;
    }
  }

  .tip-num {
    font-size: 12px;
    color: #ff0000;
    margin-left: 100px;
  }

  .tip-notenough {
    font-size: 12px;
    color: #ff0000;
    margin-left: 50px;
  }
}

.cus-input {
  pointer-events: none;
  cursor: not-allowed;
  background-color: #f5f7fa;
}

.mark {
  color: #666;
  font-size: 14px;
  padding-left: 10px;
  width: calc(100% - 10px);
  margin: 20px 0;
}

.pagination {
  text-align: right;
}

.convert-success {
  display: flex;
  align-items: center;

  span {
    font-size: 18px;
    color: #333;
  }

  .icon {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: url('~@/assets/user/success.png') no-repeat;
    background-size: 100%;
    margin-right: 20px;
  }
}

.set-code {
  display: flex;
  align-items: center;

  span {
    width: 90px;
  }

  img {
    margin-left: 10px;
  }
}

.qrcode-box {
  position: relative;
  margin-bottom: 15px;

  .on-ser {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(250, 250, 250, 0.9);
  }

  .qr-info {
    width: 100%;
    height: 20px;
    line-height: 20px;
    background: #fff;
    position: absolute;
    bottom: -25px;
    left: 0;
    text-align: center;
  }
}

.query-acount {
  margin-top: 10px;
  margin-left: 280px;
}

.enterprise-bind {
  width: 100%;
  border: 1px solid #dfe6ec;
  box-sizing: border-box;
  padding: 0 20px;
  overflow: hidden;
  margin-bottom: 25px;
  font-family: 'Microsoft YaHei';
}

.up-box {
  display: flex;

  .uploader {
    width: 175px;
  }

  .uploader >>> .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    margin-top: 10px;
    background: #f9f9f9;
  }

  .uploader >>> .el-upload:hover {
    border-color: #409EFF;
  }

  .uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }

  .img {
    width: 120px;
    height: 120px;
    display: block;
  }

  .img-tips {
    color: #999;
  }

  .sub {
    color: #999;
    font-size: 12px;
    display: block;
    margin-right: 10px;

    p {
      margin: 0;
      height: 25px;
    }
  }
}

.agreement {
  padding: 0 20px 20px;
  line-height: 20px;

  p {
    margin: 5px 0;
  }
}

.subtitle {
  font-size: 12px;
  color: #9f9f9f;
  margin-left: 10px;
}

.toUpperCase >>> .el-input__inner {
  text-transform: uppercase !important;
}

.logout {
  padding: 0 10px
  p {
    color: #ed4040;
    font-size: 16px;
    margin-bottom: 10px;
  }

  &-title {
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 20px !important;
    color: #000 !important;
  }
}
.verify-code {
  display: flex;
  align-items: center;
}

.tit {
  position: absolute;
  width: 470px;
  background-color: #ff7777;
  text-align: center;
  height: 30px;
  line-height: 30px;
  color: #fff;
  font-size: 14px;
  padding: 0;
  margin: -30px 0 20px -20px;
}
</style>
