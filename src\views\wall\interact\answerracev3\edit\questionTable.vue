<template>
  <div>
    <div class="flex flex-a-c">
      <el-button v-if="isFixed" type="primary" @click="handleEditQuestion()">添加题目</el-button>
      <el-button type="primary" @click="showQuestionSelectorDialog = true">题库选择</el-button>
      <el-button v-if="isFixed" type="primary" @click="showImportantOther = true">其他轮次导入</el-button>
      <!-- <el-button v-if="isFixed" type="primary" @click="handleManageQuestion">管理题库</el-button> -->
    </div>
    <!-- 随机提 -->
    <div v-if="actConfig.chooseSubjectType === 'RANDOM'">
      <el-table :data="subjectContent" class="mrg-t-10" row-key="id" stripe>
        <el-table-column label="题库">
          <template v-slot="{ row }">
            <div>{{ selectedItemsInfo[row.value || row.id].name }}</div>
          </template>
        </el-table-column>
        <el-table-column label="题目数量">
          <template v-slot="{ row }">
            <div>{{ selectedItemsInfo[row.value || row.id].subjectCount }}</div>
          </template>
        </el-table-column>
        <el-table-column label="抽取数量">
          <template v-slot="{ row }">
            <el-input-number
              class="w-100"
              v-model.number="row.number"
              :min="0"
              :max="selectedItemsInfo[row.value || row.id].subjectCount"
              controls-position="right"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template v-slot="{ row }">
            <el-button class="f-red" type="text" @click="handleDelQuestion(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 选择题列表 -->
    <div v-else>
      <el-table v-loading="questionLoad" class="mrg-t-10" :data="quesAndOptArr" row-key="uuid" stripe ref="table" max-height="500px">
        <el-table-column label="排序" width="60" align="center">
          <template v-slot="{}">
            <div class="drag-handle">
              <img class="move" src="~@/assets/wall/interact/drag.png" />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="80"></el-table-column>
        <el-table-column prop="title" label="题目">
          <template v-slot="{ row }">
            <div class="flex flex-a-c">
              <div class="question-content">
                <el-popover v-if="row.subjectContent.resource" placement="top-start" trigger="hover">
                  <div class="title-img-big">
                    <img v-if="row.subjectContent.type === 'IMAGE'" :src="row.subjectContent.resource" alt="题目图片" />
                    <video v-if="row.subjectContent.type === 'VIDEO'" :src="row.subjectContent.resource" alt="题目视频" controls />
                    <hi-audio-player v-if="row.subjectContent.type === 'AUDIO'" :src="row.subjectContent.resource" alt="题目音频" />
                  </div>
                  <div slot="reference" class="title-img">
                    <img v-if="row.subjectContent.type === 'IMAGE'" :src="row.subjectContent.resource" alt="题目图片" />
                    <video v-if="row.subjectContent.type === 'VIDEO'" :src="row.subjectContent.resource" />
                    <div v-if="row.subjectContent.type === 'AUDIO'" class="audio-icon">
                      <img src="@/assets/wall/interact/teamanswer/sound.png" alt="" />
                    </div>
                  </div>
                </el-popover>
              </div>
              <p class="title-cont">{{ row.subjectContent.content }}</p>
            </div>
          </template>
        </el-table-column>
        <!-- 题目类型 -->
        <el-table-column label="题目类型" width="100">
          <template v-slot="{ row }">
            <span>{{ row.type === 'RADIO' ? '单选' : '多选' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="答案选项">
          <template v-slot="{ row }">
            <el-popover placement="top-start" width="300" trigger="hover">
              <div class="option-box flex flex-w-w">
                <div class="option-txt-pic" v-for="(item, i) in row.options" :key="i">
                  <p class="opt-index">{{ String.fromCharCode(65 + i) }}：</p>
                  <div>
                    <p v-if="item.title">{{ item.title }}</p>
                    <p v-if="item.img" class="pic"><img :src="item.img" /></p>
                  </div>
                </div>
              </div>
              <span class="correct-ans" slot="reference">{{ getRightCont(row.options) }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="注释"></el-table-column>
        <el-table-column label="操作">
          <template v-slot="{ row, index }">
            <a class="mrg-r-10" href="javascript:void(0)" @click="handleEditQuestion(row)">编辑</a>
            <el-button type="text" @click="handleDelQuestion(row, index)" :class="{ 'f-red': !disabled }">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 题库选择 -->
    <hi-question-selector
      :visible="showQuestionSelectorDialog"
      :actConfig="actConfig"
      @close="showQuestionSelectorDialog = false"
      @save="onQuestionSelectorSave"
    />

    <hi-question-form
      :visible="showQuestionFormDialog"
      :question="currentQuestion"
      @close="showQuestionFormDialog = false"
      @success="onQuestionSaved"
    />

    <hi-import-other v-if="showImportantOther" :actConfig="actConfig" @save="onImportOtherAct" @close="showImportantOther = false"></hi-import-other>
  </div>
</template>
<script>
import api from '@/api'
import { Hi } from '@/libs/common'
import Sortable from 'sortablejs'
import { mapActions, mapGetters, mapState } from 'vuex'
import HiQuestionForm from '../../common/questionBank/QuestionForm.vue'
import HiQuestionSelector from '../../common/questionBank/QuestionSelector.vue'
import HiImportOther from './importOther.vue'

export default {
  name: 'Answerracev3QuestionTable',
  components: { HiQuestionForm, HiQuestionSelector, HiImportOther },
  props: {
    actConfig: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      questionLoad: false,
      showQuestionFormDialog: false, // 题目表单
      showQuestionSelectorDialog: false, // 题库选择
      currentQuestion: {},
      // 题目列表
      quesAndOptArr: [],
      originalQuesAndOptArr: [],
      // 随机题目列表
      showImportantOther: false,

      // 随机题库
      subjectContent: [],
      // 选中项目的关联信息
      selectedItemsInfo: {},
    }
  },
  computed: {
    ...mapState('questionBank', ['subjectDifficultyList', 'subjectGroupList', 'questionList']),
    ...mapGetters({
      wallFlagObj: 'wall/getWallFlagObj',
    }),
    _wallFlag() {
      return this.$route.query.wallFlag
    },
    wall() {
      return this.wallFlagObj[this._wallFlag]
    },
    isFixed() {
      return this.actConfig.chooseSubjectType === 'FIXED'
    },
    isRandom() {
      return this.actConfig.chooseSubjectType === 'RANDOM'
    },
  },
  watch: {
    'actConfig.id': {
      async handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          await this.saveQuestionList()
        }
      },
    },
    subjectContent: {
      handler(newVal) {
        this.$emit('updateSubjectContent', newVal)
      },
      deep: true,
    },
  },
  methods: {
    ...mapActions('questionBank', ['initQuestionBank']),

    // 获取正确答案
    getRightCont(data) {
      const _data = typeof data === 'string' ? JSON.parse(data) : data
      return _data
        .map((item, index) => (item.rightAnswer === 'Y' ? String.fromCharCode(65 + index) : null))
        .filter(Boolean)
        .join('/')
    },

    async fetchSubjectList() {
      try {
        this.questionLoad = true
        if (!this.actConfig.id) return
        const res = await api.answerracev3subject.list({
          where: {
            wallId: this.wall.id,
            answerracev3Id: this.actConfig.id,
          },
          sort: { sort: 'asc' },
        })
        const _toObj = (arr) => {
          return arr
            .map((item) => {
              const obj = {
                ...item,
                subjectContent: JSON.parse(item.subjectContent),
                options: JSON.parse(item.options),
                uuid: item.id,
              }
              return Hi.Object.copy(obj)
            })
            .filter(Boolean)
        }
        this.quesAndOptArr = _toObj(res)
        this.originalQuesAndOptArr = _toObj(res)
      } catch (e) {
        console.error('获取题目列表失败:', e)
      } finally {
        this.questionLoad = false
      }
    },

    // 添加题目
    async saveQuestionList() {
      // 只有新增轮次的时候才会调用这个函数
      if (!this.actConfig.id) return

      const toAdd = (arr) => {
        return arr.map((item, index) => {
          const obj = {
            ...item,
            subjectContent: JSON.stringify(item.subjectContent),
            options: JSON.stringify(item.options),
            wallId: this.wall.id,
            answerracev3Id: this.actConfig.id,
          }

          if (item.uuid) {
            delete obj.uuid
          }

          return obj
        })
      }

      const dataList = toAdd(this.quesAndOptArr)
      if (dataList.length) {
        await api.answerracev3subject.batchAdd({
          wallId: this.wall.id,
          dataList,
        })
      }
      await this.fetchSubjectList()
    },

    // 编辑题目
    handleEditQuestion(row) {
      this.currentQuestion = row || {}
      this.showQuestionFormDialog = true
    },

    // 删除题目
    async handleDelQuestion(row, index) {
      await this.$confirm('删除后无法恢复，确定删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false,
      })

      if (this.isRandom) {
        this.subjectContent.splice(index, 1)
      } else {
        if (this.actConfig.id) {
          await api.answerracev3subject.delete({
            where: {
              id: row.id,
              wallId: this.wall.id,
              answerracev3Id: this.actConfig.id,
            },
          })
          this.$notify.success({ title: '成功', message: '题目删除成功!' })
          await this.fetchSubjectList()
        } else {
          this.quesAndOptArr.splice(index, 1)
        }
      }
    },

    // 添加题目后回调 更新题目列表
    async onQuestionSaved(question) {
      if (!this.actConfig.id) {
        const isUpdate = !!(question.uuid || question.id)

        if (isUpdate) {
          const index = this.quesAndOptArr.findIndex((i) => i.uuid === question.uuid)
          if (index > -1) {
            Object.assign(this.quesAndOptArr[index], question)
          }
        } else {
          this.quesAndOptArr.push({
            ...question,
            sort: this.quesAndOptArr.length + 1,
            uuid: Hi.Math.uuid(),
          })
        }
        return
      }

      const isUpdate = !!question.id

      const toSaveData = (question) => {
        const _saveData = {
          ...question,
          wallId: this.wall.id,
          answerracev3Id: this.actConfig.id,
          subjectContent: JSON.stringify(question.subjectContent),
          options: JSON.stringify(question.options),
          sort: this.quesAndOptArr.length + 1,
        }
        if (question.uuid) {
          delete _saveData.uuid
        }
        return _saveData
      }

      const _update = async () => {
        const d1 = toSaveData(this.originalQuesAndOptArr.find((i) => i.id === question.id))
        const d2 = toSaveData(question)
        const update = Hi.Object.difference(d1, d2, { deep: true })
        if (update) {
          await api.answerracev3subject.update({
            where: {
              id: question.id,
              wallId: this.wall.id,
              answerracev3Id: this.actConfig.id,
            },
            update,
          })
        }
      }

      const _add = async () => {
        const data = toSaveData(question)
        await api.answerracev3subject.add(data)
      }

      const save = isUpdate ? _update : _add

      await save()
      await this.fetchSubjectList()
      this.$notify.success({ title: '成功', message: isUpdate ? '题目更新成功!' : '题目新增成功!' })
    },

    // 题库选择保存
    async onQuestionSelectorSave(selectedList) {
      if (!selectedList || !selectedList.length) {
        this.$notify.warning({ title: '提示', message: '请至少选择一道题目' })
        return
      }

      try {
        // 固定题库
        if (this.actConfig.chooseSubjectType === 'FIXED') {
          if (!this.actConfig.id) {
            let dataList = Hi.Object.copy(selectedList)
            dataList.forEach((item) => {
              const difficulty = this.subjectDifficultyList.find((i) => i.id === item.difficultyId)
              if (difficulty) {
                item.rightScore = difficulty.rightScore
                item.wrongScore = difficulty.wrongScore
                item.answerTime = difficulty.answerTime
              }
            })
            this.quesAndOptArr.push(...dataList)
          } else {
            const dataList = selectedList.map((item) => {
              const obj = {
                ...item,
                subjectContent: JSON.stringify(item.subjectContent),
                options: JSON.stringify(item.options),
                wallId: this.wall.id,
                answerracev3Id: this.actConfig.id,
                ...(item.uuid ? {} : { uuid: undefined }), // 如果有uuid属性则删除
                rightScore: 0,
                wrongScore: 0,
                answerTime: 0,
              }
              // 获取难度数据
              const difficulty = this.subjectDifficultyList.find((i) => i.id === item.difficultyId)
              if (difficulty) {
                obj.rightScore = difficulty.rightScore
                obj.wrongScore = difficulty.wrongScore
                obj.answerTime = difficulty.answerTime
              }

              return obj
            })

            await api.answerracev3subject.batchAdd({
              wallId: this.wall.id,
              dataList,
            })
            await this.fetchSubjectList()
            this.$notify.success({ title: '成功', message: '题目选择成功!' })
          }
        } else {
          const isType = selectedList.some((item) => item.type === 'TYPE')

          // 查询并显示关联信息
          // 改造为扁平化数组结构
          let arr = selectedList.map((item) => {
            let obj = {
              mode: item.type.toLowerCase(),
              number: 0,
            }
            if (!isType) {
              obj.id = item.id
            } else {
              obj.value = item.id
            }
            return obj
          })
          // 如果已经存在了就不添加
          arr = arr.filter((item) => {
            // 判断id或value是否已存在于subjectContent中
            if (item.id) {
              return !this.subjectContent.some((sc) => sc.id === item.id)
            } else if (item.value) {
              return !this.subjectContent.some((sc) => sc.value === item.value)
            }
            return true
          })

          this.subjectContent.push(...arr)

          await this.fetchSelectedItemsInfo(this.subjectContent)
        }

        this.showQuestionSelectorDialog = false
      } catch (error) {
        console.error('保存题目失败:', error)
        this.$notify.error({ title: '错误', message: error.msg || '题目选择失败，请重试' })
      }
    },

    async onImportOtherAct(value) {
      if (!this.actConfig.id) {
        let dataList = Hi.Object.copy(value)
        dataList.forEach((item) => {
          item.options = JSON.parse(item.options)
        })
        this.quesAndOptArr.push(...dataList)
      } else {
        const dataList = value.map((item) => {
          let obj = {
            ...item,
            subjectContent: JSON.stringify(item.subjectContent),
          }
          // 删除不需要的属性，避免数据冲突
          const fieldsToDelete = ['id', 'deleteTag', 'createDate', 'updateDate', 'sort']
          fieldsToDelete.forEach((field) => {
            if (obj.hasOwnProperty(field)) {
              delete obj[field]
            }
          })
          return obj
        })
        try {
          await api.answerracev3subject.batchAdd({
            wallId: this.wall.id,
            dataList,
          })
          await this.fetchSubjectList()
          this.$notify.success({ title: '成功', message: '题目导入成功!' })
        } catch (error) {
          console.error('导入题目失败:', error)
          this.$notify.error({ title: '错误', message: error.msg || '题目导入失败，请重试' })
        }
      }
    },

    // 查询选中项目的关联信息
    async fetchSelectedItemsInfo(data) {
      try {
        // 适配扁平化数组结构
        data.forEach((item) => {
          if (item.mode === 'difficulty') {
            const difficultyItem = this.subjectDifficultyList.find((d) => d.id === item.id)
            if (difficultyItem) {
              this.selectedItemsInfo[item.id] = {
                name: difficultyItem.name,
                subjectCount: difficultyItem.subjectCount,
              }
            }
          }

          if (item.mode === 'group') {
            const groupItem = this.subjectGroupList.find((g) => g.id === item.id)
            if (groupItem) {
              this.selectedItemsInfo[item.id] = {
                name: groupItem.name,
                subjectCount: groupItem.subjectCount,
              }
            }
          }

          if (item.mode === 'type') {
            this.selectedItemsInfo[item.value] = {
              name: item.value === 'RADIO' ? '单选题' : '多选题',
              subjectCount: this.questionList.filter((i) => i.type === item.value).length,
            }
          }
        })
      } catch (error) {
        console.error('查询关联信息失败:', error)
        this.$notify.error({ title: '错误', message: '查询关联信息失败，请重试' })
      }
    },

    // 拖拽排序
    initSortable() {
      this.$nextTick(() => {
        const el = this.$refs.table?.$el?.querySelector('.el-table__body-wrapper > table > tbody')
        if (el) {
          new Sortable(el, {
            handle: '.drag-handle',
            ghostClass: 'sortable-ghost',
            onEnd: async (evt) => {
              const { oldIndex, newIndex } = evt
              if (oldIndex === newIndex) return

              // 更新本地数据
              const targetRow = this.quesAndOptArr.splice(oldIndex, 1)[0]
              this.quesAndOptArr.splice(newIndex, 0, targetRow)

              if (this.actConfig.id) {
                let sortData = []
                this.quesAndOptArr.forEach((item, index) => {
                  item.sort = index
                  let sort = {
                    type: 'update',
                    data: {
                      where: { id: item.id },
                      update: { sort: index },
                    },
                  }
                  sortData.push(sort)
                })
                await api.answerracev3subject.sort(sortData)
                await this.fetchSubjectList()
              } else {
                // 更新排序数据
                this.quesAndOptArr.forEach((item, index) => {
                  item.sort = index
                })
              }
            },
          })
        }
      })
    },
  },
  async mounted() {
    await this.initSortable()
    await this.fetchSubjectList()
    await this.initQuestionBank()

    if (this.isRandom) {
      // 直接使用扁平化结构
      const subjectContentData = this.actConfig.subjectContent
      if (subjectContentData && Array.isArray(subjectContentData)) {
        this.subjectContent = Hi.Object.copy(subjectContentData)
      } else {
        this.subjectContent = []
      }
      await this.fetchSelectedItemsInfo(this.subjectContent)
    }
  },
}
</script>
<style scoped lang="scss">
.f-red {
  color: red;
}
.title-img {
  width: 40px;
  height: 40px;
}

.title-img-big {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  img,
  video,
  audio {
    max-width: 100%;
    max-height: 150px;
    border-radius: 4px;
    object-fit: contain;
  }

  audio {
    width: 100%;
    height: 40px;
  }
}
img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
}
.title-cont {
  height: min-content;
  margin-left: 5px;
}
:deep(.cell) {
  font-size: 14px;
}
.option-box {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  .option-txt {
    width: calc(50% - 10px);
    line-height: 1.5;
    margin: 5px 0px;
  }
  .option-txt-pic {
    width: 110px;
    margin: 10px 15px;
    display: flex;
    .pic {
      width: 80px;
      height: 80px;
    }
  }
  .opt-index {
    width: 20px;
    text-align: center;
    font-weight: 600;
  }
}

.drag-handle {
  cursor: move;
  display: flex;
  justify-content: center;

  .move {
    padding: 10px;
  }
}

.correct-ans {
  font-weight: bold;
  color: #409eff;
  cursor: pointer;
}

.title-img {
  width: 40px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  margin: 5px;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .audio-icon {
    width: 30px;
    margin-top: 5px;
  }
}
</style>
