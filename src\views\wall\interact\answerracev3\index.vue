<template>
  <div v-loading="loading" element-loading-text="拼命加载中">
    <hi-answerrace-manage v-if="isManage" :config="config" @toList="isManage = false"></hi-answerrace-manage>
    <!-- 编辑视图 -->
    <hi-answerrace-edit
      v-else-if="isEdit"
      @loaded="loaded"
      :editId="editObj.id"
      :themeId="editObj.themeId"
      :config="config"
      @list="handleReturnToList"
      @updateConfig="readConfig"
    ></hi-answerrace-edit>
    <!-- 列表视图 -->
    <hi-answerrace-list
      v-else-if="config.id"
      :config="config"
      @loaded="loaded"
      @edit="handleEdit"
      @updateConfig="readConfig"
      @toManage="isManage = true"
    ></hi-answerrace-list>
  </div>
</template>
<script>
import api from '@/api'
import { wallSetMixin } from '@/libs/mixins.js'
import HiAnswerraceEdit from './edit/index.vue'
import HiAnswerraceList from './list.vue'
import HiAnswerraceManage from './manage.vue'

export default {
  name: 'answerracev3',
  inject: ['wall'],
  mixins: [wallSetMixin],
  components: {
    HiAnswerraceEdit,
    HiAnswerraceList,
    HiAnswerraceManage,
  },
  data() {
    return {
      config: {},
      loading: false,
      isManage: false,
    }
  },
  computed: {
    isEdit() {
      return this.editObj !== null
    },
  },
  methods: {
    handleEdit(v) {
      this.setEdit(v)
    },
    handleReturnToList() {
      this.setList()
      this.readConfig()
    },
    async readConfig() {
      this.loading = true
      try {
        if (!this.wall?.id) {
          console.warn('无法读取配置，缺少 wall.id。')
          return
        }
        const result = await api.answerracev3config.read({
          where: { wallId: this.wall.id },
        })

        if (!result) {
          // 没有配置 就去创建
          await api.answerracev3config.add({
            wallId: this.wall.id,
          })
          this.config = await api.answerracev3config.read({
            where: { wallId: this.wall.id },
          })
        } else {
          this.config = result
        }
      } catch (err) {
        console.error('读取答题配置失败:', err)
        this.$notify.error({
          title: '配置加载失败',
          message: err.msg || '获取答题配置失败，请稍后重试！',
        })
        this.config = {}
      } finally {
        this.loaded()
      }
    },
    loaded() {
      if (typeof this.$options.mixins.find((m) => m.methods?.loaded)?.methods.loaded === 'function') {
        this.$options.mixins.find((m) => m.methods?.loaded).methods.loaded.call(this)
      } else {
        this.loading = false
      }
    },
  },
  async mounted() {
    await this.readConfig()
    if (this.$route.query.mode === 'manage') {
      this.isManage = true
    }
  },
}
</script>
