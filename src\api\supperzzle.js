import api from './api'

export default {
  insert: postData => api.fetchBaseData('/pro/hxc/prosupperzzle/insert.htm', postData),
  delete: postData => api.fetchBaseData('/pro/hxc/prosupperzzle/delete.htm', postData),
  read: postData => api.fetchBaseData('/pro/hxc/prosupperzzle/read.htm', postData),
  update: postData => api.fetchBaseData('/pro/hxc/prosupperzzle/update.htm', postData),
  page: postData => api.fetchBaseData('/pro/hxc/prosupperzzle/page.htm', postData),
  list: postData => api.fetchBaseData('/pro/hxc/prosupperzzle/list.htm', postData),
  add: postData => api.fetchBaseData('/pro/hxc/prosupperzzle/add.htm', postData),
  editcnt: postData => api.fetchBaseData('/pro/hxc/prosupperzzleregedit/cnt.htm', postData),
  matchcnt: postData => api.fetchBaseData('/pro/hxc/prosupperzzlematch/cnt.htm', postData),
}
