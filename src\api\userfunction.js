import api from './api'
export default {
  // 消耗单个功能场次
  unlock: postData => api.fetchBaseData('/pro/hxc/prouserfunction/unlock.htm', postData),
  // 个人增值功能次数列表
  list: postData => api.fetchBaseData('/pro/hxc/prouserfunction/list.htm', postData),
  // 个人增值功能消耗分页
  page: postData => api.fetchBaseData('/pro/hxc/prouserfunctiontrade/page.htm', postData),
   // 个人增值功能次数，用key查
  read: postData => api.fetchBaseData('/pro/hxc/prouserfunction/read.htm', postData),
}
