<template>
  <div class="relative">
    <hi-independent :config="config" @change="(v) => (showThumbnail = v)" />
    <img v-if="showThumbnail" class="thumbnail" src="@/assets/wall/interact/independent/thumbnail4.png" alt="" />
    <hi-iframe-container v-else :iframeUrl="iframeUrl"></hi-iframe-container>
  </div>
</template>

<script>
import api from '@/api'
import { env } from '@/libs/common'
import HiIframeContainer from '@/views/common/iframe-container.vue'
import HiIndependent from '@/views/wall/interact/common/independent.vue'

export default {
  name: 'WallEqxiu',
  inject: ['wall'],
  components: {
    HiIframeContainer,
    HiIndependent,
  },
  data() {
    return {
      showThumbnail: true,
      config: {},
    }
  },
  computed: {
    iframeUrl() {
      const domain = env.env === 'dev' ? env.proxy + '/' : env.wwwdomain
      return `${domain}next/admin/wall/padsign?wallFlag=${this.wall.wallFlag}&wallId=${this.wall.id}`
    },
  },
  methods: {
    async fetchConfig() {
      try {
        const res = await api.padsignconfig.read({ where: { wallId: this.wall.id } })
        this.config = res
      } catch (err) {
        console.error(err)
        this.$notify.error({
          title: '错误',
          message: err?.msg || '加载配置失败，请刷新重试',
        })
      }
    },
  },
  async created() {
    await this.fetchConfig()
  },
}
</script>
