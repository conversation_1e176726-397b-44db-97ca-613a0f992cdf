import api from './api'
export default {
  add: v => api.fetchBaseData('/pro/hxc/properformance/add.htm', v),
  read: v => api.fetchBaseData('/pro/hxc/properformance/read.htm', v),
  update: v => api.fetchBaseData('/pro/hxc/properformance/update.htm', v),
  list: v => api.fetchBaseData('/pro/hxc/properformance/list.htm', v),
  delete: v => api.fetchBaseData('/pro/hxc/properformance/delete.htm', v),
  page: v => api.fetchBaseData('/pro/hxc/properformance/page.htm', v),
}
